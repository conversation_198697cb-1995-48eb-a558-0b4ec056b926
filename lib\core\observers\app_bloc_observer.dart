import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../utils/error_filter.dart';

/// مراقب BLoC مخصص لتتبع جميع الأحداث والحالات والأخطاء
class AppBlocObserver extends BlocObserver {
  /// عداد الأحداث
  static int _eventCount = 0;

  /// عداد تغييرات الحالة
  static int _transitionCount = 0;

  /// عداد الأخطاء
  static int _errorCount = 0;

  @override
  void onCreate(BlocBase bloc) {
    super.onCreate(bloc);
    _logInfo('🏗️ BLoC Created', {
      'type': bloc.runtimeType.toString(),
      'hashCode': bloc.hashCode.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  @override
  void onEvent(BlocBase bloc, Object? event) {
    if (bloc is Bloc) {
      super.onEvent(bloc, event);
    }
    _eventCount++;

    _logInfo('📨 Event Added', {
      'bloc': bloc.runtimeType.toString(),
      'event': event.runtimeType.toString(),
      'eventData': event.toString(),
      'eventCount': _eventCount.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  @override
  void onTransition(BlocBase bloc, Transition transition) {
    if (bloc is Bloc) {
      super.onTransition(bloc, transition);
    }
    _transitionCount++;

    _logInfo('🔄 State Transition', {
      'bloc': bloc.runtimeType.toString(),
      'event': transition.event.runtimeType.toString(),
      'currentState': transition.currentState.runtimeType.toString(),
      'nextState': transition.nextState.runtimeType.toString(),
      'transitionCount': _transitionCount.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });

    // تسجيل تفاصيل إضافية للحالات المهمة
    _logTransitionDetails(bloc, transition);
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);

    _logInfo('📝 State Changed', {
      'bloc': bloc.runtimeType.toString(),
      'currentState': change.currentState.runtimeType.toString(),
      'nextState': change.nextState.runtimeType.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    // فحص ما إذا كان الخطأ يجب تجاهله
    if (ErrorFilter.shouldIgnoreError(error)) {
      // تسجيل مبسط للأخطاء المعروفة
      if (kDebugMode) {
        print('\x1B[33m⚠️ ${ErrorFilter.getErrorType(error)} Error (Ignored): ${error.runtimeType}\x1B[0m');
      }
      return;
    }

    super.onError(bloc, error, stackTrace);
    _errorCount++;

    _logError('❌ BLoC Error', {
      'bloc': bloc.runtimeType.toString(),
      'error': ErrorFilter.cleanErrorMessage(error),
      'errorType': ErrorFilter.getErrorType(error),
      'errorCount': _errorCount.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    }, stackTrace);

    // إرسال تقرير الخطأ للمراقبة
    _reportError(bloc, error, stackTrace);
  }

  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);

    _logInfo('🗑️ BLoC Closed', {
      'type': bloc.runtimeType.toString(),
      'hashCode': bloc.hashCode.toString(),
      'timestamp': DateTime.now().toIso8601String(),
    });
  }

  /// تسجيل معلومات عامة
  void _logInfo(String title, Map<String, String> data) {
    if (kDebugMode) {
      final message = _formatLogMessage(title, data);
      developer.log(
        message,
        name: 'BlocObserver',
        level: 800, // Info level
      );

      // طباعة ملونة في الكونسول
      print('\x1B[36m$message\x1B[0m'); // Cyan color
    }
  }

  /// تسجيل الأخطاء
  void _logError(String title, Map<String, String> data, StackTrace stackTrace) {
    if (kDebugMode) {
      final message = _formatLogMessage(title, data);
      developer.log(
        message,
        name: 'BlocObserver',
        level: 1000, // Error level
        error: data['error'],
        stackTrace: stackTrace,
      );

      // طباعة ملونة في الكونسول
      print('\x1B[31m$message\x1B[0m'); // Red color
      print('\x1B[31mStackTrace: $stackTrace\x1B[0m');
    }
  }

  /// تنسيق رسالة السجل
  String _formatLogMessage(String title, Map<String, String> data) {
    final buffer = StringBuffer();
    buffer.writeln('═══════════════════════════════════════');
    buffer.writeln(title);
    buffer.writeln('═══════════════════════════════════════');

    data.forEach((key, value) {
      buffer.writeln('$key: $value');
    });

    buffer.writeln('═══════════════════════════════════════');
    return buffer.toString();
  }

  /// تسجيل تفاصيل إضافية للانتقالات
  void _logTransitionDetails(BlocBase bloc, Transition transition) {
    final blocName = bloc.runtimeType.toString();
    final eventName = transition.event.runtimeType.toString();
    final currentStateName = transition.currentState.runtimeType.toString();
    final nextStateName = transition.nextState.runtimeType.toString();

    // تسجيل تفاصيل خاصة لكل نوع BLoC
    switch (blocName) {
      case 'AuthBloc':
        _logAuthTransition(eventName, currentStateName, nextStateName);
        break;
      case 'AppointmentBloc':
        _logAppointmentTransition(eventName, currentStateName, nextStateName);
        break;
      case 'ProductBloc':
        _logProductTransition(eventName, currentStateName, nextStateName);
        break;
      case 'ArticleBloc':
        _logArticleTransition(eventName, currentStateName, nextStateName);
        break;
    }
  }

  /// تسجيل انتقالات المصادقة
  void _logAuthTransition(String event, String currentState, String nextState) {
    if (nextState == 'AuthSuccess') {
      _logInfo('🔐 User Authenticated Successfully', {
        'event': event,
        'previousState': currentState,
      });
    } else if (nextState == 'AuthFailure') {
      _logInfo('🚫 Authentication Failed', {
        'event': event,
        'previousState': currentState,
      });
    }
  }

  /// تسجيل انتقالات المواعيد
  void _logAppointmentTransition(String event, String currentState, String nextState) {
    if (nextState == 'AppointmentCreated') {
      _logInfo('📅 Appointment Created', {
        'event': event,
        'previousState': currentState,
      });
    } else if (nextState == 'AppointmentCancelled') {
      _logInfo('❌ Appointment Cancelled', {
        'event': event,
        'previousState': currentState,
      });
    }
  }

  /// تسجيل انتقالات المنتجات
  void _logProductTransition(String event, String currentState, String nextState) {
    if (nextState == 'ProductLoaded') {
      _logInfo('🛍️ Products Loaded', {
        'event': event,
        'previousState': currentState,
      });
    } else if (nextState == 'ProductError') {
      _logInfo('⚠️ Product Loading Error', {
        'event': event,
        'previousState': currentState,
      });
    }
  }

  /// تسجيل انتقالات المقالات
  void _logArticleTransition(String event, String currentState, String nextState) {
    if (nextState == 'ArticleLoaded') {
      _logInfo('📖 Articles Loaded', {
        'event': event,
        'previousState': currentState,
      });
    } else if (nextState == 'ArticleError') {
      _logInfo('⚠️ Article Loading Error', {
        'event': event,
        'previousState': currentState,
      });
    }
  }

  /// إرسال تقرير الخطأ للمراقبة
  void _reportError(BlocBase bloc, Object error, StackTrace stackTrace) {
    // يمكن إضافة تكامل مع خدمات مراقبة الأخطاء مثل:
    // - Firebase Crashlytics
    // - Sentry
    // - Bugsnag

    if (kDebugMode) {
      _logInfo('📊 Error Report Generated', {
        'bloc': bloc.runtimeType.toString(),
        'errorType': error.runtimeType.toString(),
        'totalErrors': _errorCount.toString(),
      });
    }
  }

  /// الحصول على إحصائيات BLoC
  static Map<String, int> getStatistics() {
    return {
      'totalEvents': _eventCount,
      'totalTransitions': _transitionCount,
      'totalErrors': _errorCount,
    };
  }

  /// إعادة تعيين الإحصائيات
  static void resetStatistics() {
    _eventCount = 0;
    _transitionCount = 0;
    _errorCount = 0;
  }

  /// طباعة ملخص الإحصائيات
  static void printStatistics() {
    final stats = getStatistics();

    if (kDebugMode) {
      print('\x1B[33m'); // Yellow color
      print('═══════════════════════════════════════');
      print('📊 BLoC Observer Statistics');
      print('═══════════════════════════════════════');
      print('Total Events: ${stats['totalEvents']}');
      print('Total Transitions: ${stats['totalTransitions']}');
      print('Total Errors: ${stats['totalErrors']}');
      print('═══════════════════════════════════════');
      print('\x1B[0m'); // Reset color
    }

    // تسجيل في developer log أيضاً
    developer.log(
      'BLoC Statistics - Events: ${stats['totalEvents']}, '
      'Transitions: ${stats['totalTransitions']}, '
      'Errors: ${stats['totalErrors']}',
      name: 'BlocObserver',
    );
  }
}
