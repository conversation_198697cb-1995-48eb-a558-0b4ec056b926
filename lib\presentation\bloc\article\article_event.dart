part of 'article_bloc.dart';

/// أحداث المقالات
abstract class ArticleEvent extends Equatable {
  const ArticleEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل المقالات
class LoadArticles extends ArticleEvent {
  final String? category;
  final bool? isPublished;
  final String? searchTerm;
  final int? limit;
  final int? offset;
  final String? sortBy;
  final bool ascending;

  const LoadArticles({
    this.category,
    this.isPublished,
    this.searchTerm,
    this.limit,
    this.offset,
    this.sortBy,
    this.ascending = false,
  });

  @override
  List<Object?> get props => [category, isPublished, searchTerm, limit, offset, sortBy, ascending];
}

/// تحميل المقالات المميزة
class LoadFeaturedArticles extends ArticleEvent {
  final int? limit;

  const LoadFeaturedArticles({this.limit});

  @override
  List<Object?> get props => [limit];
}

/// تحميل المقالات حسب الفئة
class LoadArticlesByCategory extends ArticleEvent {
  final String category;
  final int? limit;

  const LoadArticlesByCategory({
    required this.category,
    this.limit,
  });

  @override
  List<Object?> get props => [category, limit];
}

/// تحميل فئات المقالات
class LoadArticleCategories extends ArticleEvent {
  const LoadArticleCategories();
}

/// تحميل فئات المقالات مع العدد
class LoadCategoriesWithCount extends ArticleEvent {
  const LoadCategoriesWithCount();
}

/// البحث في المقالات
class SearchArticles extends ArticleEvent {
  final String searchTerm;
  final String? category;
  final String? author;
  final int? limit;

  const SearchArticles({
    required this.searchTerm,
    this.category,
    this.author,
    this.limit,
  });

  @override
  List<Object?> get props => [searchTerm, category, author, limit];
}

/// تحميل مقال بواسطة ID
class LoadArticleById extends ArticleEvent {
  final String articleId;

  const LoadArticleById({required this.articleId});

  @override
  List<Object?> get props => [articleId];
}

/// زيادة عدد المشاهدات
class IncrementArticleViews extends ArticleEvent {
  final String articleId;

  const IncrementArticleViews({required this.articleId});

  @override
  List<Object?> get props => [articleId];
}

/// إعجاب/إلغاء إعجاب بمقال
class ToggleArticleLike extends ArticleEvent {
  final String articleId;
  final bool isLiked;

  const ToggleArticleLike({
    required this.articleId,
    required this.isLiked,
  });

  @override
  List<Object?> get props => [articleId, isLiked];
}

/// تحميل إحصائيات المقالات
class LoadArticleStats extends ArticleEvent {
  const LoadArticleStats();
}

/// تحديث المقالات
class RefreshArticles extends ArticleEvent {
  final String? category;
  final bool? isPublished;
  final String? searchTerm;
  final int? limit;
  final int? offset;
  final String? sortBy;
  final bool ascending;

  const RefreshArticles({
    this.category,
    this.isPublished,
    this.searchTerm,
    this.limit,
    this.offset,
    this.sortBy,
    this.ascending = false,
  });

  @override
  List<Object?> get props => [category, isPublished, searchTerm, limit, offset, sortBy, ascending];
}
