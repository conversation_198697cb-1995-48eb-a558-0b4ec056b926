class ProductModel {
  final String id;
  final String name;
  final String? description;
  final String? categoryId;
  final String? imageUrl;
  final bool isActive;
  final int stockQuantity;
  final String? sku;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final double? originalPrice;
  final double? sellingPrice;
  final String? productCode;
  final double discountPercentage;
  final int stock;
  final List<String> images;

  ProductModel({
    required this.id,
    required this.name,
    this.description,
    this.categoryId,
    this.imageUrl,
    this.isActive = true,
    this.stockQuantity = 0,
    this.sku,
    this.createdAt,
    this.updatedAt,
    this.originalPrice,
    this.sellingPrice,
    this.productCode,
    this.discountPercentage = 0.0,
    this.stock = 0,
    this.images = const [],
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    List<String> imagesList = [];
    if (json['images'] != null) {
      if (json['images'] is List) {
        imagesList = List<String>.from(json['images']);
      }
    }

    return ProductModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      categoryId: json['category_id'],
      imageUrl: json['image_url'],
      isActive: json['is_active'] ?? true,
      stockQuantity: json['stock_quantity'] ?? 0,
      sku: json['sku'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      originalPrice: json['original_price'] != null 
          ? double.tryParse(json['original_price'].toString()) 
          : null,
      sellingPrice: json['selling_price'] != null 
          ? double.tryParse(json['selling_price'].toString()) 
          : null,
      productCode: json['product_code'],
      discountPercentage: double.tryParse(json['discount_percentage'].toString()) ?? 0.0,
      stock: json['stock'] ?? 0,
      images: imagesList,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'category_id': categoryId,
      'image_url': imageUrl,
      'is_active': isActive,
      'stock_quantity': stockQuantity,
      'sku': sku,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'original_price': originalPrice,
      'selling_price': sellingPrice,
      'product_code': productCode,
      'discount_percentage': discountPercentage,
      'stock': stock,
      'images': images,
    };
  }

  ProductModel copyWith({
    String? id,
    String? name,
    String? description,
    String? categoryId,
    String? imageUrl,
    bool? isActive,
    int? stockQuantity,
    String? sku,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? originalPrice,
    double? sellingPrice,
    String? productCode,
    double? discountPercentage,
    int? stock,
    List<String>? images,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      categoryId: categoryId ?? this.categoryId,
      imageUrl: imageUrl ?? this.imageUrl,
      isActive: isActive ?? this.isActive,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      sku: sku ?? this.sku,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      originalPrice: originalPrice ?? this.originalPrice,
      sellingPrice: sellingPrice ?? this.sellingPrice,
      productCode: productCode ?? this.productCode,
      discountPercentage: discountPercentage ?? this.discountPercentage,
      stock: stock ?? this.stock,
      images: images ?? this.images,
    );
  }

  // Helper methods
  bool get hasDescription => description != null && description!.isNotEmpty;
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasImages => images.isNotEmpty;
  bool get hasCategory => categoryId != null && categoryId!.isNotEmpty;
  bool get hasSku => sku != null && sku!.isNotEmpty;
  bool get hasProductCode => productCode != null && productCode!.isNotEmpty;
  bool get hasDiscount => discountPercentage > 0;
  bool get hasOriginalPrice => originalPrice != null && originalPrice! > 0;
  bool get hasSellingPrice => sellingPrice != null && sellingPrice! > 0;
  
  String get displayName => name.isNotEmpty ? name : 'منتج غير محدد';
  String get displaySku => sku ?? productCode ?? 'غير محدد';
  String get displayProductCode => productCode ?? sku ?? 'غير محدد';
  
  // Stock helpers
  bool get isInStock => (stockQuantity > 0) || (stock > 0);
  bool get isOutOfStock => !isInStock;
  bool get isLowStock => totalStock <= 5 && totalStock > 0;
  
  int get totalStock => stockQuantity + stock;
  
  String get stockStatus {
    if (isOutOfStock) return 'نفد المخزون';
    if (isLowStock) return 'مخزون منخفض';
    return 'متوفر';
  }
  
  String get stockText => '$totalStock ${totalStock == 1 ? 'قطعة' : 'قطع'}';
  
  // Price helpers
  double get currentPrice => sellingPrice ?? originalPrice ?? 0.0;
  double get discountedPrice {
    if (!hasDiscount) return currentPrice;
    return currentPrice * (1 - discountPercentage / 100);
  }
  
  String get formattedCurrentPrice => '${currentPrice.toStringAsFixed(2)} جنيه';
  String get formattedDiscountedPrice => '${discountedPrice.toStringAsFixed(2)} جنيه';
  String get formattedOriginalPrice => originalPrice != null ? '${originalPrice!.toStringAsFixed(2)} جنيه' : 'غير محدد';
  String get formattedSellingPrice => sellingPrice != null ? '${sellingPrice!.toStringAsFixed(2)} جنيه' : 'غير محدد';
  
  String get discountText => hasDiscount ? '${discountPercentage.toStringAsFixed(1)}% خصم' : '';
  
  // Image helpers
  String get primaryImage => hasImage ? imageUrl! : (hasImages ? images.first : '');
  List<String> get allImages {
    List<String> allImagesList = [];
    if (hasImage) allImagesList.add(imageUrl!);
    allImagesList.addAll(images);
    return allImagesList.toSet().toList(); // Remove duplicates
  }
  
  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }
  
  String get statusText => isActive ? 'نشط' : 'غير نشط';
  
  @override
  String toString() {
    return 'ProductModel(id: $id, name: $name, price: $formattedCurrentPrice, stock: $stockText)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
