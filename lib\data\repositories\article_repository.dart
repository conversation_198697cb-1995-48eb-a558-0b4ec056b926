import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/article.dart';
import '../../core/errors/failures.dart';

/// Repository للتعامل مع بيانات المقالات في Supabase
class ArticleRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// الحصول على جميع المقالات المنشورة
  Future<List<Article>> getAllArticles({
    String? categoryId,
    String? searchTerm,
    int? limit,
    int? offset,
    String? sortBy,
    bool ascending = false,
  }) async {
    try {
      var query = _supabase
          .from('articles')
          .select('''
            id,
            title,
            content,
            author,
            image_url,
            pdf_url,
            reference,
            is_published,
            is_featured,
            created_at,
            updated_at,
            category_id,
            article_categories!inner(name)
          ''')
          .eq('is_published', true);

      // تطبيق الفلاتر
      if (categoryId != null && categoryId.isNotEmpty && categoryId != 'all') {
        query = query.eq('category_id', categoryId);
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        query = query.or(
          'title.ilike.%$searchTerm%,content.ilike.%$searchTerm%,author.ilike.%$searchTerm%',
        );
      }

      // الترتيب
      final orderBy = sortBy ?? 'created_at';
      var orderedQuery = query.order(orderBy, ascending: ascending);

      // التصفح
      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await orderedQuery;

      return (response as List)
          .map(
            (json) => Article.fromJson({
              ...json,
              'category_name': json['article_categories']?['name'],
            }),
          )
          .toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب المقالات: ${e.toString()}');
    }
  }

  /// الحصول على مقال بواسطة ID
  Future<Article?> getArticleById(String articleId) async {
    try {
      final response =
          await _supabase
              .from('articles')
              .select()
              .eq('id', articleId)
              .maybeSingle();

      if (response == null) return null;

      return Article.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب المقال: ${e.toString()}');
    }
  }

  /// الحصول على المقالات المميزة
  Future<List<Article>> getFeaturedArticles({int? limit}) async {
    try {
      var query = _supabase
          .from('articles')
          .select()
          .eq('is_featured', true)
          .eq('is_published', true)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      return response.map((json) => Article.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(
        message: 'فشل في جلب المقالات المميزة: ${e.toString()}',
      );
    }
  }

  /// الحصول على المقالات حسب الفئة
  Future<List<Article>> getArticlesByCategory(
    String category, {
    int? limit,
  }) async {
    try {
      var query = _supabase
          .from('articles')
          .select()
          .eq('category', category)
          .eq('is_published', true)
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      final response = await query;

      return response.map((json) => Article.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب مقالات الفئة: ${e.toString()}');
    }
  }

  /// الحصول على فئات المقالات
  Future<List<ArticleCategory>> getArticleCategories() async {
    try {
      final response = await _supabase
          .from('article_categories')
          .select('*')
          .eq('is_active', true)
          .order('sort_order', ascending: true);

      return (response as List)
          .map((json) => ArticleCategory.fromJson(json))
          .toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب فئات المقالات: ${e.toString()}');
    }
  }

  /// الحصول على فئات المقالات مع عدد المقالات لكل فئة
  Future<List<Map<String, dynamic>>> getCategoriesWithCount() async {
    try {
      // الحصول على الفئات
      final categoriesResponse = await _supabase
          .from('article_categories')
          .select('*')
          .eq('is_active', true)
          .order('sort_order', ascending: true);

      List<Map<String, dynamic>> categoriesWithCount = [];

      // إضافة خيار "الكل"
      final totalArticlesResponse = await _supabase
          .from('articles')
          .select('id')
          .eq('is_published', true);

      categoriesWithCount.add({
        'id': null,
        'name': 'الكل',
        'count': totalArticlesResponse.length,
      });

      // إضافة الفئات مع العدد
      for (final category in categoriesResponse) {
        final articlesResponse = await _supabase
            .from('articles')
            .select('id')
            .eq('is_published', true)
            .eq('category_id', category['id']);

        categoriesWithCount.add({
          'id': category['id'],
          'name': category['name'],
          'count': articlesResponse.length,
        });
      }

      return categoriesWithCount;
    } catch (e) {
      throw ServerFailure(
        message: 'فشل في جلب فئات المقالات مع العدد: ${e.toString()}',
      );
    }
  }

  /// البحث في المقالات
  Future<List<Article>> searchArticles({
    required String searchTerm,
    String? category,
    String? author,
    int? limit,
  }) async {
    try {
      var query = _supabase.from('articles').select().eq('is_published', true);

      // البحث في العنوان والمحتوى والملخص
      if (searchTerm.isNotEmpty) {
        query = query.or(
          'title.ilike.%$searchTerm%,content.ilike.%$searchTerm%,summary.ilike.%$searchTerm%',
        );
      }

      // فلتر الفئة
      if (category != null && category.isNotEmpty) {
        query = query.eq('category', category);
      }

      // فلتر الكاتب
      if (author != null && author.isNotEmpty) {
        query = query.eq('author', author);
      }

      // الترتيب والحد الأقصى
      var orderedQuery = query.order('created_at', ascending: false);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      final response = await orderedQuery;

      return response.map((json) => Article.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في البحث عن المقالات: ${e.toString()}');
    }
  }

  /// إنشاء مقال جديد (للإدارة)
  Future<Article> createArticle({
    required String title,
    required String content,
    required String summary,
    required String category,
    required String author,
    String? imageUrl,
    List<String>? tags,
    bool isPublished = false,
    bool isFeatured = false,
    int readingTime = 5,
  }) async {
    try {
      final response =
          await _supabase
              .from('articles')
              .insert({
                'title': title,
                'content': content,
                'summary': summary,
                'category': category,
                'author': author,
                'image_url': imageUrl,
                'tags': tags,
                'is_published': isPublished,
                'is_featured': isFeatured,
                'reading_time': readingTime,
                'views_count': 0,
                'likes_count': 0,
                'created_at': DateTime.now().toIso8601String(),
                'updated_at': DateTime.now().toIso8601String(),
              })
              .select()
              .single();

      return Article.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في إنشاء المقال: ${e.toString()}');
    }
  }

  /// تحديث مقال (للإدارة)
  Future<Article> updateArticle({
    required String articleId,
    String? title,
    String? content,
    String? summary,
    String? category,
    String? author,
    String? imageUrl,
    List<String>? tags,
    bool? isPublished,
    bool? isFeatured,
    int? readingTime,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (title != null) updateData['title'] = title;
      if (content != null) updateData['content'] = content;
      if (summary != null) updateData['summary'] = summary;
      if (category != null) updateData['category'] = category;
      if (author != null) updateData['author'] = author;
      if (imageUrl != null) updateData['image_url'] = imageUrl;
      if (tags != null) updateData['tags'] = tags;
      if (isPublished != null) updateData['is_published'] = isPublished;
      if (isFeatured != null) updateData['is_featured'] = isFeatured;
      if (readingTime != null) updateData['reading_time'] = readingTime;

      final response =
          await _supabase
              .from('articles')
              .update(updateData)
              .eq('id', articleId)
              .select()
              .single();

      return Article.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في تحديث المقال: ${e.toString()}');
    }
  }

  /// حذف مقال (للإدارة)
  Future<void> deleteArticle(String articleId) async {
    try {
      await _supabase.from('articles').delete().eq('id', articleId);
    } catch (e) {
      throw ServerFailure(message: 'فشل في حذف المقال: ${e.toString()}');
    }
  }

  /// زيادة عدد المشاهدات
  Future<void> incrementViews(String articleId) async {
    try {
      await _supabase.rpc(
        'increment_article_views',
        params: {'article_id': articleId},
      );
    } catch (e) {
      // في حالة عدم وجود الدالة، نستخدم طريقة بديلة
      try {
        final article = await getArticleById(articleId);
        if (article != null) {
          await _supabase
              .from('articles')
              .update({
                'views_count': (article.viewsCount ?? 0) + 1,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', articleId);
        }
      } catch (e2) {
        // تجاهل الخطأ في حالة فشل تحديث المشاهدات
      }
    }
  }

  /// إعجاب/إلغاء إعجاب بمقال
  Future<void> toggleLike(String articleId, bool isLiked) async {
    try {
      final article = await getArticleById(articleId);
      if (article != null) {
        final newLikesCount =
            isLiked
                ? (article.likesCount ?? 0) + 1
                : (article.likesCount ?? 0) - 1;

        await _supabase
            .from('articles')
            .update({
              'likes_count': newLikesCount.clamp(0, double.infinity).toInt(),
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', articleId);
      }
    } catch (e) {
      throw ServerFailure(message: 'فشل في تحديث الإعجاب: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات المقالات
  Future<Map<String, dynamic>> getArticleStats() async {
    try {
      final response = await _supabase
          .from('articles')
          .select('category, is_published, views_count, likes_count');

      final stats = {
        'total': response.length,
        'published': 0,
        'categories': <String, int>{},
        'totalViews': 0,
        'totalLikes': 0,
      };

      for (final article in response) {
        if (article['is_published'] == true) {
          stats['published'] = (stats['published'] as int) + 1;
        }

        final category = article['category'] as String;
        final categories = stats['categories'] as Map<String, int>;
        categories[category] = (categories[category] ?? 0) + 1;

        stats['totalViews'] =
            (stats['totalViews'] as int) +
            ((article['views_count'] as int?) ?? 0);
        stats['totalLikes'] =
            (stats['totalLikes'] as int) +
            ((article['likes_count'] as int?) ?? 0);
      }

      return stats;
    } catch (e) {
      throw ServerFailure(
        message: 'فشل في جلب إحصائيات المقالات: ${e.toString()}',
      );
    }
  }
}
