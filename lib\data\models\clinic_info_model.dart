class ClinicInfoModel {
  final String id;
  final String infoType;
  final String infoKey;
  final String infoValue;
  final String displayName;
  final String iconName;
  final bool isActive;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime updatedAt;

  ClinicInfoModel({
    required this.id,
    required this.infoType,
    required this.infoKey,
    required this.infoValue,
    required this.displayName,
    required this.iconName,
    required this.isActive,
    required this.displayOrder,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ClinicInfoModel.fromJson(Map<String, dynamic> json) {
    return ClinicInfoModel(
      id: json['id'] ?? '',
      infoType: json['info_type'] ?? '',
      infoKey: json['info_key'] ?? '',
      infoValue: json['info_value'] ?? '',
      displayName: json['display_name'] ?? '',
      iconName: json['icon_name'] ?? '',
      isActive: json['is_active'] ?? false,
      displayOrder: json['display_order'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'info_type': infoType,
      'info_key': infoKey,
      'info_value': infoValue,
      'display_name': displayName,
      'icon_name': iconName,
      'is_active': isActive,
      'display_order': displayOrder,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isPhone => infoType == 'phone';
  bool get isEmail => infoType == 'email';
  bool get isSocialMedia => infoType == 'social_media';
  bool get isAddress => infoType == 'address';
  bool get isWebsite => infoType == 'website';

  String get actionUrl {
    switch (infoType) {
      case 'phone':
        return 'tel:$infoValue';
      case 'email':
        return 'mailto:$infoValue';
      case 'social_media':
      case 'website':
        return infoValue;
      case 'address':
        return 'https://maps.google.com/?q=${Uri.encodeComponent(infoValue)}';
      default:
        return infoValue;
    }
  }
}
