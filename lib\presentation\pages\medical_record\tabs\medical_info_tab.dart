import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../data/models/medical_info_model.dart';
import '../../../widgets/common/filter_button.dart';

class MedicalInfoTab extends StatefulWidget {
  final List<MedicalInfoModel> medicalInfo;
  final String? patientId;

  const MedicalInfoTab({super.key, required this.medicalInfo, this.patientId});

  @override
  State<MedicalInfoTab> createState() => _MedicalInfoTabState();
}

class _MedicalInfoTabState extends State<MedicalInfoTab> {
  String selectedFilter = 'all';

  final Map<String, String> filterOptions = {
    'all': 'الكل',
    'medication': 'الأدوية',
    'allergy': 'الحساسية',
    'condition': 'الحالات الطبية',
    'activity': 'النشاط البدني',
  };

  @override
  Widget build(BuildContext context) {
    if (widget.medicalInfo.isEmpty) {
      return _buildEmptyState();
    }

    final filteredInfo = _getFilteredInfo();

    return Column(
      children: [
        _buildFilterChips(),
        Expanded(
          child:
              filteredInfo.isEmpty
                  ? _buildNoResultsState()
                  : _buildMedicalInfoList(filteredInfo),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.medical_information_outlined,
            size: 80.sp,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد معلومات طبية',
            style: TextStyle(fontSize: 18.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم عرض المعلومات الطبية هنا عند توفرها',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 60.sp, color: AppColors.textSecondary),
          SizedBox(height: 16.h),
          Text(
            'لا توجد نتائج للفلتر المحدد',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: filterOptions.length,
        separatorBuilder: (context, index) => SizedBox(width: 8.w),
        itemBuilder: (context, index) {
          final key = filterOptions.keys.elementAt(index);
          final label = filterOptions[key]!;
          final isSelected = selectedFilter == key;

          return MedicalFilterButton(
            text: label,
            isSelected: isSelected,
            onTap: () {
              setState(() {
                selectedFilter = key;
              });
            },
          );
        },
      ),
    );
  }

  Widget _buildMedicalInfoList(List<MedicalInfoModel> filteredInfo) {
    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: filteredInfo.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final info = filteredInfo[index];
        return _buildMedicalInfoCard(info);
      },
    );
  }

  Widget _buildMedicalInfoCard(MedicalInfoModel info) {
    final color = Color(int.parse(info.colorCode.replaceFirst('#', '0xFF')));

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      _getIconForType(info.infoType),
                      color: color,
                      size: 20.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          info.name,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 2.h),
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 2.h,
                              ),
                              decoration: BoxDecoration(
                                color: color.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                info.displayInfoType,
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.bold,
                                  color: color,
                                ),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 2.h,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    info.isActive
                                        ? Colors.green.withValues(alpha: 0.1)
                                        : Colors.grey.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                info.statusText,
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      info.isActive
                                          ? Colors.green
                                          : Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (info.description != null &&
                      info.description!.isNotEmpty) ...[
                    _buildInfoRow('الوصف', info.description!),
                    SizedBox(height: 8.h),
                  ],
                  if (info.dosage != null && info.dosage!.isNotEmpty) ...[
                    _buildInfoRow('الجرعة', info.dosage!),
                    SizedBox(height: 8.h),
                  ],
                  if (info.frequency != null && info.frequency!.isNotEmpty) ...[
                    _buildInfoRow('التكرار', info.frequency!),
                    SizedBox(height: 8.h),
                  ],
                  if (info.severity != null && info.severity!.isNotEmpty) ...[
                    _buildInfoRow('الشدة', info.displaySeverity),
                    SizedBox(height: 8.h),
                  ],
                  Row(
                    children: [
                      Expanded(
                        child: _buildInfoRow(
                          'تاريخ البداية',
                          info.displayStartDate,
                        ),
                      ),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: _buildInfoRow(
                          'تاريخ النهاية',
                          info.displayEndDate,
                        ),
                      ),
                    ],
                  ),
                  if (info.duration != null) ...[
                    SizedBox(height: 8.h),
                    _buildInfoRow('المدة', info.displayDuration),
                  ],
                  if (info.notes != null && info.notes!.isNotEmpty) ...[
                    SizedBox(height: 12.h),
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: AppColors.background,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: AppColors.border),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'ملاحظات:',
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.bold,
                              color: AppColors.textSecondary,
                            ),
                          ),
                          SizedBox(height: 4.h),
                          Text(
                            info.notes!,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 80.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  List<MedicalInfoModel> _getFilteredInfo() {
    if (selectedFilter == 'all') {
      return widget.medicalInfo;
    }
    return widget.medicalInfo
        .where((info) => info.infoType.toLowerCase() == selectedFilter)
        .toList();
  }

  IconData _getIconForType(String type) {
    switch (type.toLowerCase()) {
      case 'medication':
        return Icons.medication;
      case 'allergy':
        return Icons.warning;
      case 'condition':
        return Icons.medical_information;
      case 'activity':
        return Icons.fitness_center;
      default:
        return Icons.info;
    }
  }
}
