import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/article.dart';
import '../../../core/errors/failures.dart';
import '../../../data/repositories/article_repository.dart';

part 'article_event.dart';
part 'article_state.dart';

/// BLoC للمقالات
class ArticleBloc extends Bloc<ArticleEvent, ArticleState> {
  final ArticleRepository _articleRepository;

  ArticleBloc({ArticleRepository? articleRepository})
      : _articleRepository = articleRepository ?? ArticleRepository(),
        super(ArticleInitial()) {
    on<LoadArticles>(_onLoadArticles);
    on<LoadFeaturedArticles>(_onLoadFeaturedArticles);
    on<LoadArticlesByCategory>(_onLoadArticlesByCategory);
    on<LoadArticleCategories>(_onLoadArticleCategories);
    on<LoadCategoriesWithCount>(_onLoadCategoriesWithCount);
    on<SearchArticles>(_onSearchArticles);
    on<LoadArticleById>(_onLoadArticleById);
    on<IncrementArticleViews>(_onIncrementArticleViews);
    on<ToggleArticleLike>(_onToggleArticleLike);
    on<LoadArticleStats>(_onLoadArticleStats);
    on<RefreshArticles>(_onRefreshArticles);
  }

  /// تحميل المقالات
  Future<void> _onLoadArticles(
    LoadArticles event,
    Emitter<ArticleState> emit,
  ) async {
    emit(ArticleLoading());

    try {
      final articles = await _articleRepository.getAllArticles(
        categoryId: event.category,
        searchTerm: event.searchTerm,
        limit: event.limit,
        offset: event.offset,
        sortBy: event.sortBy,
        ascending: event.ascending,
      );

      emit(ArticleLoaded(articles: articles));
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل المقالات المميزة
  Future<void> _onLoadFeaturedArticles(
    LoadFeaturedArticles event,
    Emitter<ArticleState> emit,
  ) async {
    emit(ArticleLoading());

    try {
      final articles = await _articleRepository.getFeaturedArticles(
        limit: event.limit,
      );

      emit(FeaturedArticlesLoaded(articles: articles));
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل المقالات حسب الفئة
  Future<void> _onLoadArticlesByCategory(
    LoadArticlesByCategory event,
    Emitter<ArticleState> emit,
  ) async {
    emit(ArticleLoading());

    try {
      final articles = await _articleRepository.getArticlesByCategory(
        event.category,
        limit: event.limit,
      );

      emit(ArticlesByCategoryLoaded(
        category: event.category,
        articles: articles,
      ));
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل فئات المقالات
  Future<void> _onLoadArticleCategories(
    LoadArticleCategories event,
    Emitter<ArticleState> emit,
  ) async {
    emit(ArticleLoading());

    try {
      final categories = await _articleRepository.getArticleCategories();
      final categoryNames = categories.map((c) => c.name).toList();

      emit(ArticleCategoriesLoaded(categories: categoryNames));
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل فئات المقالات مع العدد
  Future<void> _onLoadCategoriesWithCount(
    LoadCategoriesWithCount event,
    Emitter<ArticleState> emit,
  ) async {
    emit(ArticleLoading());

    try {
      final categoriesWithCount = await _articleRepository.getCategoriesWithCount();

      emit(CategoriesWithCountLoaded(categoriesWithCount: categoriesWithCount));
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// البحث في المقالات
  Future<void> _onSearchArticles(
    SearchArticles event,
    Emitter<ArticleState> emit,
  ) async {
    emit(ArticleLoading());

    try {
      final articles = await _articleRepository.searchArticles(
        searchTerm: event.searchTerm,
        category: event.category,
        author: event.author,
        limit: event.limit,
      );

      emit(ArticleSearchResults(articles: articles));
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل مقال بواسطة ID
  Future<void> _onLoadArticleById(
    LoadArticleById event,
    Emitter<ArticleState> emit,
  ) async {
    emit(ArticleLoading());

    try {
      final article = await _articleRepository.getArticleById(event.articleId);

      if (article != null) {
        emit(ArticleDetailLoaded(article: article));
      } else {
        emit(ArticleError(
          failure: ServerFailure(message: 'المقال غير موجود'),
        ));
      }
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// زيادة عدد المشاهدات
  Future<void> _onIncrementArticleViews(
    IncrementArticleViews event,
    Emitter<ArticleState> emit,
  ) async {
    try {
      await _articleRepository.incrementViews(event.articleId);
      // لا نحتاج لتحديث الحالة هنا
    } catch (e) {
      // تجاهل الخطأ في حالة فشل تحديث المشاهدات
    }
  }

  /// إعجاب/إلغاء إعجاب بمقال
  Future<void> _onToggleArticleLike(
    ToggleArticleLike event,
    Emitter<ArticleState> emit,
  ) async {
    try {
      await _articleRepository.toggleLike(event.articleId, event.isLiked);

      // إعادة تحميل المقال لإظهار التحديث
      final article = await _articleRepository.getArticleById(event.articleId);
      if (article != null) {
        emit(ArticleDetailLoaded(article: article));
      }
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل إحصائيات المقالات
  Future<void> _onLoadArticleStats(
    LoadArticleStats event,
    Emitter<ArticleState> emit,
  ) async {
    emit(ArticleLoading());

    try {
      final stats = await _articleRepository.getArticleStats();

      emit(ArticleStatsLoaded(stats: stats));
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحديث المقالات
  Future<void> _onRefreshArticles(
    RefreshArticles event,
    Emitter<ArticleState> emit,
  ) async {
    try {
      final articles = await _articleRepository.getAllArticles(
        categoryId: event.category,
        searchTerm: event.searchTerm,
        limit: event.limit,
        offset: event.offset,
        sortBy: event.sortBy,
        ascending: event.ascending,
      );

      emit(ArticleLoaded(articles: articles));
    } catch (e) {
      emit(ArticleError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }
}


