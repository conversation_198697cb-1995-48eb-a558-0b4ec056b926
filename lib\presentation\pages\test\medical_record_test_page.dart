import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/app_colors.dart';
import '../../bloc/medical_record/medical_record_bloc.dart';
import '../medical_record/medical_record_page.dart';

/// صفحة اختبار السجل الطبي
class MedicalRecordTestPage extends StatelessWidget {
  const MedicalRecordTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: Text(
          'اختبار السجل الطبي',
          style: TextStyle(
            fontSize: 20.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppColors.primary,
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(24.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.medical_information,
              size: 80.sp,
              color: AppColors.primary,
            ),
            SizedBox(height: 24.h),
            Text(
              'اختبار صفحة السجل الطبي',
              style: TextStyle(
                fontSize: 24.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            Text(
              'اختر طريقة فتح السجل الطبي',
              style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 40.h),

            // زر فتح السجل الطبي مع بيانات تجريبية
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _openMedicalRecordWithTestData(context),
                icon: Icon(Icons.science, size: 20.sp),
                label: Text(
                  'فتح السجل الطبي (بيانات تجريبية)',
                  style: TextStyle(fontSize: 16.sp),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),

            SizedBox(height: 16.h),

            // زر فتح السجل الطبي فارغ
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _openEmptyMedicalRecord(context),
                icon: Icon(Icons.folder_open, size: 20.sp),
                label: Text(
                  'فتح السجل الطبي (فارغ)',
                  style: TextStyle(fontSize: 16.sp),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
              ),
            ),

            SizedBox(height: 32.h),

            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Column(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue, size: 24.sp),
                  SizedBox(height: 8.h),
                  Text(
                    'ملاحظة',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.blue,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    'هذه صفحة اختبار لعرض السجل الطبي. في التطبيق الحقيقي، سيتم تمرير معرف المريض أو معرف المصادقة.',
                    style: TextStyle(fontSize: 14.sp, color: Colors.blue[700]),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openMedicalRecordWithTestData(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => BlocProvider(
              create: (context) => MedicalRecordBloc(),
              child: const MedicalRecordPage(
                // يمكن تمرير معرف تجريبي هنا
                // patientId: 'test-patient-id',
                // authId: 'test-auth-id',
              ),
            ),
      ),
    );
  }

  void _openEmptyMedicalRecord(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => BlocProvider(
              create: (context) => MedicalRecordBloc(),
              child: const MedicalRecordPage(
                // بدون تمرير أي معرف لاختبار الحالة الفارغة
              ),
            ),
      ),
    );
  }
}

/// صفحة اختبار مبسطة للسجل الطبي
class SimpleMedicalRecordTest extends StatelessWidget {
  const SimpleMedicalRecordTest({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('اختبار السجل الطبي'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder:
                    (context) => BlocProvider(
                      create: (context) => MedicalRecordBloc(),
                      child: const MedicalRecordPage(),
                    ),
              ),
            );
          },
          child: const Text('فتح السجل الطبي'),
        ),
      ),
    );
  }
}
