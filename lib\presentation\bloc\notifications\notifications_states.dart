import 'package:equatable/equatable.dart';
import '../../../data/models/notification_model.dart';

/// حالات الإشعارات
abstract class NotificationsState extends Equatable {
  const NotificationsState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class NotificationsInitial extends NotificationsState {
  const NotificationsInitial();
}

/// حالة التحميل
class NotificationsLoading extends NotificationsState {
  const NotificationsLoading();
}

/// حالة النجاح
class NotificationsLoaded extends NotificationsState {
  final List<NotificationModel> notifications;
  final int unreadCount;

  const NotificationsLoaded({
    required this.notifications,
    required this.unreadCount,
  });

  @override
  List<Object?> get props => [notifications, unreadCount];

  /// نسخ مع تعديل
  NotificationsLoaded copyWith({
    List<NotificationModel>? notifications,
    int? unreadCount,
  }) {
    return NotificationsLoaded(
      notifications: notifications ?? this.notifications,
      unreadCount: unreadCount ?? this.unreadCount,
    );
  }
}

/// حالة الخطأ
class NotificationsError extends NotificationsState {
  final String message;

  const NotificationsError(this.message);

  @override
  List<Object?> get props => [message];
}

/// حالة نجاح العملية
class NotificationsOperationSuccess extends NotificationsState {
  final String message;
  final List<NotificationModel> notifications;
  final int unreadCount;

  const NotificationsOperationSuccess({
    required this.message,
    required this.notifications,
    required this.unreadCount,
  });

  @override
  List<Object?> get props => [message, notifications, unreadCount];
}
