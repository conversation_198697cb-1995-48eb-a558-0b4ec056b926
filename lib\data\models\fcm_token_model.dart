import 'package:equatable/equatable.dart';

/// نموذج FCM Token
class FCMTokenModel extends Equatable {
  final String id;
  final String userId;
  final String fcmToken;
  final Map<String, dynamic> deviceInfo;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const FCMTokenModel({
    required this.id,
    required this.userId,
    required this.fcmToken,
    required this.deviceInfo,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء من JSON
  factory FCMTokenModel.fromJson(Map<String, dynamic> json) {
    return FCMTokenModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      fcmToken: json['fcm_token'] as String,
      deviceInfo: Map<String, dynamic>.from(json['device_info'] as Map),
      isActive: json['is_active'] as bool,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'fcm_token': fcmToken,
      'device_info': deviceInfo,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة
  FCMTokenModel copyWith({
    String? id,
    String? userId,
    String? fcmToken,
    Map<String, dynamic>? deviceInfo,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FCMTokenModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      fcmToken: fcmToken ?? this.fcmToken,
      deviceInfo: deviceInfo ?? this.deviceInfo,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        fcmToken,
        deviceInfo,
        isActive,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'FCMTokenModel(id: $id, userId: $userId, fcmToken: ${fcmToken.substring(0, 20)}..., isActive: $isActive)';
  }
}

/// نموذج معلومات الجهاز
class DeviceInfo extends Equatable {
  final String platform;
  final String model;
  final String osVersion;
  final String appVersion;
  final String deviceId;

  const DeviceInfo({
    required this.platform,
    required this.model,
    required this.osVersion,
    required this.appVersion,
    required this.deviceId,
  });

  /// إنشاء من JSON
  factory DeviceInfo.fromJson(Map<String, dynamic> json) {
    return DeviceInfo(
      platform: json['platform'] as String,
      model: json['model'] as String,
      osVersion: json['os_version'] as String,
      appVersion: json['app_version'] as String,
      deviceId: json['device_id'] as String,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'platform': platform,
      'model': model,
      'os_version': osVersion,
      'app_version': appVersion,
      'device_id': deviceId,
    };
  }

  @override
  List<Object?> get props => [
        platform,
        model,
        osVersion,
        appVersion,
        deviceId,
      ];

  @override
  String toString() {
    return 'DeviceInfo(platform: $platform, model: $model, osVersion: $osVersion)';
  }
}
