import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_logger.dart';
import '../../../core/utils/helpers.dart';
import '../../../core/services/account_deletion_service.dart';
import '../../../data/models/patient.dart';
import '../../../data/repositories/patient_repository.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/custom_button.dart';
import '../../widgets/common/custom_loading.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_events.dart';
import '../../bloc/auth/auth_states.dart';
import '../auth/login_page.dart';

/// صفحة تعديل الملف الشخصي
class EditProfilePage extends StatefulWidget {
  const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();

  String? _selectedGender;
  DateTime? _selectedBirthDate;
  bool _isLoading = false;
  bool _isLoadingData = true;
  Patient? _currentPatient;

  @override
  void initState() {
    super.initState();
    AppLogger.info('Edit Profile page opened', category: LogCategory.ui);
    _loadCurrentPatientData();
  }

  Future<void> _loadCurrentPatientData() async {
    try {
      final authState = context.read<AuthBloc>().state;
      if (authState is AuthSuccess) {
        // التأكد من أن النوع صحيح
        String? gender = authState.patient.gender;
        AppLogger.info(
          'Loading patient gender',
          category: LogCategory.ui,
          data: {'originalGender': gender},
        );

        if (gender != null && !['ذكر', 'أنثى'].contains(gender)) {
          AppLogger.warning(
            'Invalid gender value, resetting to null',
            category: LogCategory.ui,
            data: {'invalidGender': gender},
          );
          gender = null; // إعادة تعيين إذا كانت القيمة غير صحيحة
        }

        setState(() {
          _currentPatient = authState.patient;
          _nameController.text = authState.patient.name;
          _phoneController.text = authState.patient.phone ?? '';
          _selectedGender = gender;
          _selectedBirthDate = authState.patient.birthDate;
          _isLoadingData = false;
        });
      }
    } catch (e) {
      AppLogger.error(
        'Error loading patient data',
        category: LogCategory.ui,
        error: e,
      );
      setState(() {
        _isLoadingData = false;
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const CustomAppBar(
          title: 'تعديل الملف الشخصي',
          showBackButton: true,
        ),
        body:
            _isLoadingData
                ? const Center(child: CircularProgressIndicator())
                : SingleChildScrollView(
                  padding: EdgeInsets.all(24.w),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // معلومات شخصية
                        Text(
                          'المعلومات الشخصية',
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 20.h),

                        // حقل الاسم
                        Text(
                          'الاسم الكامل',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        CustomTextField(
                          controller: _nameController,
                          hint: 'أدخل الاسم الكامل',
                          prefixIcon: Icons.person,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال الاسم';
                            }
                            return null;
                          },
                        ),

                        SizedBox(height: 20.h),

                        // حقل رقم الهاتف
                        Text(
                          'رقم الهاتف',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        CustomTextField(
                          controller: _phoneController,
                          hint: 'أدخل رقم الهاتف',
                          prefixIcon: Icons.phone,
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال رقم الهاتف';
                            }
                            return null;
                          },
                        ),

                        SizedBox(height: 20.h),

                        // النوع
                        Text(
                          'النوع',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        GestureDetector(
                          onTap: () => _showGenderPicker(),
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.border),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  _selectedGender == 'ذكر'
                                      ? Icons.male
                                      : _selectedGender == 'أنثى'
                                      ? Icons.female
                                      : Icons.person,
                                  color: AppColors.primary,
                                  size: 20.sp,
                                ),
                                SizedBox(width: 12.w),
                                Text(
                                  _selectedGender ?? 'اختر النوع',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color:
                                        _selectedGender != null
                                            ? AppColors.textPrimary
                                            : AppColors.textSecondary,
                                  ),
                                ),
                                const Spacer(),
                                Icon(
                                  Icons.arrow_drop_down,
                                  color: AppColors.textSecondary,
                                  size: 24.sp,
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(height: 20.h),

                        // تاريخ الميلاد
                        Text(
                          'تاريخ الميلاد',
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 8.h),
                        GestureDetector(
                          onTap: _selectBirthDate,
                          child: Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(16.w),
                            decoration: BoxDecoration(
                              border: Border.all(color: AppColors.border),
                              borderRadius: BorderRadius.circular(12.r),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.calendar_today,
                                  color: AppColors.primary,
                                  size: 20.sp,
                                ),
                                SizedBox(width: 12.w),
                                Text(
                                  _selectedBirthDate != null
                                      ? '${_selectedBirthDate!.day}/${_selectedBirthDate!.month}/${_selectedBirthDate!.year}'
                                      : 'اختر تاريخ الميلاد',
                                  style: TextStyle(
                                    fontSize: 16.sp,
                                    color:
                                        _selectedBirthDate != null
                                            ? AppColors.textPrimary
                                            : AppColors.textSecondary,
                                  ),
                                ),
                                const Spacer(),
                                Icon(
                                  Icons.arrow_drop_down,
                                  color: AppColors.textSecondary,
                                  size: 24.sp,
                                ),
                              ],
                            ),
                          ),
                        ),

                        SizedBox(height: 40.h),

                        // زر الحفظ
                        SizedBox(
                          width: double.infinity,
                          child: CustomButton(
                            text: 'حفظ التغييرات',
                            onPressed: _isLoading ? null : _saveProfile,
                            isLoading: _isLoading,
                            icon: Icons.save,
                          ),
                        ),

                        SizedBox(height: 24.h),

                        // زر حذف الحساب
                        _buildDeleteAccountButton(),
                      ],
                    ),
                  ),
                ),
      ),
    );
  }

  void _showGenderPicker() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder:
          (context) => Directionality(
            textDirection: TextDirection.rtl,
            child: Container(
              padding: EdgeInsets.all(20.w),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'اختر النوع',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w600,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 20.h),
                  ListTile(
                    leading: Icon(Icons.male, color: AppColors.primary),
                    title: Text('ذكر', style: TextStyle(fontSize: 16.sp)),
                    onTap: () {
                      setState(() {
                        _selectedGender = 'ذكر';
                      });
                      Navigator.pop(context);
                    },
                  ),
                  ListTile(
                    leading: Icon(Icons.female, color: AppColors.primary),
                    title: Text('أنثى', style: TextStyle(fontSize: 16.sp)),
                    onTap: () {
                      setState(() {
                        _selectedGender = 'أنثى';
                      });
                      Navigator.pop(context);
                    },
                  ),
                  SizedBox(height: 20.h),
                ],
              ),
            ),
          ),
    );
  }

  Future<void> _selectBirthDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _selectedBirthDate ??
          DateTime.now().subtract(const Duration(days: 365 * 25)),
      firstDate: DateTime(1950),
      lastDate: DateTime.now(),
      locale: const Locale('ar'),
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl,
          child: Theme(
            data: Theme.of(context).copyWith(
              colorScheme: const ColorScheme.light(
                primary: AppColors.primary,
                onPrimary: AppColors.white,
                surface: AppColors.white,
                onSurface: AppColors.textPrimary,
              ),
            ),
            child: child!,
          ),
        );
      },
    );

    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
      });
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من أن النوع تم اختياره
    if (_selectedGender == null) {
      Helpers.showErrorSnackBar(context, 'يرجى اختيار النوع');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      AppLogger.info(
        'Profile save requested',
        category: LogCategory.ui,
        data: {
          'name': _nameController.text,
          'phone': _phoneController.text,
          'gender': _selectedGender,
          'birthDate': _selectedBirthDate?.toIso8601String(),
        },
      );

      if (_currentPatient == null) {
        throw Exception('بيانات المريض غير متوفرة');
      }

      // تحديث البيانات في قاعدة البيانات
      final repository = PatientRepository();
      await repository.updatePatient(
        authId: _currentPatient!.authId!,
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        gender: _selectedGender!,
        birthDate: _selectedBirthDate,
      );

      if (mounted) {
        // تحديث AuthBloc بالبيانات الجديدة
        context.read<AuthBloc>().add(const CheckAuthStatus());

        Helpers.showSuccessSnackBar(context, 'تم حفظ التغييرات بنجاح');
        Navigator.pop(context);
      }
    } catch (e) {
      AppLogger.error(
        'Error saving profile',
        category: LogCategory.ui,
        error: e,
      );

      if (mounted) {
        String errorMessage = 'فشل في حفظ التغييرات';
        if (e.toString().contains('network')) {
          errorMessage = 'خطأ في الاتصال بالإنترنت';
        } else if (e.toString().contains('unauthorized')) {
          errorMessage = 'غير مصرح لك بتحديث هذه البيانات';
        }

        Helpers.showErrorSnackBar(context, errorMessage);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// بناء زر حذف الحساب
  Widget _buildDeleteAccountButton() {
    return Container(
      width: double.infinity,
      margin: EdgeInsets.only(top: 16.h),
      child: OutlinedButton.icon(
        onPressed: _showDeleteAccountDialog,
        icon: Icon(Icons.delete_forever, color: AppColors.error, size: 20.sp),
        label: Text(
          'حذف الحساب نهائياً',
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.error,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: OutlinedButton.styleFrom(
          side: BorderSide(color: AppColors.error, width: 1.5),
          padding: EdgeInsets.symmetric(vertical: 12.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
      ),
    );
  }

  /// عرض حوار تأكيد حذف الحساب
  void _showDeleteAccountDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => Directionality(
            textDirection: TextDirection.rtl,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16.r),
              ),
              title: Row(
                children: [
                  Icon(
                    Icons.warning_amber_rounded,
                    color: AppColors.error,
                    size: 28.sp,
                  ),
                  SizedBox(width: 12.w),
                  Text(
                    'تحذير',
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.error,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'هل أنت متأكد من رغبتك في حذف حسابك نهائياً؟',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  Text(
                    'سيتم حذف:',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  ...[
                    '• جميع بياناتك الشخصية',
                    '• سجلك الطبي',
                    '• مواعيدك',
                    '• حسابك من النظام',
                  ].map(
                    (item) => Padding(
                      padding: EdgeInsets.only(bottom: 4.h),
                      child: Text(
                        item,
                        style: TextStyle(
                          fontSize: 13.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Container(
                    padding: EdgeInsets.all(12.w),
                    decoration: BoxDecoration(
                      color: AppColors.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(
                        color: AppColors.error.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Text(
                      '⚠️ هذا الإجراء لا يمكن التراجع عنه',
                      style: TextStyle(
                        fontSize: 13.sp,
                        color: AppColors.error,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'إلغاء',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: _deleteAccount,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.error,
                    foregroundColor: AppColors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                  ),
                  child: Text(
                    'حذف نهائياً',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  /// تنفيذ حذف الحساب
  Future<void> _deleteAccount() async {
    Navigator.pop(context); // إغلاق الحوار

    // عرض مؤشر التحميل
    CustomLoadingDialog.show(context, message: 'جاري حذف الحساب...');

    try {
      // حذف الحساب باستخدام الخدمة
      await AccountDeletionService.deleteAccount();

      if (mounted) {
        // إغلاق مؤشر التحميل
        CustomLoadingDialog.hide(context);

        // العودة إلى صفحة تسجيل الدخول
        Navigator.pushAndRemoveUntil(
          context,
          MaterialPageRoute(builder: (context) => const LoginPage()),
          (route) => false,
        );

        // عرض رسالة نجاح
        Helpers.showSuccessSnackBar(context, 'تم حذف الحساب بنجاح');
      }
    } catch (e) {
      AppLogger.error(
        'Error deleting account',
        category: LogCategory.auth,
        error: e,
      );

      if (mounted) {
        // إغلاق مؤشر التحميل
        CustomLoadingDialog.hide(context);

        // عرض رسالة خطأ
        String errorMessage = 'فشل في حذف الحساب';
        if (e.toString().contains('network')) {
          errorMessage = 'خطأ في الاتصال بالإنترنت';
        } else if (e.toString().contains('unauthorized')) {
          errorMessage = 'غير مصرح لك بحذف هذا الحساب';
        }

        Helpers.showErrorSnackBar(context, errorMessage);
      }
    }
  }
}
