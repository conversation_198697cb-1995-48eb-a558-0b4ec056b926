import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/app_logger.dart';

import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_events.dart';
import '../../bloc/auth/auth_states.dart';
import '../auth/login_page.dart';
import 'edit_profile_page.dart';
import 'change_password_page.dart';
import 'privacy_policy_page.dart';
import '../../widgets/common/custom_app_bar.dart';
import 'package:url_launcher/url_launcher.dart';

/// صفحة الملف الشخصي
class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AuthBloc, AuthState>(
      builder: (context, state) {
        if (state is! AuthSuccess) {
          return Scaffold(
            backgroundColor: AppColors.background,
            appBar: const CustomAppBar(title: AppStrings.profile),
            body: const Center(child: CircularProgressIndicator()),
          );
        }

        final patient = state.patient;

        return Scaffold(
          backgroundColor: AppColors.background,
          appBar: CustomAppBar(title: patient.name),
          body: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: Column(
              children: [
                // بطاقة المعلومات الشخصية
                _buildProfileCard(patient),

                SizedBox(height: 20.h),

                // قائمة الخيارات
                _buildOptionsCard(context),

                SizedBox(height: 20.h),

                // زر تسجيل الخروج
                _buildLogoutButton(context),

                SizedBox(height: 16.h),

                // Developed by
                Text(
                  '${AppStrings.developedBy} ${AppStrings.khwasstech}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textLight,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildProfileCard(patient) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primary, AppColors.primary.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // صورة المستخدم
          Container(
            height: 108, // مقاس ثابت
            width: 108, // مقاس ثابت
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const CircleAvatar(
              radius: 50, // مقاس ثابت
              backgroundImage: AssetImage('assets/images/logo.jpeg'),
              backgroundColor: AppColors.white,
            ),
          ),

          SizedBox(height: 20.h),

          // اسم المستخدم
          Text(
            patient.name,
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
          ),

          SizedBox(height: 8.h),

          // Patient ID
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(Icons.badge, size: 16.sp, color: Colors.white),
                SizedBox(width: 8.w),
                Text(
                  'ID: ${patient.id}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 12.h),

          // شارة المستخدم المميز
          if (patient.isPremium)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              decoration: BoxDecoration(
                color: AppColors.warning,
                borderRadius: BorderRadius.circular(20.r),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.warning.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.star, size: 16.sp, color: Colors.white),
                  SizedBox(width: 8.w),
                  Text(
                    AppStrings.premiumUser,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOptionsCard(BuildContext context) {
    return Card(
      child: Column(
        children: [
          _buildOptionTile(
            icon: Icons.edit,
            title: 'تعديل الملف الشخصي',
            onTap: () {
              AppLogger.navigation('ProfilePage', 'EditProfilePage');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EditProfilePage(),
                ),
              );
            },
          ),
          _buildDivider(),
          _buildOptionTile(
            icon: Icons.lock,
            title: 'تغيير كلمة السر',
            onTap: () {
              AppLogger.navigation('ProfilePage', 'ChangePasswordPage');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const ChangePasswordPage(),
                ),
              );
            },
          ),
          _buildDivider(),
          _buildOptionTile(
            icon: Icons.security,
            title: 'الأمان والخصوصية',
            onTap: () {
              AppLogger.navigation('ProfilePage', 'PrivacyPolicyPage');
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PrivacyPolicyPage(),
                ),
              );
            },
          ),
          _buildDivider(),
          _buildOptionTile(
            icon: Icons.help,
            title: 'المساعدة والدعم',
            onTap: () => _sendSupportEmail(),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.primary, size: 24.sp),
      title: Text(
        title,
        style: TextStyle(fontSize: 16.sp, color: AppColors.textPrimary),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16.sp,
        color: AppColors.textLight,
      ),
      onTap: onTap,
    );
  }

  Widget _buildDivider() {
    return Divider(height: 1.h, color: AppColors.divider);
  }

  Widget _buildLogoutButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () => _showLogoutDialog(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.white,
          padding: EdgeInsets.symmetric(vertical: 12.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: Text(
          AppStrings.logout,
          style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  Future<void> _sendSupportEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query: 'subject=طلب مساعدة ودعم&body=مرحباً،%0A%0Aأحتاج مساعدة في...',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      }
    } catch (e) {
      AppLogger.error('Error launching email', error: e);
    }
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تسجيل الخروج'),
            content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () async {
                  Navigator.of(context).pop();

                  // تسجيل الخروج من Bloc
                  context.read<AuthBloc>().add(const LogoutRequested());

                  // الانتقال لصفحة تسجيل الدخول مباشرة
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(builder: (context) => const LoginPage()),
                    (route) => false,
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.error,
                ),
                child: const Text('تسجيل الخروج'),
              ),
            ],
          ),
    );
  }
}
