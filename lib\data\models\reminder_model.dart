class ReminderModel {
  final String id;
  final String patientId;
  final String reminderType;
  final String title;
  final String? description;
  final String reminderTime; // Time in HH:MM format
  final List<int> daysOfWeek; // 1=Monday, 7=Sunday
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ReminderModel({
    required this.id,
    required this.patientId,
    required this.reminderType,
    required this.title,
    this.description,
    required this.reminderTime,
    required this.daysOfWeek,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ReminderModel.fromMap(Map<String, dynamic> map) {
    return ReminderModel(
      id: map['id'] as String,
      patientId: map['patient_id'] as String,
      reminderType: map['reminder_type'] as String,
      title: map['title'] as String,
      description: map['description'] as String?,
      reminderTime: map['reminder_time'] as String,
      daysOfWeek: List<int>.from(map['days_of_week'] ?? [1, 2, 3, 4, 5, 6, 7]),
      isActive: map['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'patient_id': patientId,
      'reminder_type': reminderType,
      'title': title,
      'description': description,
      'reminder_time': reminderTime,
      'days_of_week': daysOfWeek,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  ReminderModel copyWith({
    String? id,
    String? patientId,
    String? reminderType,
    String? title,
    String? description,
    String? reminderTime,
    List<int>? daysOfWeek,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ReminderModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      reminderType: reminderType ?? this.reminderType,
      title: title ?? this.title,
      description: description ?? this.description,
      reminderTime: reminderTime ?? this.reminderTime,
      daysOfWeek: daysOfWeek ?? this.daysOfWeek,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ReminderModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ReminderModel(id: $id, title: $title, reminderType: $reminderType, isActive: $isActive)';
  }

  // Helper methods
  String get displayReminderType {
    switch (reminderType.toLowerCase()) {
      case 'medication':
        return 'دواء';
      case 'supplement':
        return 'مكمل غذائي';
      case 'exercise':
        return 'تمرين';
      case 'meal':
        return 'وجبة';
      case 'water':
        return 'شرب الماء';
      case 'appointment':
        return 'موعد طبي';
      case 'measurement':
        return 'قياس الوزن';
      case 'sleep':
        return 'النوم';
      case 'custom':
        return 'مخصص';
      default:
        return reminderType;
    }
  }

  String get displayTime {
    try {
      final parts = reminderTime.split(':');
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);

      String period = hour >= 12 ? 'م' : 'ص';
      int displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);

      return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
    } catch (e) {
      return reminderTime;
    }
  }

  String get displayDays {
    if (daysOfWeek.length == 7) return 'يومياً';
    if (daysOfWeek.length == 5 &&
        !daysOfWeek.contains(6) &&
        !daysOfWeek.contains(7)) {
      return 'أيام العمل';
    }
    if (daysOfWeek.length == 2 &&
        daysOfWeek.contains(6) &&
        daysOfWeek.contains(7)) {
      return 'عطلة نهاية الأسبوع';
    }

    List<String> dayNames = [];
    for (int day in daysOfWeek) {
      switch (day) {
        case 1:
          dayNames.add('الإثنين');
          break;
        case 2:
          dayNames.add('الثلاثاء');
          break;
        case 3:
          dayNames.add('الأربعاء');
          break;
        case 4:
          dayNames.add('الخميس');
          break;
        case 5:
          dayNames.add('الجمعة');
          break;
        case 6:
          dayNames.add('السبت');
          break;
        case 7:
          dayNames.add('الأحد');
          break;
      }
    }
    return dayNames.join(', ');
  }

  String get statusText => isActive ? 'نشط' : 'غير نشط';

  // Check if reminder is due today
  bool get isDueToday {
    if (!isActive) return false;
    final today = DateTime.now().weekday;
    final todayAdjusted = today == 7 ? 7 : today; // Sunday = 7
    return daysOfWeek.contains(todayAdjusted);
  }

  // Get next occurrence
  DateTime? get nextOccurrence {
    if (!isActive) return null;

    try {
      final parts = reminderTime.split(':');
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);

      final now = DateTime.now();
      final today = now.weekday;
      final todayAdjusted = today == 7 ? 7 : today;

      // Check if it's due today and hasn't passed yet
      if (daysOfWeek.contains(todayAdjusted)) {
        final todayReminder = DateTime(
          now.year,
          now.month,
          now.day,
          hour,
          minute,
        );
        if (todayReminder.isAfter(now)) {
          return todayReminder;
        }
      }

      // Find next day
      for (int i = 1; i <= 7; i++) {
        final nextDay = (todayAdjusted + i - 1) % 7 + 1;
        if (daysOfWeek.contains(nextDay)) {
          final nextDate = now.add(Duration(days: i));
          return DateTime(
            nextDate.year,
            nextDate.month,
            nextDate.day,
            hour,
            minute,
          );
        }
      }
    } catch (e) {
      return null;
    }

    return null;
  }

  String get nextOccurrenceText {
    final next = nextOccurrence;
    if (next == null) return 'غير محدد';

    final now = DateTime.now();
    final difference = next.difference(now);

    if (difference.inDays == 0) {
      return 'اليوم في $displayTime';
    } else if (difference.inDays == 1) {
      return 'غداً في $displayTime';
    } else if (difference.inDays < 7) {
      return 'خلال ${difference.inDays} أيام في $displayTime';
    } else {
      return '${next.day}/${next.month} في $displayTime';
    }
  }

  // Get color based on reminder type
  String get colorCode {
    switch (reminderType.toLowerCase()) {
      case 'medication':
        return '#4CAF50'; // Green
      case 'supplement':
        return '#2196F3'; // Blue
      case 'exercise':
        return '#FF5722'; // Deep Orange
      case 'meal':
        return '#FF9800'; // Orange
      case 'water':
        return '#00BCD4'; // Cyan
      case 'appointment':
        return '#9C27B0'; // Purple
      case 'measurement':
        return '#607D8B'; // Blue Grey
      case 'sleep':
        return '#3F51B5'; // Indigo
      default:
        return '#757575'; // Grey
    }
  }

  // Get icon based on reminder type
  String get iconName {
    switch (reminderType.toLowerCase()) {
      case 'medication':
        return 'medication';
      case 'supplement':
        return 'vitamins';
      case 'exercise':
        return 'fitness_center';
      case 'meal':
        return 'restaurant';
      case 'water':
        return 'water_drop';
      case 'appointment':
        return 'event';
      case 'measurement':
        return 'monitor_weight';
      case 'sleep':
        return 'bedtime';
      default:
        return 'notifications';
    }
  }
}
