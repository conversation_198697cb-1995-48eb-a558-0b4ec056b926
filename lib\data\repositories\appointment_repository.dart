import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/appointment.dart';
import '../../core/errors/failures.dart';

/// Repository للتعامل مع بيانات المواعيد في Supabase
class AppointmentRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// إنشاء موعد جديد
  Future<Appointment> createAppointment({
    required String patientId,
    required DateTime appointmentDate,
    required String timeSlot,
    required String type,
    String? notes,
    String? doctorId,
  }) async {
    try {
      final response = await _supabase
          .from('appointments')
          .insert({
            'patient_id': patientId,
            'appointment_date': appointmentDate.toIso8601String(),
            'time_slot': timeSlot,
            'type': type,
            'notes': notes,
            'doctor_id': doctorId,
            'status': 'scheduled',
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();

      return Appointment.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في إنشاء الموعد: ${e.toString()}');
    }
  }

  /// الحصول على مواعيد المريض
  Future<List<Appointment>> getPatientAppointments({
    required String patientId,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    try {
      var query = _supabase
          .from('appointments')
          .select()
          .eq('patient_id', patientId);

      if (status != null) {
        query = query.eq('status', status);
      }

      if (fromDate != null) {
        query = query.gte('appointment_date', fromDate.toIso8601String());
      }

      if (toDate != null) {
        query = query.lte('appointment_date', toDate.toIso8601String());
      }

      var orderedQuery = query.order('appointment_date', ascending: true);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await orderedQuery;

      return response.map((json) => Appointment.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب المواعيد: ${e.toString()}');
    }
  }

  /// الحصول على موعد بواسطة ID
  Future<Appointment?> getAppointmentById(String appointmentId) async {
    try {
      final response = await _supabase
          .from('appointments')
          .select()
          .eq('id', appointmentId)
          .maybeSingle();

      if (response == null) return null;

      return Appointment.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب الموعد: ${e.toString()}');
    }
  }

  /// تحديث موعد
  Future<Appointment> updateAppointment({
    required String appointmentId,
    DateTime? appointmentDate,
    String? timeSlot,
    String? type,
    String? status,
    String? notes,
    String? doctorId,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (appointmentDate != null) {
        updateData['appointment_date'] = appointmentDate.toIso8601String();
      }
      if (timeSlot != null) updateData['time_slot'] = timeSlot;
      if (type != null) updateData['type'] = type;
      if (status != null) updateData['status'] = status;
      if (notes != null) updateData['notes'] = notes;
      if (doctorId != null) updateData['doctor_id'] = doctorId;

      final response = await _supabase
          .from('appointments')
          .update(updateData)
          .eq('id', appointmentId)
          .select()
          .single();

      return Appointment.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في تحديث الموعد: ${e.toString()}');
    }
  }

  /// إلغاء موعد
  Future<Appointment> cancelAppointment(String appointmentId) async {
    try {
      final response = await _supabase
          .from('appointments')
          .update({
            'status': 'cancelled',
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', appointmentId)
          .select()
          .single();

      return Appointment.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في إلغاء الموعد: ${e.toString()}');
    }
  }

  /// حذف موعد
  Future<void> deleteAppointment(String appointmentId) async {
    try {
      await _supabase
          .from('appointments')
          .delete()
          .eq('id', appointmentId);
    } catch (e) {
      throw ServerFailure(message: 'فشل في حذف الموعد: ${e.toString()}');
    }
  }

  /// الحصول على الأوقات المتاحة لتاريخ معين
  Future<List<String>> getAvailableTimeSlots(DateTime date) async {
    try {
      // الحصول على الأوقات المحجوزة لهذا التاريخ
      final bookedSlots = await _supabase
          .from('appointments')
          .select('time_slot')
          .eq('appointment_date', date.toIso8601String().split('T')[0])
          .neq('status', 'cancelled');

      final bookedTimes = bookedSlots.map((slot) => slot['time_slot'] as String).toList();

      // الأوقات المتاحة (يمكن تخصيصها حسب ساعات العمل)
      const allTimeSlots = [
        '09:00',
        '09:30',
        '10:00',
        '10:30',
        '11:00',
        '11:30',
        '12:00',
        '12:30',
        '14:00',
        '14:30',
        '15:00',
        '15:30',
        '16:00',
        '16:30',
        '17:00',
        '17:30',
      ];

      // إرجاع الأوقات غير المحجوزة
      return allTimeSlots.where((slot) => !bookedTimes.contains(slot)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب الأوقات المتاحة: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات المواعيد
  Future<Map<String, int>> getAppointmentStats(String patientId) async {
    try {
      final response = await _supabase
          .from('appointments')
          .select('status')
          .eq('patient_id', patientId);

      final stats = <String, int>{
        'total': response.length,
        'scheduled': 0,
        'completed': 0,
        'cancelled': 0,
      };

      for (final appointment in response) {
        final status = appointment['status'] as String;
        stats[status] = (stats[status] ?? 0) + 1;
      }

      return stats;
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب إحصائيات المواعيد: ${e.toString()}');
    }
  }

  /// البحث في المواعيد
  Future<List<Appointment>> searchAppointments({
    required String patientId,
    String? searchTerm,
    String? status,
    String? type,
  }) async {
    try {
      var query = _supabase
          .from('appointments')
          .select()
          .eq('patient_id', patientId);

      if (status != null) {
        query = query.eq('status', status);
      }

      if (type != null) {
        query = query.eq('type', type);
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        query = query.or('notes.ilike.%$searchTerm%,type.ilike.%$searchTerm%');
      }

      final response = await query.order('appointment_date', ascending: false);

      return response.map((json) => Appointment.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في البحث في المواعيد: ${e.toString()}');
    }
  }
}
