import 'package:equatable/equatable.dart';

/// كيان التحليل الطبي
class LabTest extends Equatable {
  /// معرف التحليل
  final String id;

  /// معرف المريض
  final String patientId;

  /// اسم التحليل
  final String testName;

  /// نوع التحليل
  final String testType;

  /// تاريخ التحليل
  final DateTime testDate;

  /// اسم المختبر
  final String? labName;

  /// معرف الطبيب
  final String? doctorId;

  /// اسم الطبيب
  final String? doctorName;

  /// نتائج التحليل
  final Map<String, dynamic>? results;

  /// المعدلات الطبيعية
  final Map<String, dynamic>? referenceRanges;

  /// حالة التحليل
  final String status;

  /// الملاحظات
  final String? notes;

  /// المرفقات
  final List<String>? attachments;

  /// تاريخ الإنشاء
  final DateTime? createdAt;

  /// تاريخ التحديث
  final DateTime? updatedAt;

  const LabTest({
    required this.id,
    required this.patientId,
    required this.testName,
    required this.testType,
    required this.testDate,
    required this.status,
    this.labName,
    this.doctorId,
    this.doctorName,
    this.results,
    this.referenceRanges,
    this.notes,
    this.attachments,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    patientId,
    testName,
    testType,
    testDate,
    labName,
    doctorId,
    doctorName,
    results,
    referenceRanges,
    status,
    notes,
    attachments,
    createdAt,
    updatedAt,
  ];

  /// إنشاء LabTest من JSON
  factory LabTest.fromJson(Map<String, dynamic> json) {
    return LabTest(
      id: json['id']?.toString() ?? '',
      patientId: json['patient_id']?.toString() ?? '',
      testName: json['test_name']?.toString() ?? '',
      testType: json['test_type']?.toString() ?? '',
      testDate:
          json['test_date'] != null
              ? DateTime.parse(json['test_date'].toString())
              : DateTime.now(),
      status: json['status']?.toString() ?? 'pending',
      labName: json['lab_name']?.toString(),
      doctorId: json['doctor_id']?.toString(),
      doctorName: json['doctor_name']?.toString(),
      results: json['results'] as Map<String, dynamic>?,
      referenceRanges: json['reference_ranges'] as Map<String, dynamic>?,
      notes: json['notes']?.toString(),
      attachments:
          json['attachments'] != null
              ? List<String>.from(json['attachments'])
              : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'].toString())
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'].toString())
              : null,
    );
  }

  /// تحويل LabTest إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'test_name': testName,
      'test_type': testType,
      'test_date': testDate.toIso8601String(),
      'lab_name': labName,
      'doctor_id': doctorId,
      'doctor_name': doctorName,
      'results': results,
      'reference_ranges': referenceRanges,
      'status': status,
      'notes': notes,
      'attachments': attachments,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من LabTest
  LabTest copyWith({
    String? id,
    String? patientId,
    String? testName,
    String? testType,
    DateTime? testDate,
    String? labName,
    String? doctorId,
    String? doctorName,
    Map<String, dynamic>? results,
    Map<String, dynamic>? referenceRanges,
    String? status,
    String? notes,
    List<String>? attachments,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LabTest(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      testName: testName ?? this.testName,
      testType: testType ?? this.testType,
      testDate: testDate ?? this.testDate,
      labName: labName ?? this.labName,
      doctorId: doctorId ?? this.doctorId,
      doctorName: doctorName ?? this.doctorName,
      results: results ?? this.results,
      referenceRanges: referenceRanges ?? this.referenceRanges,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      attachments: attachments ?? this.attachments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من اكتمال التحليل
  bool get isCompleted {
    return status.toLowerCase() == 'completed';
  }

  /// التحقق من أن التحليل معلق
  bool get isPending {
    return status.toLowerCase() == 'pending';
  }

  /// التحقق من إلغاء التحليل
  bool get isCancelled {
    return status.toLowerCase() == 'cancelled';
  }

  /// التحقق من وجود نتائج
  bool get hasResults {
    return results != null && results!.isNotEmpty;
  }

  /// التحقق من وجود مرفقات
  bool get hasAttachments {
    return attachments != null && attachments!.isNotEmpty;
  }

  /// عدد المرفقات
  int get attachmentsCount {
    return attachments?.length ?? 0;
  }

  /// الحصول على قيمة نتيجة محددة
  T? getResultValue<T>(String key) {
    if (results == null) return null;
    return results![key] as T?;
  }

  /// الحصول على المعدل الطبيعي لقيمة محددة
  T? getReferenceRange<T>(String key) {
    if (referenceRanges == null) return null;
    return referenceRanges![key] as T?;
  }

  /// تنسيق تاريخ التحليل
  String get formattedTestDate {
    return '${testDate.day}/${testDate.month}/${testDate.year}';
  }

  /// تنسيق وقت التحليل
  String get formattedTestTime {
    return '${testDate.hour.toString().padLeft(2, '0')}:${testDate.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق تاريخ ووقت التحليل
  String get formattedTestDateTime {
    return '$formattedTestDate - $formattedTestTime';
  }

  /// الحصول على لون الحالة
  String get statusColor {
    switch (status.toLowerCase()) {
      case 'completed':
        return '#4CAF50'; // أخضر
      case 'pending':
        return '#FF9800'; // برتقالي
      case 'cancelled':
        return '#F44336'; // أحمر
      case 'in_progress':
        return '#2196F3'; // أزرق
      default:
        return '#607D8B'; // رمادي
    }
  }

  /// الحصول على أيقونة الحالة
  String get statusIcon {
    switch (status.toLowerCase()) {
      case 'completed':
        return '✅';
      case 'pending':
        return '⏳';
      case 'cancelled':
        return '❌';
      case 'in_progress':
        return '🔄';
      default:
        return '📋';
    }
  }

  /// الحصول على نص الحالة بالعربية
  String get statusText {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'مكتمل';
      case 'pending':
        return 'معلق';
      case 'cancelled':
        return 'ملغي';
      case 'in_progress':
        return 'قيد التنفيذ';
      default:
        return 'غير محدد';
    }
  }

  /// الحصول على أيقونة نوع التحليل
  String get testTypeIcon {
    switch (testType.toLowerCase()) {
      case 'blood':
      case 'دم':
        return '🩸';
      case 'urine':
      case 'بول':
        return '🧪';
      case 'stool':
      case 'براز':
        return '💩';
      case 'imaging':
      case 'تصوير':
        return '📷';
      case 'biopsy':
      case 'خزعة':
        return '🔬';
      default:
        return '🧬';
    }
  }

  /// التحقق من أن التحليل حديث (خلال آخر 7 أيام)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(testDate);
    return difference.inDays <= 7;
  }

  /// التحقق من أن التحليل قديم (أكثر من 6 أشهر)
  bool get isOld {
    final now = DateTime.now();
    final difference = now.difference(testDate);
    return difference.inDays > 180;
  }

  /// التحقق من وجود قيم غير طبيعية في النتائج
  bool get hasAbnormalResults {
    if (!hasResults || referenceRanges == null) return false;

    // هذا مثال بسيط - يمكن تطويره حسب نوع التحليل
    for (final key in results!.keys) {
      final value = results![key];
      final range = referenceRanges![key];

      if (value is num && range is Map) {
        final min = range['min'] as num?;
        final max = range['max'] as num?;

        if (min != null && value < min) return true;
        if (max != null && value > max) return true;
      }
    }

    return false;
  }

  @override
  String toString() {
    return 'LabTest(id: $id, testName: $testName, testType: $testType, status: $status, testDate: $testDate)';
  }
}
