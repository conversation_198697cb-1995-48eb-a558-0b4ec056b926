import '../constants/app_constants.dart';
import '../constants/app_strings.dart';
import 'app_logger.dart';

/// فئة التحقق من صحة البيانات
class Validators {
  Validators._();

  /// تسجيل نتيجة التحقق
  static void _logValidation(String field, String? value, String? error) {
    if (error != null) {
      AppLogger.warning('❌ Validation failed',
        category: LogCategory.ui,
        data: {
          'field': field,
          'value': value?.length.toString() ?? 'null',
          'error': error,
        }
      );
    }
  }

  /// التحقق من صحة البريد الإلكتروني
  static String? validateEmail(String? value) {
    String? error;

    if (value == null || value.isEmpty) {
      error = AppStrings.emailRequired;
    } else {
      final emailRegExp = RegExp(AppConstants.emailRegex);
      if (!emailRegExp.hasMatch(value)) {
        error = AppStrings.emailInvalid;
      }
    }

    _logValidation('email', value, error);
    return error;
  }

  /// التحقق من صحة كلمة المرور
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.passwordRequired;
    }

    if (value.length < AppConstants.minPasswordLength) {
      return AppStrings.passwordTooShort;
    }

    if (value.length > AppConstants.maxPasswordLength) {
      return 'كلمة المرور طويلة جداً';
    }

    return null;
  }

  /// التحقق من تطابق كلمات المرور
  static String? validateConfirmPassword(String? value, String? password) {
    if (value == null || value.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }

    if (value != password) {
      return AppStrings.passwordsNotMatch;
    }

    return null;
  }

  /// التحقق من صحة الاسم
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.nameRequired;
    }

    if (value.length < AppConstants.minNameLength) {
      return 'الاسم قصير جداً';
    }

    if (value.length > AppConstants.maxNameLength) {
      return 'الاسم طويل جداً';
    }

    final nameRegExp = RegExp(AppConstants.nameRegex);
    if (!nameRegExp.hasMatch(value)) {
      return 'الاسم يحتوي على أحرف غير صحيحة';
    }

    return null;
  }

  /// التحقق من صحة رقم الهاتف
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return AppStrings.phoneRequired;
    }

    final phoneRegExp = RegExp(AppConstants.phoneRegex);
    if (!phoneRegExp.hasMatch(value)) {
      return AppStrings.phoneInvalid;
    }

    return null;
  }

  /// التحقق من صحة العمر
  static String? validateAge(String? value) {
    if (value == null || value.isEmpty) {
      return 'العمر مطلوب';
    }

    final age = int.tryParse(value);
    if (age == null) {
      return 'العمر يجب أن يكون رقماً';
    }

    if (age < 1 || age > 120) {
      return 'العمر غير صحيح';
    }

    return null;
  }

  /// التحقق من صحة الطول
  static String? validateHeight(String? value) {
    if (value == null || value.isEmpty) {
      return 'الطول مطلوب';
    }

    final height = double.tryParse(value);
    if (height == null) {
      return 'الطول يجب أن يكون رقماً';
    }

    if (height < 50 || height > 250) {
      return 'الطول غير صحيح';
    }

    return null;
  }

  /// التحقق من صحة الوزن
  static String? validateWeight(String? value) {
    if (value == null || value.isEmpty) {
      return 'الوزن مطلوب';
    }

    final weight = double.tryParse(value);
    if (weight == null) {
      return 'الوزن يجب أن يكون رقماً';
    }

    if (weight < 20 || weight > 300) {
      return 'الوزن غير صحيح';
    }

    return null;
  }

  /// التحقق من صحة السعر
  static String? validatePrice(String? value) {
    if (value == null || value.isEmpty) {
      return 'السعر مطلوب';
    }

    final price = double.tryParse(value);
    if (price == null) {
      return 'السعر يجب أن يكون رقماً';
    }

    if (price < 0) {
      return 'السعر لا يمكن أن يكون سالباً';
    }

    return null;
  }

  /// التحقق من صحة النسبة المئوية
  static String? validatePercentage(String? value) {
    if (value == null || value.isEmpty) {
      return 'النسبة مطلوبة';
    }

    final percentage = double.tryParse(value);
    if (percentage == null) {
      return 'النسبة يجب أن تكون رقماً';
    }

    if (percentage < 0 || percentage > 100) {
      return 'النسبة يجب أن تكون بين 0 و 100';
    }

    return null;
  }

  /// التحقق من صحة الملاحظات
  static String? validateNotes(String? value) {
    if (value != null && value.length > AppConstants.maxNotesLength) {
      return 'الملاحظات طويلة جداً';
    }

    return null;
  }

  /// التحقق من صحة التاريخ
  static String? validateDate(DateTime? value) {
    if (value == null) {
      return 'التاريخ مطلوب';
    }

    final now = DateTime.now();
    if (value.isAfter(now)) {
      return 'التاريخ لا يمكن أن يكون في المستقبل';
    }

    return null;
  }

  /// التحقق من صحة تاريخ الميلاد
  static String? validateBirthDate(DateTime? value) {
    if (value == null) {
      return 'تاريخ الميلاد مطلوب';
    }

    final now = DateTime.now();
    if (value.isAfter(now)) {
      return 'تاريخ الميلاد لا يمكن أن يكون في المستقبل';
    }

    final age = now.difference(value).inDays ~/ 365;
    if (age < 1 || age > 120) {
      return 'تاريخ الميلاد غير صحيح';
    }

    return null;
  }

  /// التحقق من صحة تاريخ الموعد
  static String? validateAppointmentDate(DateTime? value) {
    if (value == null) {
      return 'تاريخ الموعد مطلوب';
    }

    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final appointmentDate = DateTime(value.year, value.month, value.day);

    if (appointmentDate.isBefore(today)) {
      return 'تاريخ الموعد لا يمكن أن يكون في الماضي';
    }

    return null;
  }

  /// التحقق من صحة الحقل المطلوب
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName مطلوب';
    }

    return null;
  }

  /// التحقق من صحة الحد الأدنى للطول
  static String? validateMinLength(String? value, int minLength, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName مطلوب';
    }

    if (value.length < minLength) {
      return '$fieldName يجب أن يكون على الأقل $minLength أحرف';
    }

    return null;
  }

  /// التحقق من صحة الحد الأقصى للطول
  static String? validateMaxLength(String? value, int maxLength, String fieldName) {
    if (value != null && value.length > maxLength) {
      return '$fieldName يجب أن يكون أقل من $maxLength حرف';
    }

    return null;
  }

  /// التحقق من صحة النطاق الرقمي
  static String? validateNumberRange(String? value, double min, double max, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName مطلوب';
    }

    final number = double.tryParse(value);
    if (number == null) {
      return '$fieldName يجب أن يكون رقماً';
    }

    if (number < min || number > max) {
      return '$fieldName يجب أن يكون بين $min و $max';
    }

    return null;
  }

  /// التحقق من صحة الرقم الموجب
  static String? validatePositiveNumber(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName مطلوب';
    }

    final number = double.tryParse(value);
    if (number == null) {
      return '$fieldName يجب أن يكون رقماً';
    }

    if (number <= 0) {
      return '$fieldName يجب أن يكون رقماً موجباً';
    }

    return null;
  }
}
