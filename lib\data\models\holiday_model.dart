class HolidayModel {
  final String id;
  final DateTime holidayDate;
  final String occasionName;
  final String holidayType;
  final bool isActive;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  HolidayModel({
    required this.id,
    required this.holidayDate,
    required this.occasionName,
    required this.holidayType,
    required this.isActive,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory HolidayModel.fromJson(Map<String, dynamic> json) {
    return HolidayModel(
      id: json['id'] ?? '',
      holidayDate: DateTime.parse(json['holiday_date']),
      occasionName: json['occasion_name'] ?? '',
      holidayType: json['holiday_type'] ?? '',
      isActive: json['is_active'] ?? false,
      notes: json['notes'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'holiday_date': holidayDate.toIso8601String().split('T')[0],
      'occasion_name': occasionName,
      'holiday_type': holidayType,
      'is_active': isActive,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isOfficial => holidayType == 'official';
  bool get isMaintenance => holidayType == 'maintenance';
  bool get isEmergency => holidayType == 'emergency';

  String get displayHolidayDate {
    return '${holidayDate.day}/${holidayDate.month}/${holidayDate.year}';
  }
}
