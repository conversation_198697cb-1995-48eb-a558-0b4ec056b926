import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/utils/app_logger.dart';

/// صفحة إعدادات الإشعارات
class NotificationSettingsPage extends StatefulWidget {
  const NotificationSettingsPage({super.key});

  @override
  State<NotificationSettingsPage> createState() => _NotificationSettingsPageState();
}

class _NotificationSettingsPageState extends State<NotificationSettingsPage> {
  bool _appointmentNotifications = true;
  bool _medicationReminders = true;
  bool _mealReminders = false;
  bool _exerciseReminders = false;
  bool _waterReminders = true;
  bool _generalNotifications = true;
  bool _promotionalNotifications = false;

  @override
  void initState() {
    super.initState();
    AppLogger.info('Notification Settings page opened', category: LogCategory.ui);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('إعدادات الإشعارات'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم الإشعارات الطبية
            _buildSectionTitle('الإشعارات الطبية'),
            SizedBox(height: 16.h),
            
            _buildNotificationTile(
              title: 'تذكيرات المواعيد',
              subtitle: 'تلقي إشعارات قبل المواعيد المحجوزة',
              value: _appointmentNotifications,
              onChanged: (value) {
                setState(() {
                  _appointmentNotifications = value;
                });
                _logNotificationChange('appointment_notifications', value);
              },
            ),
            
            _buildNotificationTile(
              title: 'تذكيرات الأدوية',
              subtitle: 'تلقي إشعارات لمواعيد تناول الأدوية',
              value: _medicationReminders,
              onChanged: (value) {
                setState(() {
                  _medicationReminders = value;
                });
                _logNotificationChange('medication_reminders', value);
              },
            ),
            
            SizedBox(height: 24.h),
            
            // قسم إشعارات نمط الحياة
            _buildSectionTitle('إشعارات نمط الحياة'),
            SizedBox(height: 16.h),
            
            _buildNotificationTile(
              title: 'تذكيرات الوجبات',
              subtitle: 'تلقي إشعارات لمواعيد الوجبات',
              value: _mealReminders,
              onChanged: (value) {
                setState(() {
                  _mealReminders = value;
                });
                _logNotificationChange('meal_reminders', value);
              },
            ),
            
            _buildNotificationTile(
              title: 'تذكيرات التمارين',
              subtitle: 'تلقي إشعارات لممارسة التمارين',
              value: _exerciseReminders,
              onChanged: (value) {
                setState(() {
                  _exerciseReminders = value;
                });
                _logNotificationChange('exercise_reminders', value);
              },
            ),
            
            _buildNotificationTile(
              title: 'تذكيرات شرب الماء',
              subtitle: 'تلقي إشعارات لشرب الماء',
              value: _waterReminders,
              onChanged: (value) {
                setState(() {
                  _waterReminders = value;
                });
                _logNotificationChange('water_reminders', value);
              },
            ),
            
            SizedBox(height: 24.h),
            
            // قسم الإشعارات العامة
            _buildSectionTitle('الإشعارات العامة'),
            SizedBox(height: 16.h),
            
            _buildNotificationTile(
              title: 'الإشعارات العامة',
              subtitle: 'تلقي إشعارات حول التطبيق والخدمات',
              value: _generalNotifications,
              onChanged: (value) {
                setState(() {
                  _generalNotifications = value;
                });
                _logNotificationChange('general_notifications', value);
              },
            ),
            
            _buildNotificationTile(
              title: 'الإشعارات الترويجية',
              subtitle: 'تلقي إشعارات حول العروض والخصومات',
              value: _promotionalNotifications,
              onChanged: (value) {
                setState(() {
                  _promotionalNotifications = value;
                });
                _logNotificationChange('promotional_notifications', value);
              },
            ),
            
            SizedBox(height: 32.h),
            
            // زر حفظ الإعدادات
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saveSettings,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  'حفظ الإعدادات',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textDark,
      ),
    );
  }

  Widget _buildNotificationTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.shadow,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textDark,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: AppColors.textLight,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppColors.primary,
          ),
        ],
      ),
    );
  }

  void _logNotificationChange(String setting, bool value) {
    AppLogger.info('Notification setting changed', 
      category: LogCategory.ui,
      data: {
        'setting': setting,
        'value': value.toString(),
      }
    );
  }

  void _saveSettings() {
    AppLogger.info('Notification settings saved', 
      category: LogCategory.ui,
      data: {
        'appointment_notifications': _appointmentNotifications.toString(),
        'medication_reminders': _medicationReminders.toString(),
        'meal_reminders': _mealReminders.toString(),
        'exercise_reminders': _exerciseReminders.toString(),
        'water_reminders': _waterReminders.toString(),
        'general_notifications': _generalNotifications.toString(),
        'promotional_notifications': _promotionalNotifications.toString(),
      }
    );
    
    // تنفيذ حفظ الإعدادات
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم حفظ إعدادات الإشعارات بنجاح'),
        backgroundColor: AppColors.success,
      ),
    );
    
    Navigator.pop(context);
  }
}
