{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\projects\\diet_rx\\deit_rx\\android\\app\\.cxx\\RelWithDebInfo\\u1j6w4i4\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "E:\\projects\\diet_rx\\deit_rx\\android\\app\\.cxx\\RelWithDebInfo\\u1j6w4i4\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}