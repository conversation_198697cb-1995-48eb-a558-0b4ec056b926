# عيادة التغذية - Diet RX

تطبيق Flutter متكامل لإدارة عيادة التغذية والدايت مع ميزات متقدمة للمرضى والأطباء.

## 🌟 المميزات الرئيسية

### للمستخدمين العاديين:
- 📅 **حجز المواعيد**: نظام حجز مواعيد سهل ومرن
- 🛒 **متجر المنتجات**: عرض وشراء المكملات الغذائية والمنتجات الصحية
- 📚 **المقالات**: مقالات تثقيفية حول التغذية والصحة
- 👤 **الملف الشخصي**: إدارة المعلومات الشخصية والطبية

### للمستخدمين المميزين (إضافية):
- 🏥 **السجل الطبي**: متابعة النتائج الأسبوعية والفحوصات المخبرية
- 🤖 **تحليل الطعام بالذكاء الاصطناعي**: تحليل الوجبات باستخدام OpenAI GPT-4
- 📊 **تقارير مفصلة**: رسوم بيانية للتقدم والنتائج
- 🔔 **تذكيرات متقدمة**: تذكيرات للوجبات والأدوية والنشاط البدني

## 🛠️ التقنيات المستخدمة

### Frontend:
- **Flutter** - إطار العمل الأساسي
- **Dart** - لغة البرمجة
- **BLoC** - إدارة الحالات
- **Clean Architecture** - هندسة البرمجيات

### Backend & Database:
- **Supabase** - قاعدة البيانات والمصادقة
- **PostgreSQL** - قاعدة البيانات الأساسية

### AI & APIs:
- **OpenAI GPT-4.1-nano** - تحليل الطعام
- **REST APIs** - التواصل مع الخدمات

### UI/UX:
- **Material Design 3** - نظام التصميم
- **RTL Support** - دعم اللغة العربية
- **Responsive Design** - تصميم متجاوب
- **Custom Animations** - رسوم متحركة مخصصة

## 🎨 نظام الألوان

- **الأساسي**: `#80AC5C` (أخضر طبيعي)
- **الثانوي**: `#B4BCB0` (رمادي فاتح)
- **الأبيض**: `#FFFFFF`
- **الأسود**: `#000000`

## 📱 الشاشات والصفحات

### المصادقة:
- صفحة تسجيل الدخول
- صفحة إنشاء الحساب
- استعادة كلمة المرور

### الصفحات الأساسية:
- الصفحة الرئيسية (للمميزين)
- المواعيد (حجز + مواعيدي)
- المنتجات (متجر احترافي)
- المقالات (مصنفة بفئات)
- الملف الشخصي

### الصفحات المتقدمة (للمميزين):
- السجل الطبي
- تحليل الطعام بالذكاء الاصطناعي
- التقارير والإحصائيات

## 🏗️ هيكل المشروع

```
lib/
├── core/                    # الطبقة الأساسية
│   ├── constants/          # الثوابت والألوان
│   ├── errors/             # معالجة الأخطاء
│   ├── network/            # طبقة الشبكة
│   ├── services/           # الخدمات (OpenAI, etc.)
│   ├── themes/             # الثيمات والتصميم
│   └── utils/              # الأدوات المساعدة
├── data/                   # طبقة البيانات
│   ├── models/             # نماذج البيانات
│   ├── repositories/       # تنفيذ المستودعات
│   └── datasources/        # مصادر البيانات
├── domain/                 # طبقة المنطق
│   ├── entities/           # الكيانات
│   ├── repositories/       # واجهات المستودعات
│   └── usecases/           # حالات الاستخدام
└── presentation/           # طبقة العرض
    ├── bloc/               # إدارة الحالات
    ├── pages/              # الصفحات
    └── widgets/            # الويدجتس المشتركة
```

## 🚀 التشغيل والتطوير

### المتطلبات:
- Flutter SDK (3.7.2+)
- Dart SDK
- Android Studio / VS Code
- حساب Supabase
- مفتاح OpenAI API

### خطوات التشغيل:

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd deit_rx
```

2. **تثبيت المكتبات:**
```bash
flutter pub get
```

3. **إعداد المتغيرات:**
- تحديث `AppConstants` بمعلومات Supabase
- إضافة مفتاح OpenAI API

4. **تشغيل التطبيق:**
```bash
flutter run
```

## 🗄️ قاعدة البيانات

### الجداول الرئيسية:
- `patients` - معلومات المرضى
- `appointments` - المواعيد
- `products` - المنتجات
- `articles` - المقالات
- `weekly_results` - النتائج الأسبوعية
- `lab_tests` - الفحوصات المخبرية
- `medical_records` - السجلات الطبية
- `reminders` - التذكيرات

## 🔐 الأمان والخصوصية

- مصادقة آمنة مع Supabase Auth
- تشفير البيانات الحساسة
- Row Level Security (RLS)
- التحقق من الصلاحيات
- حماية API Keys

## 🌍 الدعم اللغوي

- **اللغة العربية**: دعم كامل مع RTL
- **الخطوط**: Cairo Font للنصوص العربية
- **التوطين**: جميع النصوص باللغة العربية

## 📊 الميزات المتقدمة

### تحليل الطعام بالذكاء الاصطناعي:
- تحليل الصور باستخدام OpenAI Vision
- استخراج المعلومات الغذائية
- تقييم صحة الطعام
- توصيات غذائية مخصصة

### نظام التذكيرات:
- تذكيرات المواعيد
- تذكيرات الوجبات
- تذكيرات شرب الماء
- تذكيرات الأدوية والمكملات
- تذكيرات النشاط البدني

## 🎯 المستخدمون المستهدفون

- **المرضى**: حجز المواعيد ومتابعة الحالة الصحية
- **المستخدمون المميزون**: ميزات متقدمة وتحليل شامل
- **عيادات التغذية**: إدارة المرضى والمواعيد

## 📈 خطط التطوير المستقبلية

- [ ] إضافة Firebase للإشعارات
- [ ] تطبيق ويب للأطباء
- [ ] تكامل مع أجهزة القياس الذكية
- [ ] نظام الدفع الإلكتروني
- [ ] تقارير PDF
- [ ] دعم لغات إضافية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة دليل المساهمة قبل البدء.

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 التواصل

للاستفسارات والدعم الفني، يرجى التواصل معنا.

---

**تم تطويره بـ ❤️ باستخدام Flutter**
