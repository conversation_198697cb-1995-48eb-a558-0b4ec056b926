import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../data/models/clinic_info_model.dart';
import '../../../../data/services/appointments_service.dart';

class ClinicInfoTab extends StatefulWidget {
  const ClinicInfoTab({super.key});

  @override
  State<ClinicInfoTab> createState() => _ClinicInfoTabState();
}

class _ClinicInfoTabState extends State<ClinicInfoTab> {
  final AppointmentsService _appointmentsService = AppointmentsService();
  List<ClinicInfoModel> _clinicInfo = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadClinicInfo();
  }

  Future<void> _loadClinicInfo() async {
    try {
      final clinicInfo = await _appointmentsService.getClinicInfo();
      setState(() {
        _clinicInfo = clinicInfo;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل معلومات العيادة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_clinicInfo.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadClinicInfo,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildInfoSections()],
        ),
      ),
    );
  }

  Widget _buildClinicHeaderCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Row(
          children: [
            // صورة السنتر بنفس تصميم الملف الشخصي
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppColors.primary, width: 4.w),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: CircleAvatar(
                radius: 30.r,
                backgroundImage: const AssetImage('assets/images/logo.jpeg'),
                backgroundColor: AppColors.white,
              ),
            ),
            SizedBox(width: 16.w),

            // معلومات السنتر
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'DietRx',
                    style: TextStyle(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'سنتر متخصص في التغذية العلاجية والصحة العامة',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSections() {
    final groupedInfo = <String, List<ClinicInfoModel>>{};

    for (final info in _clinicInfo) {
      if (!groupedInfo.containsKey(info.infoType)) {
        groupedInfo[info.infoType] = [];
      }
      groupedInfo[info.infoType]!.add(info);
    }

    // ترتيب الأقسام حسب الطلب: أرقام التواصل، العنوان، مواعيد العمل، باقي الأقسام
    final orderedSections = <String>[];

    // إضافة أرقام التواصل أولاً
    if (groupedInfo.containsKey('phone')) {
      orderedSections.add('phone');
    }

    // إضافة العنوان ثانياً
    if (groupedInfo.containsKey('address')) {
      orderedSections.add('address');
    }

    // إضافة مواعيد العمل (معلومات أخرى) ثالثاً
    if (groupedInfo.containsKey('working_hours')) {
      orderedSections.add('working_hours');
    }

    // إضافة باقي الأقسام
    for (final key in groupedInfo.keys) {
      if (!orderedSections.contains(key)) {
        orderedSections.add(key);
      }
    }

    return Column(
      children: [
        // كارت معلومات العيادة كأول عنصر
        _buildClinicHeaderCard(),
        SizedBox(height: 16.h),

        // باقي الأقسام
        ...orderedSections.map((sectionType) {
          final items = groupedInfo[sectionType] ?? [];
          if (items.isEmpty) return const SizedBox.shrink();

          return Column(
            children: [
              _buildInfoSection(sectionType, items),
              SizedBox(height: 16.h),
            ],
          );
        }),
      ],
    );
  }

  Widget _buildInfoSection(String sectionType, List<ClinicInfoModel> items) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _getSectionIcon(sectionType),
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  _getSectionTitle(sectionType),
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            ...items.map((item) => _buildInfoItem(item)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(ClinicInfoModel info) {
    // تحديد ما إذا كان العنصر قابل للنقر (ليس مواعيد العمل)
    final isClickable = info.infoType != 'working_hours';

    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: InkWell(
        onTap: isClickable ? () => _launchUrl(info) : null,
        borderRadius: BorderRadius.circular(8.r),
        child: Container(
          padding: EdgeInsets.all(12.w),
          decoration: BoxDecoration(
            color: AppColors.background,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: AppColors.border),
          ),
          child: Row(
            children: [
              Icon(
                _getInfoIcon(info.iconName),
                color: AppColors.primary,
                size: 18.sp,
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      info.displayName,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      info.infoValue,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ),
              // إظهار أيقونة الفتح فقط للعناصر القابلة للنقر
              if (isClickable)
                Icon(Icons.launch, color: AppColors.textSecondary, size: 16.sp),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info_outline, size: 64.sp, color: AppColors.textSecondary),
          SizedBox(height: 16.h),
          Text(
            'لا توجد معلومات متاحة',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  IconData _getSectionIcon(String sectionType) {
    switch (sectionType) {
      case 'phone':
        return Icons.phone;
      case 'email':
        return Icons.email;
      case 'social_media':
        return Icons.share;
      case 'address':
        return Icons.location_on;
      case 'website':
        return Icons.language;
      case 'working_hours':
        return Icons.access_time;
      default:
        return Icons.info;
    }
  }

  String _getSectionTitle(String sectionType) {
    switch (sectionType) {
      case 'phone':
        return 'أرقام الهواتف';
      case 'email':
        return 'البريد الإلكتروني';
      case 'social_media':
        return 'وسائل التواصل الاجتماعي';
      case 'address':
        return 'العنوان';
      case 'website':
        return 'المواقع الإلكترونية';
      case 'working_hours':
        return 'مواعيد العمل';
      default:
        return 'معلومات أخرى';
    }
  }

  IconData _getInfoIcon(String iconName) {
    switch (iconName) {
      case 'phone_emergency':
        return Icons.emergency;
      case 'phone_reception':
        return Icons.phone;
      case 'email':
        return Icons.email;
      case 'facebook':
        return Icons.facebook;
      case 'instagram':
        return Icons.camera_alt;
      case 'twitter':
        return Icons.alternate_email;
      case 'whatsapp':
        return Icons.chat;
      case 'location':
        return Icons.location_on;
      case 'website':
        return Icons.language;
      default:
        return Icons.info;
    }
  }

  Future<void> _launchUrl(ClinicInfoModel info) async {
    try {
      final url = info.actionUrl;
      final uri = Uri.parse(url);

      // تحديد نوع الرابط ونمط الفتح المناسب
      LaunchMode mode = LaunchMode.externalApplication;

      if (info.isPhone) {
        // للهواتف، استخدم نمط الفتح الخارجي
        mode = LaunchMode.externalApplication;
      } else if (info.isEmail) {
        // للإيميل، استخدم نمط الفتح الخارجي
        mode = LaunchMode.externalApplication;
      } else if (info.isSocialMedia || info.isWebsite) {
        // للمواقع ووسائل التواصل، جرب الفتح في التطبيق أولاً
        mode = LaunchMode.externalApplication;
      }

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: mode);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم فتح ${info.displayName} بنجاح'),
              backgroundColor: AppColors.success,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        // إذا لم يتمكن من فتح الرابط، جرب نمط مختلف
        if (info.isSocialMedia || info.isWebsite) {
          await launchUrl(uri, mode: LaunchMode.inAppBrowserView);
        } else {
          throw 'لا يمكن فتح ${info.displayName}';
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح ${info.displayName}: ${e.toString()}'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }
}
