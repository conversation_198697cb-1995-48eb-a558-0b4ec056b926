part of 'article_bloc.dart';

/// حالات المقالات
abstract class ArticleState extends Equatable {
  const ArticleState();

  @override
  List<Object?> get props => [];
}

/// الحالة الأولية
class ArticleInitial extends ArticleState {}

/// حالة التحميل
class ArticleLoading extends ArticleState {}

/// حالة تحميل المقالات بنجاح
class ArticleLoaded extends ArticleState {
  final List<Article> articles;

  const ArticleLoaded({required this.articles});

  @override
  List<Object?> get props => [articles];
}

/// حالة تحميل المقالات المميزة
class FeaturedArticlesLoaded extends ArticleState {
  final List<Article> articles;

  const FeaturedArticlesLoaded({required this.articles});

  @override
  List<Object?> get props => [articles];
}

/// حالة تحميل المقالات حسب الفئة
class ArticlesByCategoryLoaded extends ArticleState {
  final String category;
  final List<Article> articles;

  const ArticlesByCategoryLoaded({
    required this.category,
    required this.articles,
  });

  @override
  List<Object?> get props => [category, articles];
}

/// حالة تحميل فئات المقالات
class ArticleCategoriesLoaded extends ArticleState {
  final List<String> categories;

  const ArticleCategoriesLoaded({required this.categories});

  @override
  List<Object?> get props => [categories];
}

/// حالة تحميل فئات المقالات مع العدد
class CategoriesWithCountLoaded extends ArticleState {
  final List<Map<String, dynamic>> categoriesWithCount;

  const CategoriesWithCountLoaded({required this.categoriesWithCount});

  @override
  List<Object?> get props => [categoriesWithCount];
}

/// حالة نتائج البحث
class ArticleSearchResults extends ArticleState {
  final List<Article> articles;

  const ArticleSearchResults({required this.articles});

  @override
  List<Object?> get props => [articles];
}

/// حالة تحميل تفاصيل المقال
class ArticleDetailLoaded extends ArticleState {
  final Article article;

  const ArticleDetailLoaded({required this.article});

  @override
  List<Object?> get props => [article];
}

/// حالة تحميل الإحصائيات
class ArticleStatsLoaded extends ArticleState {
  final Map<String, dynamic> stats;

  const ArticleStatsLoaded({required this.stats});

  @override
  List<Object?> get props => [stats];
}

/// حالة الخطأ
class ArticleError extends ArticleState {
  final Failure failure;

  const ArticleError({required this.failure});

  @override
  List<Object?> get props => [failure];
}
