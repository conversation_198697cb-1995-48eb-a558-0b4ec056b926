import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_constants.dart';

/// حقل نص مخصص
class CustomTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? label;
  final String? hint;
  final String? initialValue;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final void Function()? onTap;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final List<TextInputFormatter>? inputFormatters;
  final EdgeInsetsGeometry? contentPadding;
  final Color? fillColor;
  final Color? borderColor;
  final double? borderRadius;

  const CustomTextField({
    super.key,
    this.controller,
    this.label,
    this.hint,
    this.initialValue,
    this.keyboardType,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.focusNode,
    this.textInputAction,
    this.inputFormatters,
    this.contentPadding,
    this.fillColor,
    this.borderColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveContentPadding =
        contentPadding ??
        EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding.w,
          vertical: AppConstants.defaultPadding.h,
        );

    final effectiveBorderRadius = borderRadius ?? AppConstants.defaultRadius;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null) ...[
          Text(
            label!,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.textPrimary,
              fontFamily: 'Cairo',
            ),
          ),
          SizedBox(height: 8.h),
        ],
        TextFormField(
          controller: controller,
          initialValue: initialValue,
          keyboardType: keyboardType,
          obscureText: obscureText,
          enabled: enabled,
          readOnly: readOnly,
          maxLines: maxLines,
          minLines: minLines,
          maxLength: maxLength,
          validator: validator,
          onChanged: onChanged,
          onFieldSubmitted: onSubmitted,
          onTap: onTap,
          focusNode: focusNode,
          textInputAction: textInputAction,
          inputFormatters: inputFormatters,
          style: TextStyle(
            fontSize: 16.sp,
            color: AppColors.textPrimary,
            fontFamily: 'Cairo',
          ),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: TextStyle(
              fontSize: 14.sp,
              color: AppColors.textLight,
              fontFamily: 'Cairo',
            ),
            filled: true,
            fillColor: fillColor ?? AppColors.inputBackground,
            prefixIcon:
                prefixIcon != null
                    ? Icon(
                      prefixIcon,
                      color: AppColors.textSecondary,
                      size: 20.sp,
                    )
                    : null,
            suffixIcon: suffixIcon,
            contentPadding: effectiveContentPadding,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
              borderSide: BorderSide(color: borderColor ?? AppColors.border),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
              borderSide: BorderSide(color: borderColor ?? AppColors.border),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
              borderSide: const BorderSide(color: AppColors.error),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
              borderSide: const BorderSide(color: AppColors.error, width: 2),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
              borderSide: BorderSide(
                color: AppColors.border.withValues(alpha: 0.5),
              ),
            ),
            errorStyle: TextStyle(
              fontSize: 12.sp,
              color: AppColors.error,
              fontFamily: 'Cairo',
            ),
            counterStyle: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textLight,
              fontFamily: 'Cairo',
            ),
          ),
        ),
      ],
    );
  }
}

/// حقل نص للبحث
class SearchTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? hint;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final VoidCallback? onClear;

  const SearchTextField({
    super.key,
    this.controller,
    this.hint,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      controller: controller,
      hint: hint ?? 'بحث...',
      prefixIcon: Icons.search,
      suffixIcon:
          controller?.text.isNotEmpty == true
              ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  controller?.clear();
                  onClear?.call();
                },
              )
              : null,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      textInputAction: TextInputAction.search,
    );
  }
}

/// حقل نص متعدد الأسطر
class MultilineTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? label;
  final String? hint;
  final int maxLines;
  final int? maxLength;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;

  const MultilineTextField({
    super.key,
    this.controller,
    this.label,
    this.hint,
    this.maxLines = 5,
    this.maxLength,
    this.validator,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      controller: controller,
      label: label,
      hint: hint,
      maxLines: maxLines,
      minLines: 3,
      maxLength: maxLength,
      validator: validator,
      onChanged: onChanged,
      keyboardType: TextInputType.multiline,
      textInputAction: TextInputAction.newline,
    );
  }
}

/// حقل نص للأرقام
class NumberTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String? label;
  final String? hint;
  final bool allowDecimals;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final IconData? prefixIcon;
  final String? suffix;

  const NumberTextField({
    super.key,
    this.controller,
    this.label,
    this.hint,
    this.allowDecimals = false,
    this.validator,
    this.onChanged,
    this.prefixIcon,
    this.suffix,
  });

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      controller: controller,
      label: label,
      hint: hint,
      keyboardType: TextInputType.numberWithOptions(decimal: allowDecimals),
      inputFormatters: [
        if (allowDecimals)
          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))
        else
          FilteringTextInputFormatter.digitsOnly,
      ],
      validator: validator,
      onChanged: onChanged,
      prefixIcon: prefixIcon,
      suffixIcon:
          suffix != null
              ? Padding(
                padding: EdgeInsets.only(right: 12.w),
                child: Center(
                  widthFactor: 0.0,
                  child: Text(
                    suffix!,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textSecondary,
                      fontFamily: 'Cairo',
                    ),
                  ),
                ),
              )
              : null,
    );
  }
}

/// حقل إدخال مبسط بدون animation
class FloatingLabelTextField extends StatelessWidget {
  final TextEditingController? controller;
  final String label;
  final String? hint;
  final String? initialValue;
  final TextInputType? keyboardType;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final int? maxLines;
  final int? maxLength;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function()? onTap;
  final void Function(String)? onSubmitted;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final EdgeInsetsGeometry? contentPadding;
  final Color? fillColor;
  final Color? borderColor;
  final double? borderRadius;

  const FloatingLabelTextField({
    super.key,
    this.controller,
    required this.label,
    this.hint,
    this.initialValue,
    this.keyboardType,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.maxLines = 1,
    this.maxLength,
    this.validator,
    this.onChanged,
    this.onTap,
    this.onSubmitted,
    this.prefixIcon,
    this.suffixIcon,
    this.inputFormatters,
    this.focusNode,
    this.textInputAction,
    this.contentPadding,
    this.fillColor,
    this.borderColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveContentPadding =
        contentPadding ??
        EdgeInsets.symmetric(
          horizontal: AppConstants.defaultPadding.w,
          vertical: AppConstants.defaultPadding.h,
        );

    final effectiveBorderRadius = borderRadius ?? AppConstants.defaultRadius;

    return TextFormField(
      controller: controller,
      initialValue: initialValue,
      keyboardType: keyboardType,
      obscureText: obscureText,
      enabled: enabled,
      readOnly: readOnly,
      maxLines: maxLines,
      maxLength: maxLength,
      validator: validator,
      onChanged: onChanged,
      onFieldSubmitted: onSubmitted,
      onTap: onTap,
      focusNode: focusNode,
      textInputAction: textInputAction,
      inputFormatters: inputFormatters,
      style: TextStyle(
        fontSize: 16.sp,
        color: AppColors.textPrimary,
        fontFamily: 'Cairo',
      ),
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        labelStyle: TextStyle(
          fontSize: 14.sp,
          color: AppColors.textSecondary,
          fontFamily: 'Cairo',
        ),
        hintStyle: TextStyle(
          fontSize: 14.sp,
          color: AppColors.textLight,
          fontFamily: 'Cairo',
        ),
        filled: true,
        fillColor: fillColor ?? AppColors.inputBackground,
        prefixIcon:
            prefixIcon != null
                ? Icon(prefixIcon, color: AppColors.textSecondary, size: 20.sp)
                : null,
        suffixIcon: suffixIcon,
        contentPadding: effectiveContentPadding,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
          borderSide: BorderSide(color: borderColor ?? AppColors.border),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
          borderSide: BorderSide(color: borderColor ?? AppColors.border),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
          borderSide: const BorderSide(color: AppColors.error, width: 2),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(effectiveBorderRadius.r),
          borderSide: BorderSide(
            color: AppColors.border.withValues(alpha: 0.5),
          ),
        ),
        errorStyle: TextStyle(
          fontSize: 12.sp,
          color: AppColors.error,
          fontFamily: 'Cairo',
        ),
        counterStyle: TextStyle(
          fontSize: 12.sp,
          color: AppColors.textLight,
          fontFamily: 'Cairo',
        ),
      ),
    );
  }
}

/// حقل اختيار النوع (ذكر/أنثى) مبسط
class GenderSelectionField extends StatefulWidget {
  final String? initialValue;
  final void Function(String?)? onChanged;
  final String? Function(String?)? validator;

  const GenderSelectionField({
    super.key,
    this.initialValue,
    this.onChanged,
    this.validator,
  });

  @override
  State<GenderSelectionField> createState() => _GenderSelectionFieldState();
}

class _GenderSelectionFieldState extends State<GenderSelectionField> {
  String? _selectedGender;

  @override
  void initState() {
    super.initState();
    _selectedGender = widget.initialValue;
  }

  @override
  Widget build(BuildContext context) {
    return FormField<String>(
      initialValue: _selectedGender,
      validator: widget.validator,
      builder: (FormFieldState<String> state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Label
            Padding(
              padding: EdgeInsets.only(bottom: 8.h, right: 4.w),
              child: Text(
                'النوع',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  fontFamily: 'Cairo',
                ),
              ),
            ),
            // Gender Options Container
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: state.hasError ? AppColors.error : AppColors.border,
                ),
                borderRadius: BorderRadius.circular(
                  AppConstants.defaultRadius.r,
                ),
                color: AppColors.inputBackground,
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                child: Row(
                  children: [
                    Expanded(
                      child: RadioListTile<String>(
                        title: Text(
                          'ذكر',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontFamily: 'Cairo',
                          ),
                        ),
                        value: 'male',
                        groupValue: _selectedGender,
                        onChanged: (value) {
                          setState(() {
                            _selectedGender = value;
                          });
                          state.didChange(value);
                          widget.onChanged?.call(value);
                        },
                        activeColor: AppColors.primary,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    Expanded(
                      child: RadioListTile<String>(
                        title: Text(
                          'أنثى',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontFamily: 'Cairo',
                          ),
                        ),
                        value: 'female',
                        groupValue: _selectedGender,
                        onChanged: (value) {
                          setState(() {
                            _selectedGender = value;
                          });
                          state.didChange(value);
                          widget.onChanged?.call(value);
                        },
                        activeColor: AppColors.primary,
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (state.hasError)
              Padding(
                padding: EdgeInsets.only(top: 8.h, right: 4.w),
                child: Text(
                  state.errorText!,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.error,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}

/// حقل اختيار التاريخ مبسط
class DatePickerField extends StatefulWidget {
  final String label;
  final DateTime? initialDate;
  final DateTime? firstDate;
  final DateTime? lastDate;
  final void Function(DateTime?)? onChanged;
  final String? Function(DateTime?)? validator;
  final IconData? prefixIcon;

  const DatePickerField({
    super.key,
    required this.label,
    this.initialDate,
    this.firstDate,
    this.lastDate,
    this.onChanged,
    this.validator,
    this.prefixIcon,
  });

  @override
  State<DatePickerField> createState() => _DatePickerFieldState();
}

class _DatePickerFieldState extends State<DatePickerField> {
  DateTime? _selectedDate;
  final TextEditingController _controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.initialDate;
    if (_selectedDate != null) {
      _controller.text = _formatDate(_selectedDate!);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _selectDate() async {
    try {
      final DateTime? picked = await showDatePicker(
        context: context,
        initialDate:
            _selectedDate ??
            DateTime.now().subtract(
              const Duration(days: 365 * 25),
            ), // 25 سنة افتراضي
        firstDate: widget.firstDate ?? DateTime(1900),
        lastDate: widget.lastDate ?? DateTime.now(),
        locale: const Locale('ar'),
        helpText: 'اختر تاريخ الميلاد',
        cancelText: 'إلغاء',
        confirmText: 'تأكيد',
        builder: (context, child) {
          return Directionality(
            textDirection: TextDirection.rtl,
            child: Theme(
              data: Theme.of(context).copyWith(
                colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.primary,
                  onPrimary: Colors.white,
                  surface: Colors.white,
                  onSurface: AppColors.textPrimary,
                ),
                textButtonTheme: TextButtonThemeData(
                  style: TextButton.styleFrom(
                    foregroundColor: AppColors.primary,
                  ),
                ),
              ),
              child: child!,
            ),
          );
        },
      );

      if (picked != null) {
        setState(() {
          _selectedDate = picked;
          _controller.text = _formatDate(picked);
        });
        widget.onChanged?.call(picked);
      }
    } catch (e) {
      // في حالة حدوث خطأ، لا نفعل شيء
      // يمكن إضافة logging هنا لاحقاً
    }
  }

  @override
  Widget build(BuildContext context) {
    return FormField<DateTime>(
      initialValue: _selectedDate,
      validator: (value) => widget.validator?.call(value),
      builder: (FormFieldState<DateTime> state) {
        return Column(
          children: [
            InkWell(
              onTap: () async {
                // إخفاء لوحة المفاتيح إذا كانت مفتوحة
                FocusScope.of(context).unfocus();

                // انتظار قصير للتأكد من إخفاء لوحة المفاتيح
                await Future.delayed(const Duration(milliseconds: 100));

                // فتح ديالوج التاريخ
                await _selectDate();

                // تحديث حالة الحقل
                state.didChange(_selectedDate);
              },
              borderRadius: BorderRadius.circular(AppConstants.defaultRadius.r),
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(
                    color: state.hasError ? AppColors.error : AppColors.border,
                    width: 1,
                  ),
                  borderRadius: BorderRadius.circular(
                    AppConstants.defaultRadius.r,
                  ),
                  color: AppColors.inputBackground,
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: AppConstants.defaultPadding.w,
                  vertical: AppConstants.defaultPadding.h + 4.h,
                ),
                child: Row(
                  children: [
                    // أيقونة التقويم
                    Icon(
                      widget.prefixIcon ?? Icons.calendar_today,
                      color: AppColors.textSecondary,
                      size: 20.sp,
                    ),
                    SizedBox(width: 12.w),
                    // النص
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // التسمية
                          Text(
                            widget.label,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: AppColors.textSecondary,
                              fontFamily: 'Cairo',
                            ),
                          ),
                          SizedBox(height: 4.h),
                          // التاريخ المحدد أو النص الافتراضي
                          Text(
                            _selectedDate != null
                                ? _formatDate(_selectedDate!)
                                : 'اضغط لاختيار تاريخ الميلاد',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color:
                                  _selectedDate != null
                                      ? AppColors.textPrimary
                                      : AppColors.textLight,
                              fontFamily: 'Cairo',
                            ),
                          ),
                        ],
                      ),
                    ),
                    // أيقونة السهم
                    Icon(
                      Icons.arrow_drop_down,
                      color: AppColors.textSecondary,
                      size: 24.sp,
                    ),
                  ],
                ),
              ),
            ),
            // رسالة الخطأ
            if (state.hasError)
              Padding(
                padding: EdgeInsets.only(top: 8.h, right: 4.w),
                child: Text(
                  state.errorText!,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.error,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
