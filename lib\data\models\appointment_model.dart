import 'time_slot_model.dart';

class AppointmentModel {
  final String id;
  final String patientId;
  final DateTime appointmentDate;
  final String status;
  final String? notes;
  final String timeSlotId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final TimeSlotModel? timeSlot;

  AppointmentModel({
    required this.id,
    required this.patientId,
    required this.appointmentDate,
    required this.status,
    this.notes,
    required this.timeSlotId,
    required this.createdAt,
    required this.updatedAt,
    this.timeSlot,
  });

  factory AppointmentModel.fromJson(Map<String, dynamic> json) {
    return AppointmentModel(
      id: json['id'] ?? '',
      patientId: json['patient_id'] ?? '',
      appointmentDate: DateTime.parse(json['appointment_date']),
      status: json['status'] ?? '',
      notes: json['notes'],
      timeSlotId: json['time_slot_id'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      timeSlot:
          json['time_slot'] != null
              ? TimeSlotModel.fromJson(json['time_slot'])
              : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'appointment_date': appointmentDate.toIso8601String().split('T')[0],
      'status': status,
      'notes': notes,
      'time_slot_id': timeSlotId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  bool get isScheduled => status == 'scheduled';
  bool get isBooked => status == 'booked';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get isNoShow => status == 'no_show';

  bool get isPast => appointmentDate.isBefore(DateTime.now());
  bool get isToday =>
      appointmentDate.year == DateTime.now().year &&
      appointmentDate.month == DateTime.now().month &&
      appointmentDate.day == DateTime.now().day;
  bool get isFuture => appointmentDate.isAfter(DateTime.now());

  String get displayDate {
    return '${appointmentDate.day}/${appointmentDate.month}/${appointmentDate.year}';
  }

  String get displayStatus {
    switch (status) {
      case 'scheduled':
        return 'موعد قادم';
      case 'booked':
        return 'فحص واستشارة';
      case 'completed':
        return 'مكتمل';
      case 'cancelled':
        return 'ملغي';
      case 'no_show':
        return 'لم يحضر';
      default:
        return status;
    }
  }

  String get displayTime {
    if (timeSlot != null) {
      return timeSlot!.displayTimeRange12Hour;
    }
    return '';
  }

  bool get canCancel {
    if (!isBooked) return false;

    // إذا كان لدينا معلومات الوقت، نتحقق من الوقت الفعلي للجلسة
    if (timeSlot != null) {
      final now = DateTime.now();

      // تحويل وقت البداية من string إلى DateTime
      final timeParts = timeSlot!.startTime.split(':');
      final hour = int.parse(timeParts[0]);
      final minute = int.parse(timeParts[1]);

      final appointmentDateTime = DateTime(
        appointmentDate.year,
        appointmentDate.month,
        appointmentDate.day,
        hour,
        minute,
      );

      // يمكن إلغاء الحجز إذا لم يحن وقت الجلسة بعد
      return appointmentDateTime.isAfter(now);
    }

    // إذا لم تكن معلومات الوقت متوفرة، نستخدم التاريخ فقط
    return isFuture;
  }

  /// الحصول على الوقت المتبقي قبل الجلسة
  String get timeUntilAppointment {
    if (timeSlot == null) return '';

    final now = DateTime.now();
    final timeParts = timeSlot!.startTime.split(':');
    final hour = int.parse(timeParts[0]);
    final minute = int.parse(timeParts[1]);

    final appointmentDateTime = DateTime(
      appointmentDate.year,
      appointmentDate.month,
      appointmentDate.day,
      hour,
      minute,
    );

    if (appointmentDateTime.isBefore(now)) {
      return 'انتهت الجلسة';
    }

    final difference = appointmentDateTime.difference(now);

    if (difference.inDays > 0) {
      return 'خلال ${difference.inDays} ${difference.inDays == 1 ? 'يوم' : 'أيام'}';
    } else if (difference.inHours > 0) {
      return 'خلال ${difference.inHours} ${difference.inHours == 1 ? 'ساعة' : 'ساعات'}';
    } else if (difference.inMinutes > 0) {
      return 'خلال ${difference.inMinutes} ${difference.inMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    } else {
      return 'الآن';
    }
  }
}
