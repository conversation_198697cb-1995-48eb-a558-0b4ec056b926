import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../../core/constants/app_colors.dart';

/// تاب الواجبات - IIHC
class AssignmentsTab extends StatefulWidget {
  final String? patientId;

  const AssignmentsTab({super.key, this.patientId});

  @override
  State<AssignmentsTab> createState() => _AssignmentsTabState();
}

class _AssignmentsTabState extends State<AssignmentsTab> {
  List<Map<String, dynamic>> _assignments = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadAssignments();
  }

  Future<void> _loadAssignments() async {
    if (widget.patientId == null) {
      setState(() {
        _isLoading = false;
        _error = 'معرف المريض غير متوفر';
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final response = await Supabase.instance.client
          .from('assignments')
          .select('*')
          .eq('patient_id', widget.patientId!)
          .order('created_at', ascending: false);

      if (mounted) {
        setState(() {
          _assignments = List<Map<String, dynamic>>.from(response);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = 'خطأ في تحميل الواجبات: $e';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateAssignmentStatus(String assignmentId, bool isCompleted) async {
    try {
      await Supabase.instance.client
          .from('assignments')
          .update({
            'is_completed': isCompleted,
            'completed_at': isCompleted ? DateTime.now().toIso8601String() : null,
          })
          .eq('id', assignmentId);

      // إعادة تحميل البيانات
      await _loadAssignments();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isCompleted ? 'تم تحديد الواجب كمكتمل' : 'تم إلغاء إكمال الواجب',
            ),
            backgroundColor: AppColors.success,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الواجب: $e'),
            backgroundColor: AppColors.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: RefreshIndicator(
        onRefresh: _loadAssignments,
        color: AppColors.primary,
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: AppColors.primary),
            SizedBox(height: 16.h),
            Text(
              'جاري تحميل الواجبات...',
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.sp,
              color: AppColors.error,
            ),
            SizedBox(height: 16.h),
            Text(
              _error!,
              style: TextStyle(
                fontSize: 16.sp,
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton.icon(
              onPressed: _loadAssignments,
              icon: Icon(Icons.refresh),
              label: Text('إعادة المحاولة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    if (_assignments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.task_alt_outlined,
              size: 64.sp,
              color: AppColors.textLight,
            ),
            SizedBox(height: 16.h),
            Text(
              'لا توجد واجبات',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textSecondary,
              ),
            ),
            SizedBox(height: 8.h),
            Text(
              'لم يتم تعيين أي واجبات بعد',
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColors.textLight,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16.w),
      itemCount: _assignments.length,
      itemBuilder: (context, index) {
        final assignment = _assignments[index];
        return _buildAssignmentCard(assignment);
      },
    );
  }

  Widget _buildAssignmentCard(Map<String, dynamic> assignment) {
    final createdAt = DateTime.tryParse(assignment['created_at'] ?? '');
    final dueDate = DateTime.tryParse(assignment['due_date'] ?? '');
    final isCompleted = assignment['is_completed'] == true;
    final isOverdue = dueDate != null && 
                     DateTime.now().isAfter(dueDate) && 
                     !isCompleted;

    final formattedCreatedDate = createdAt != null
        ? '${createdAt.day}/${createdAt.month}/${createdAt.year}'
        : 'غير محدد';

    final formattedDueDate = dueDate != null
        ? '${dueDate.day}/${dueDate.month}/${dueDate.year}'
        : null;

    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
        side: BorderSide(
          color: isOverdue 
              ? AppColors.error.withValues(alpha: 0.3)
              : isCompleted 
                  ? AppColors.success.withValues(alpha: 0.3)
                  : Colors.transparent,
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8.w),
                  decoration: BoxDecoration(
                    color: isCompleted 
                        ? AppColors.success.withValues(alpha: 0.1)
                        : isOverdue
                            ? AppColors.error.withValues(alpha: 0.1)
                            : AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Icon(
                    isCompleted 
                        ? Icons.task_alt_rounded
                        : Icons.assignment_rounded,
                    color: isCompleted 
                        ? AppColors.success
                        : isOverdue
                            ? AppColors.error
                            : AppColors.primary,
                    size: 20.sp,
                  ),
                ),
                SizedBox(width: 12.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        assignment['title'] ?? 'واجب',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                          decoration: isCompleted 
                              ? TextDecoration.lineThrough 
                              : null,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        'تاريخ الإنشاء: $formattedCreatedDate',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Checkbox(
                  value: isCompleted,
                  onChanged: (value) {
                    if (value != null) {
                      _updateAssignmentStatus(assignment['id'], value);
                    }
                  },
                  activeColor: AppColors.success,
                ),
              ],
            ),

            if (assignment['description'] != null) ...[
              SizedBox(height: 16.h),
              Text(
                'الوصف:',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                assignment['description'],
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  height: 1.5,
                ),
              ),
            ],

            if (formattedDueDate != null) ...[
              SizedBox(height: 16.h),
              Row(
                children: [
                  Icon(
                    Icons.schedule_rounded,
                    size: 16.sp,
                    color: isOverdue ? AppColors.error : AppColors.textSecondary,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'موعد التسليم: $formattedDueDate',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: isOverdue ? AppColors.error : AppColors.textSecondary,
                      fontWeight: isOverdue ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  if (isOverdue) ...[
                    SizedBox(width: 8.w),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 6.w,
                        vertical: 2.h,
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.error,
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      child: Text(
                        'متأخر',
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],

            if (assignment['priority'] != null) ...[
              SizedBox(height: 12.h),
              Row(
                children: [
                  Icon(
                    Icons.flag_rounded,
                    size: 16.sp,
                    color: _getPriorityColor(assignment['priority']),
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'الأولوية: ${_getPriorityText(assignment['priority'])}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: _getPriorityColor(assignment['priority']),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],

            if (isCompleted && assignment['completed_at'] != null) ...[
              SizedBox(height: 12.h),
              Row(
                children: [
                  Icon(
                    Icons.check_circle_rounded,
                    size: 16.sp,
                    color: AppColors.success,
                  ),
                  SizedBox(width: 8.w),
                  Text(
                    'تم الإكمال: ${_formatDateTime(assignment['completed_at'])}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.success,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Color _getPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
      case 'عالية':
        return AppColors.error;
      case 'medium':
      case 'متوسطة':
        return AppColors.warning;
      case 'low':
      case 'منخفضة':
        return AppColors.success;
      default:
        return AppColors.textSecondary;
    }
  }

  String _getPriorityText(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return priority;
    }
  }

  String _formatDateTime(String dateTimeString) {
    final dateTime = DateTime.tryParse(dateTimeString);
    if (dateTime == null) return 'غير محدد';
    
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}