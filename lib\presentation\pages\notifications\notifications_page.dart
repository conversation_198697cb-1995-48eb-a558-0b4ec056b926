import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../core/constants/app_colors.dart';
import '../../../core/utils/helpers.dart';
import '../../../core/services/fcm_integration_service.dart';
import '../../../data/models/notification_model.dart';
import '../../bloc/notifications/notifications_bloc.dart';
import '../../bloc/notifications/notifications_events.dart';
import '../../bloc/notifications/notifications_states.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/notifications/notification_card.dart';

class NotificationsPage extends StatefulWidget {
  const NotificationsPage({super.key});

  @override
  State<NotificationsPage> createState() => _NotificationsPageState();
}

class _NotificationsPageState extends State<NotificationsPage> {
  bool _hasLoadedData = false;

  @override
  void initState() {
    super.initState();
    // لا نحمل البيانات في initState - سيتم التحميل في build عند الحاجة
  }

  void _loadDataIfNeeded() {
    if (!_hasLoadedData) {
      context.read<NotificationsBloc>().add(const LoadNotifications());
      _hasLoadedData = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحميل البيانات عند أول عرض للصفحة
    _loadDataIfNeeded();

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: CustomAppBar(
          title: 'الإشعارات',
          actions: [
            // زر تحديد الكل كمقروء
            BlocBuilder<NotificationsBloc, NotificationsState>(
              builder: (context, state) {
                if (state is NotificationsLoaded && state.unreadCount > 0) {
                  return IconButton(
                    onPressed: () {
                      context.read<NotificationsBloc>().add(
                        const MarkAllNotificationsAsRead(),
                      );
                    },
                    icon: Icon(
                      Icons.done_all,
                      color: AppColors.primary,
                      size: 24.sp,
                    ),
                    tooltip: 'تحديد الكل كمقروء',
                  );
                }
                return const SizedBox.shrink();
              },
            ),
            // قائمة الخيارات
            _buildPopupMenu(),
          ],
        ),
        body: BlocConsumer<NotificationsBloc, NotificationsState>(
          listener: (context, state) {
            if (state is NotificationsOperationSuccess) {
              Helpers.showSuccessSnackBar(context, state.message);
            } else if (state is NotificationsError) {
              Helpers.showErrorSnackBar(context, state.message);
            }
          },
          builder: (context, state) {
            if (state is NotificationsLoading) {
              return const Center(child: LoadingWidget());
            }

            if (state is NotificationsError) {
              return _buildErrorState(state.message);
            }

            if (state is NotificationsLoaded ||
                state is NotificationsOperationSuccess) {
              final notifications =
                  state is NotificationsLoaded
                      ? state.notifications
                      : (state as NotificationsOperationSuccess).notifications;

              if (notifications.isEmpty) {
                return _buildEmptyState();
              }

              return _buildNotificationsList(notifications);
            }

            return _buildEmptyState();
          },
        ),
      ),
    );
  }

  /// بناء قائمة الخيارات
  Widget _buildPopupMenu() {
    return PopupMenuButton<String>(
      onSelected: _handleMenuAction,
      icon: Icon(Icons.more_vert, color: AppColors.textPrimary, size: 24.sp),
      itemBuilder:
          (context) => [
            PopupMenuItem(
              value: 'mark_all_read',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.done_all, color: AppColors.primary, size: 20.sp),
                  SizedBox(width: 12.w),
                  Text('تحديد الكل كمقروء', style: TextStyle(fontSize: 14.sp)),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'clear_all',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.delete_sweep, color: AppColors.error, size: 20.sp),
                  SizedBox(width: 12.w),
                  Text('مسح جميع الإشعارات', style: TextStyle(fontSize: 14.sp)),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'print_token',
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.token, color: AppColors.secondary, size: 20.sp),
                  SizedBox(width: 12.w),
                  Text('طباعة رمز الجهاز', style: TextStyle(fontSize: 14.sp)),
                ],
              ),
            ),
          ],
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'mark_all_read':
        context.read<NotificationsBloc>().add(
          const MarkAllNotificationsAsRead(),
        );
        break;
      case 'clear_all':
        _showClearAllDialog();
        break;
      case 'print_token':
        FCMIntegrationService().printCurrentToken();
        break;
    }
  }

  /// عرض حوار تأكيد مسح جميع الإشعارات
  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('مسح جميع الإشعارات'),
            content: const Text(
              'هل أنت متأكد من رغبتك في مسح جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<NotificationsBloc>().add(
                    const ClearAllNotifications(),
                  );
                },
                style: TextButton.styleFrom(foregroundColor: AppColors.error),
                child: const Text('مسح'),
              ),
            ],
          ),
    );
  }

  /// بناء قائمة الإشعارات
  Widget _buildNotificationsList(List<NotificationModel> notifications) {
    return RefreshIndicator(
      onRefresh: () async {
        // إعادة تحميل البيانات عند السحب للتحديث
        context.read<NotificationsBloc>().add(const LoadNotifications());
      },
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return NotificationCard(
            notification: notification,
            onTap: () => _handleNotificationTap(notification),
          );
        },
      ),
    );
  }

  /// معالجة النقر على الإشعار
  void _handleNotificationTap(NotificationModel notification) {
    // تحديد الإشعار كمقروء إذا لم يكن مقروءاً
    if (!notification.isRead) {
      context.read<NotificationsBloc>().add(
        MarkNotificationAsRead(notification.id),
      );
    }

    // عرض تفاصيل الإشعار
    _showNotificationDetails(notification);
  }

  /// عرض تفاصيل الإشعار
  void _showNotificationDetails(NotificationModel notification) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Row(
              children: [
                Text(notification.type.icon),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    notification.title,
                    style: TextStyle(fontSize: 16.sp),
                  ),
                ),
              ],
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(notification.body, style: TextStyle(fontSize: 14.sp)),
                SizedBox(height: 16.h),
                Row(
                  children: [
                    Icon(
                      Icons.access_time,
                      size: 16.sp,
                      color: AppColors.textSecondary,
                    ),
                    SizedBox(width: 4.w),
                    Text(
                      notification.formattedTime,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// بناء حالة الخطأ
  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64.sp, color: AppColors.error),
          SizedBox(height: 16.h),
          Text(
            message,
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () {
              context.read<NotificationsBloc>().add(const LoadNotifications());
            },
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء حالة عدم وجود إشعارات
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_none,
            size: 80.sp,
            color: AppColors.textLight,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 18.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            'ستظهر الإشعارات هنا عند وصولها',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textLight),
          ),
        ],
      ),
    );
  }
}
