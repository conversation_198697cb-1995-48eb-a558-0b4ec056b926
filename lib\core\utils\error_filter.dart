import 'package:flutter/foundation.dart';

/// فئة لفلترة الأخطاء غير المهمة
class ErrorFilter {
  /// قائمة الأخطاء التي يجب تجاهلها
  static const List<String> _ignoredErrors = [
    'RenderFlex overflowed',
    'overflowed by',
    'pixels on the right',
    'pixels on the bottom',
    'pixels on the left',
    'pixels on the top',
    'renderObject.child == child',
    'build dirty widget',
    'Failed assertion',
    'A widget which was marked as dirty',
    'Tried to build dirty widget',
    'No Directionality widget found',
    'Directionality widget ancestor',
    'Scaffold widgets require a Directionality',
    'The specific widget that could not find a Directionality ancestor was',
    'HttpException: Invalid statusCode: 404',
    'Failed to load network image',
    'Image loading failed',
    'NetworkImageLoadException',
    'CachedNetworkImageProvider',
  ];

  /// فحص ما إذا كان الخطأ يجب تجاهله
  static bool shouldIgnoreError(Object error) {
    final errorMessage = error.toString().toLowerCase();

    return _ignoredErrors.any(
      (ignoredError) => errorMessage.contains(ignoredError.toLowerCase()),
    );
  }

  /// تسجيل مبسط للأخطاء المتجاهلة
  static void logIgnoredError(Object error) {
    if (kDebugMode) {
      final errorMessage = error.toString();
      final firstLine = errorMessage.split('\n').first;
      print('⚠️ Layout Warning (Ignored): $firstLine');
    }
  }

  /// فحص ما إذا كان الخطأ متعلق بالتخطيط
  static bool isLayoutError(Object error) {
    final errorMessage = error.toString().toLowerCase();

    return errorMessage.contains('renderflex') ||
        errorMessage.contains('overflowed') ||
        errorMessage.contains('pixels on') ||
        errorMessage.contains('layout');
  }

  /// فحص ما إذا كان الخطأ متعلق بالبناء
  static bool isBuildError(Object error) {
    final errorMessage = error.toString().toLowerCase();

    return errorMessage.contains('build dirty') ||
        errorMessage.contains('renderobject.child') ||
        errorMessage.contains('widget which was marked as dirty');
  }

  /// فحص ما إذا كان الخطأ متعلق بـ Directionality
  static bool isDirectionalityError(Object error) {
    final errorMessage = error.toString().toLowerCase();

    return errorMessage.contains('directionality') ||
        errorMessage.contains('scaffold widgets require a directionality');
  }

  /// فحص ما إذا كان الخطأ متعلق بتحميل الصور
  static bool isImageLoadingError(Object error) {
    final errorMessage = error.toString().toLowerCase();

    return errorMessage.contains('httpexception: invalid statuscode: 404') ||
        errorMessage.contains('failed to load network image') ||
        errorMessage.contains('image loading failed') ||
        errorMessage.contains('networkimageloadexception') ||
        errorMessage.contains('cachednetworkimageprovider');
  }

  /// فحص ما إذا كان الخطأ حرج
  static bool isCriticalError(Object error) {
    return !shouldIgnoreError(error);
  }

  /// الحصول على نوع الخطأ
  static String getErrorType(Object error) {
    if (isLayoutError(error)) return 'Layout';
    if (isBuildError(error)) return 'Build';
    return 'Critical';
  }

  /// تنظيف رسالة الخطأ
  static String cleanErrorMessage(Object error) {
    final errorMessage = error.toString();
    final lines = errorMessage.split('\n');

    // إرجاع أول سطرين فقط للأخطاء الطويلة
    if (lines.length > 2) {
      return '${lines[0]}\n${lines[1]}...';
    }

    return errorMessage;
  }
}
