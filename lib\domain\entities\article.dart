import 'package:equatable/equatable.dart';

/// كيان المقال
class Article extends Equatable {
  final String id;
  final String title;
  final String content;
  final String? summary;
  final String author;
  final String? imageUrl;
  final String? pdfUrl;
  final String? reference;
  final String? category;
  final String? categoryName;
  final List<String>? tags;
  final bool isPublished;
  final bool isFeatured;
  final int? readingTime;
  final int? viewsCount;
  final int? likesCount;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? categoryId;

  const Article({
    required this.id,
    required this.title,
    required this.content,
    required this.author,
    this.summary,
    this.imageUrl,
    this.pdfUrl,
    this.reference,
    this.category,
    this.categoryName,
    this.tags,
    this.isPublished = false,
    this.isFeatured = false,
    this.readingTime,
    this.viewsCount,
    this.likesCount,
    this.createdAt,
    this.updatedAt,
    this.categoryId,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    content,
    author,
    imageUrl,
    isPublished,
    createdAt,
    updatedAt,
    categoryId,
    isFeatured,
  ];

  /// التحقق من وجود صورة
  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;

  /// التحقق من وجود ملف PDF
  bool get hasPdf => pdfUrl != null && pdfUrl!.isNotEmpty;

  /// التحقق من وجود مرجع
  bool get hasReference => reference != null && reference!.isNotEmpty;

  /// التحقق من وجود فئة
  bool get hasCategory => categoryId != null && categoryId!.isNotEmpty;

  /// حساب وقت القراءة المقدر (بالدقائق)
  int get estimatedReadingTime {
    const wordsPerMinute = 200; // متوسط سرعة القراءة
    final wordCount = content.split(' ').length;
    final minutes = (wordCount / wordsPerMinute).ceil();
    return minutes < 1 ? 1 : minutes;
  }

  /// الحصول على ملخص المقال التلقائي
  String get autoSummary {
    // إذا كان هناك ملخص محفوظ، استخدمه
    if (summary != null && summary!.isNotEmpty) {
      return summary!;
    }

    // وإلا قم بإنشاء ملخص تلقائي
    const maxLength = 150;
    if (content.length <= maxLength) return content;

    final truncated = content.substring(0, maxLength);
    final lastSpace = truncated.lastIndexOf(' ');

    if (lastSpace > 0) {
      return '${truncated.substring(0, lastSpace)}...';
    }

    return '$truncated...';
  }

  /// التحقق من كون المقال جديد (أقل من أسبوع)
  bool get isNew {
    if (createdAt == null) return false;
    final now = DateTime.now();
    final difference = now.difference(createdAt!);
    return difference.inDays <= 7;
  }

  /// التحقق من كون المقال محدث مؤخراً
  bool get isRecentlyUpdated {
    if (updatedAt == null || createdAt == null) return false;
    final updateDifference = updatedAt!.difference(createdAt!);
    return updateDifference.inHours > 1; // تم التحديث بعد ساعة من الإنشاء
  }

  /// إنشاء Article من JSON
  factory Article.fromJson(Map<String, dynamic> json) {
    return Article(
      id: json['id']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      content: json['content']?.toString() ?? '',
      summary: json['summary']?.toString(),
      author: json['author']?.toString() ?? '',
      imageUrl: json['image_url']?.toString(),
      pdfUrl: json['pdf_url']?.toString(),
      reference: json['reference']?.toString(),
      category: json['category']?.toString(),
      categoryName: json['category_name']?.toString(),
      tags: json['tags'] != null ? List<String>.from(json['tags']) : null,
      isPublished: json['is_published'] == true,
      isFeatured: json['is_featured'] == true,
      readingTime:
          json['reading_time'] != null
              ? int.tryParse(json['reading_time'].toString())
              : null,
      viewsCount:
          json['views_count'] != null
              ? int.tryParse(json['views_count'].toString())
              : null,
      likesCount:
          json['likes_count'] != null
              ? int.tryParse(json['likes_count'].toString())
              : null,
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'].toString())
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'].toString())
              : null,
      categoryId: json['category_id']?.toString(),
    );
  }

  /// تحويل Article إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'summary': summary,
      'author': author,
      'image_url': imageUrl,
      'pdf_url': pdfUrl,
      'reference': reference,
      'category': category,
      'category_name': categoryName,
      'tags': tags,
      'is_published': isPublished,
      'is_featured': isFeatured,
      'reading_time': readingTime,
      'views_count': viewsCount,
      'likes_count': likesCount,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'category_id': categoryId,
    };
  }

  /// إنشاء نسخة محدثة من Article
  Article copyWith({
    String? id,
    String? title,
    String? content,
    String? summary,
    String? author,
    String? imageUrl,
    String? pdfUrl,
    String? reference,
    String? category,
    String? categoryName,
    List<String>? tags,
    bool? isPublished,
    bool? isFeatured,
    int? readingTime,
    int? viewsCount,
    int? likesCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? categoryId,
  }) {
    return Article(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      summary: summary ?? this.summary,
      author: author ?? this.author,
      imageUrl: imageUrl ?? this.imageUrl,
      pdfUrl: pdfUrl ?? this.pdfUrl,
      reference: reference ?? this.reference,
      category: category ?? this.category,
      categoryName: categoryName ?? this.categoryName,
      tags: tags ?? this.tags,
      isPublished: isPublished ?? this.isPublished,
      isFeatured: isFeatured ?? this.isFeatured,
      readingTime: readingTime ?? this.readingTime,
      viewsCount: viewsCount ?? this.viewsCount,
      likesCount: likesCount ?? this.likesCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      categoryId: categoryId ?? this.categoryId,
    );
  }
}

/// كيان فئة المقال
class ArticleCategory extends Equatable {
  final String id;
  final String name;
  final String? description;
  final int sortOrder;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const ArticleCategory({
    required this.id,
    required this.name,
    this.description,
    this.sortOrder = 0,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
    id,
    name,
    description,
    sortOrder,
    isActive,
    createdAt,
    updatedAt,
  ];

  /// تحويل من JSON
  factory ArticleCategory.fromJson(Map<String, dynamic> json) {
    return ArticleCategory(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString(),
      sortOrder: json['sort_order'] ?? 0,
      isActive: json['is_active'] ?? true,
      createdAt:
          json['created_at'] != null
              ? DateTime.tryParse(json['created_at'].toString())
              : null,
      updatedAt:
          json['updated_at'] != null
              ? DateTime.tryParse(json['updated_at'].toString())
              : null,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'sort_order': sortOrder,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
