import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../data/models/lab_test_model.dart';
import '../../../widgets/common/filter_button.dart';

class LabTestsTab extends StatefulWidget {
  final List<LabTestModel> labTests;
  final String? patientId;

  const LabTestsTab({super.key, required this.labTests, this.patientId});

  @override
  State<LabTestsTab> createState() => _LabTestsTabState();
}

class _LabTestsTabState extends State<LabTestsTab> {
  String selectedFilter = 'all';

  final Map<String, String> filterOptions = {
    'all': 'الكل',
    'normal': 'طبيعي',
    'abnormal': 'غير طبيعي',
    'pending': 'في الانتظار',
  };

  @override
  Widget build(BuildContext context) {
    if (widget.labTests.isEmpty) {
      return _buildEmptyState();
    }

    final filteredTests = _getFilteredTests();

    return Column(
      children: [
        _buildFilterChips(),
        Expanded(
          child:
              filteredTests.isEmpty
                  ? _buildNoResultsState()
                  : _buildLabTestsList(filteredTests),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.biotech_outlined,
            size: 80.sp,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد فحوصات مخبرية',
            style: TextStyle(fontSize: 18.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم عرض الفحوصات المخبرية هنا عند توفرها',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 60.sp, color: AppColors.textSecondary),
          SizedBox(height: 16.h),
          Text(
            'لا توجد نتائج للفلتر المحدد',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: filterOptions.length,
        separatorBuilder: (context, index) => SizedBox(width: 8.w),
        itemBuilder: (context, index) {
          final key = filterOptions.keys.elementAt(index);
          final label = filterOptions[key]!;
          final isSelected = selectedFilter == key;

          return MedicalFilterButton(
            text: label,
            isSelected: isSelected,
            onTap: () {
              setState(() {
                selectedFilter = key;
              });
            },
          );
        },
      ),
    );
  }

  Widget _buildLabTestsList(List<LabTestModel> filteredTests) {
    // Sort by priority (abnormal and recent tests first)
    final sortedTests = List<LabTestModel>.from(filteredTests);
    sortedTests.sort((a, b) => b.priority.compareTo(a.priority));

    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: sortedTests.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final test = sortedTests[index];
        return _buildLabTestCard(test);
      },
    );
  }

  Widget _buildLabTestCard(LabTestModel test) {
    final statusColor = Color(
      int.parse(test.statusColor.replaceFirst('#', '0xFF')),
    );

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: statusColor.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: statusColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      _getTestIcon(test.testType),
                      color: statusColor,
                      size: 20.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          test.testName,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 2.h),
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 2.h,
                              ),
                              decoration: BoxDecoration(
                                color: statusColor.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                test.statusText,
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.bold,
                                  color: statusColor,
                                ),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Text(
                              test.timeSinceTest,
                              style: TextStyle(
                                fontSize: 10.sp,
                                color: AppColors.textSecondary,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        test.displayTestDate,
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      if (test.testType != null) ...[
                        SizedBox(height: 2.h),
                        Text(
                          test.displayTestType,
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (test.hasResults) ...[
                    _buildResultsSection(test),
                    SizedBox(height: 12.h),
                  ],
                  if (test.hasDoctorNotes) ...[
                    _buildDoctorNotesSection(test),
                    SizedBox(height: 12.h),
                  ],
                  if (test.notes != null && test.notes!.isNotEmpty) ...[
                    _buildNotesSection(test),
                    SizedBox(height: 12.h),
                  ],
                  if (test.hasImage) ...[_buildImageSection(test)],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultsSection(LabTestModel test) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.assignment, color: AppColors.primary, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                'النتائج:',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            test.results!,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textPrimary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDoctorNotesSection(LabTestModel test) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.medical_information, color: Colors.blue, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                'ملاحظات الطبيب:',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            test.doctorNotes!,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textPrimary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotesSection(LabTestModel test) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.note, color: AppColors.textSecondary, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                'ملاحظات:',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            test.notes!,
            style: TextStyle(
              fontSize: 12.sp,
              color: AppColors.textPrimary,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageSection(LabTestModel test) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(Icons.image, color: AppColors.primary, size: 16.sp),
          SizedBox(width: 8.w),
          Text(
            'صورة الفحص متوفرة',
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
          const Spacer(),
          ElevatedButton.icon(
            onPressed: () {
              _showImageViewer(context, test.imageUrl!);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            icon: Icon(Icons.visibility, size: 14.sp),
            label: Text('عرض', style: TextStyle(fontSize: 12.sp)),
          ),
        ],
      ),
    );
  }

  void _showImageViewer(BuildContext context, String imageUrl) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.black,
          insetPadding: EdgeInsets.zero,
          child: Stack(
            children: [
              InteractiveViewer(
                panEnabled: true,
                boundaryMargin: EdgeInsets.all(20.w),
                minScale: 0.5,
                maxScale: 4.0,
                child: Center(
                  child: Image.network(
                    imageUrl,
                    fit: BoxFit.contain,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Center(
                        child: CircularProgressIndicator(
                          value:
                              loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      loadingProgress.expectedTotalBytes!
                                  : null,
                          color: AppColors.primary,
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.white,
                              size: 48.sp,
                            ),
                            SizedBox(height: 16.h),
                            Text(
                              'خطأ في تحميل الصورة',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16.sp,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ),
              Positioned(
                top: 40.h,
                right: 20.w,
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(Icons.close, color: Colors.white, size: 24.sp),
                  ),
                ),
              ),
              Positioned(
                bottom: 40.h,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 20.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 8.h,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(20.r),
                        ),
                        child: Text(
                          'اسحب للتحريك • اضغط مرتين للتكبير',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12.sp,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  List<LabTestModel> _getFilteredTests() {
    if (selectedFilter == 'all') {
      return widget.labTests;
    }

    return widget.labTests.where((test) {
      switch (selectedFilter) {
        case 'normal':
          return test.isNormal == true;
        case 'abnormal':
          return test.isNormal == false;
        case 'pending':
          return test.isNormal == null;
        default:
          return true;
      }
    }).toList();
  }

  IconData _getTestIcon(String? testType) {
    switch (testType?.toLowerCase()) {
      case 'blood':
        return Icons.water_drop;
      case 'urine':
        return Icons.science;
      case 'stool':
        return Icons.biotech;
      case 'xray':
      case 'ct':
      case 'mri':
        return Icons.medical_services;
      case 'ultrasound':
        return Icons.monitor_heart;
      case 'ecg':
      case 'echo':
        return Icons.favorite;
      case 'endoscopy':
        return Icons.visibility;
      default:
        return Icons.biotech;
    }
  }
}
