import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../data/models/reminder_model.dart';
import '../../../widgets/common/filter_button.dart';

class RemindersTab extends StatefulWidget {
  final List<ReminderModel> reminders;
  final String? patientId;

  const RemindersTab({super.key, required this.reminders, this.patientId});

  @override
  State<RemindersTab> createState() => _RemindersTabState();
}

class _RemindersTabState extends State<RemindersTab> {
  String selectedFilter = 'all';

  final Map<String, String> filterOptions = {
    'all': 'الكل',
    'today': 'اليوم',
    'water': 'الماء',
    'exercise': 'التمارين',
    'meal': 'الوجبات',
    'medication': 'الأدوية',
  };

  @override
  Widget build(BuildContext context) {
    if (widget.reminders.isEmpty) {
      return _buildEmptyState();
    }

    final filteredReminders = _getFilteredReminders();

    return Column(
      children: [
        _buildFilterChips(),
        Expanded(
          child:
              filteredReminders.isEmpty
                  ? _buildNoResultsState()
                  : _buildRemindersList(filteredReminders),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_outlined,
            size: 80.sp,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد تذكيرات',
            style: TextStyle(fontSize: 18.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم عرض التذكيرات هنا عند توفرها',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 60.sp, color: AppColors.textSecondary),
          SizedBox(height: 16.h),
          Text(
            'لا توجد نتائج للفلتر المحدد',
            style: TextStyle(fontSize: 16.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: filterOptions.length,
        separatorBuilder: (context, index) => SizedBox(width: 8.w),
        itemBuilder: (context, index) {
          final key = filterOptions.keys.elementAt(index);
          final label = filterOptions[key]!;
          final isSelected = selectedFilter == key;

          return MedicalFilterButton(
            text: label,
            isSelected: isSelected,
            onTap: () {
              setState(() {
                selectedFilter = key;
              });
            },
          );
        },
      ),
    );
  }

  Widget _buildRemindersList(List<ReminderModel> filteredReminders) {
    // Sort reminders: active first, then by next occurrence
    final sortedReminders = List<ReminderModel>.from(filteredReminders);
    sortedReminders.sort((a, b) {
      if (a.isActive != b.isActive) {
        return a.isActive ? -1 : 1;
      }
      final aNext = a.nextOccurrence;
      final bNext = b.nextOccurrence;
      if (aNext == null && bNext == null) return 0;
      if (aNext == null) return 1;
      if (bNext == null) return -1;
      return aNext.compareTo(bNext);
    });

    return ListView.separated(
      padding: EdgeInsets.all(16.w),
      itemCount: sortedReminders.length,
      separatorBuilder: (context, index) => SizedBox(height: 12.h),
      itemBuilder: (context, index) {
        final reminder = sortedReminders[index];
        return _buildReminderCard(reminder);
      },
    );
  }

  Widget _buildReminderCard(ReminderModel reminder) {
    final color = Color(
      int.parse(reminder.colorCode.replaceFirst('#', '0xFF')),
    );
    final isDueToday = reminder.isDueToday;

    return Card(
      elevation: isDueToday ? 4 : 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: isDueToday ? color : color.withValues(alpha: 0.3),
            width: isDueToday ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: color.withValues(alpha: isDueToday ? 0.2 : 0.1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8.r),
                    ),
                    child: Icon(
                      _getReminderIcon(reminder.reminderType),
                      color: color,
                      size: 20.sp,
                    ),
                  ),
                  SizedBox(width: 12.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          reminder.title,
                          style: TextStyle(
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        SizedBox(height: 2.h),
                        Row(
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 2.h,
                              ),
                              decoration: BoxDecoration(
                                color: color.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                reminder.displayReminderType,
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.bold,
                                  color: color,
                                ),
                              ),
                            ),
                            SizedBox(width: 8.w),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8.w,
                                vertical: 2.h,
                              ),
                              decoration: BoxDecoration(
                                color:
                                    reminder.isActive
                                        ? Colors.green.withValues(alpha: 0.1)
                                        : Colors.grey.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12.r),
                              ),
                              child: Text(
                                reminder.statusText,
                                style: TextStyle(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      reminder.isActive
                                          ? Colors.green
                                          : Colors.grey,
                                ),
                              ),
                            ),
                            if (isDueToday) ...[
                              SizedBox(width: 8.w),
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 8.w,
                                  vertical: 2.h,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.orange.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Text(
                                  'اليوم',
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.orange,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        reminder.displayTime,
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),
                      SizedBox(height: 2.h),
                      Text(
                        reminder.nextOccurrenceText,
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (reminder.description != null &&
                      reminder.description!.isNotEmpty) ...[
                    _buildInfoRow('الوصف', reminder.description!),
                    SizedBox(height: 12.h),
                  ],
                  _buildInfoRow('الأيام', reminder.displayDays),
                  SizedBox(height: 12.h),
                  _buildScheduleInfo(reminder),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 60.w,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w400,
              color: AppColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildScheduleInfo(ReminderModel reminder) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.schedule, color: AppColors.primary, size: 16.sp),
              SizedBox(width: 8.w),
              Text(
                'جدولة التذكير:',
                style: TextStyle(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            'الوقت: ${reminder.displayTime}',
            style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
          ),
          SizedBox(height: 4.h),
          Text(
            'الأيام: ${reminder.displayDays}',
            style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
          ),
          if (reminder.nextOccurrence != null) ...[
            SizedBox(height: 4.h),
            Text(
              'التذكير القادم: ${reminder.nextOccurrenceText}',
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<ReminderModel> _getFilteredReminders() {
    if (selectedFilter == 'all') {
      return widget.reminders;
    }

    return widget.reminders.where((reminder) {
      switch (selectedFilter) {
        case 'water':
        case 'exercise':
        case 'meal':
        case 'medication':
          return reminder.reminderType.toLowerCase() == selectedFilter;
        case 'today':
          return reminder.isDueToday;
        default:
          return true;
      }
    }).toList();
  }

  IconData _getReminderIcon(String type) {
    switch (type.toLowerCase()) {
      case 'medication':
        return Icons.medication;
      case 'supplement':
        return Icons.medication_liquid;
      case 'exercise':
        return Icons.fitness_center;
      case 'meal':
        return Icons.restaurant;
      case 'water':
        return Icons.water_drop;
      case 'appointment':
        return Icons.event;
      case 'measurement':
        return Icons.monitor_weight;
      case 'sleep':
        return Icons.bedtime;
      default:
        return Icons.notifications;
    }
  }
}
