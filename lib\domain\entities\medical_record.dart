import 'package:equatable/equatable.dart';

/// كيان السجل الطبي
class MedicalRecord extends Equatable {
  /// معرف السجل
  final String id;

  /// معرف المريض
  final String patientId;

  /// نوع السجل
  final String recordType;

  /// عنوان السجل
  final String title;

  /// وصف السجل
  final String description;

  /// معرف الطبيب
  final String? doctorId;

  /// اسم الطبيب
  final String? doctorName;

  /// تاريخ السجل
  final DateTime recordDate;

  /// بيانات إضافية
  final Map<String, dynamic>? data;

  /// المرفقات
  final List<String>? attachments;

  /// الملاحظات
  final String? notes;

  /// تاريخ الإنشاء
  final DateTime? createdAt;

  /// تاريخ التحديث
  final DateTime? updatedAt;

  const MedicalRecord({
    required this.id,
    required this.patientId,
    required this.recordType,
    required this.title,
    required this.description,
    required this.recordDate,
    this.doctorId,
    this.doctorName,
    this.data,
    this.attachments,
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  @override
  List<Object?> get props => [
        id,
        patientId,
        recordType,
        title,
        description,
        doctorId,
        doctorName,
        recordDate,
        data,
        attachments,
        notes,
        createdAt,
        updatedAt,
      ];

  /// إنشاء MedicalRecord من JSON
  factory MedicalRecord.fromJson(Map<String, dynamic> json) {
    return MedicalRecord(
      id: json['id']?.toString() ?? '',
      patientId: json['patient_id']?.toString() ?? '',
      recordType: json['record_type']?.toString() ?? '',
      title: json['title']?.toString() ?? '',
      description: json['description']?.toString() ?? '',
      recordDate: json['record_date'] != null 
          ? DateTime.parse(json['record_date'].toString())
          : DateTime.now(),
      doctorId: json['doctor_id']?.toString(),
      doctorName: json['doctor_name']?.toString(),
      data: json['data'] as Map<String, dynamic>?,
      attachments: json['attachments'] != null 
          ? List<String>.from(json['attachments'])
          : null,
      notes: json['notes']?.toString(),
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at'].toString())
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at'].toString())
          : null,
    );
  }

  /// تحويل MedicalRecord إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'record_type': recordType,
      'title': title,
      'description': description,
      'record_date': recordDate.toIso8601String(),
      'doctor_id': doctorId,
      'doctor_name': doctorName,
      'data': data,
      'attachments': attachments,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من MedicalRecord
  MedicalRecord copyWith({
    String? id,
    String? patientId,
    String? recordType,
    String? title,
    String? description,
    DateTime? recordDate,
    String? doctorId,
    String? doctorName,
    Map<String, dynamic>? data,
    List<String>? attachments,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MedicalRecord(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      recordType: recordType ?? this.recordType,
      title: title ?? this.title,
      description: description ?? this.description,
      recordDate: recordDate ?? this.recordDate,
      doctorId: doctorId ?? this.doctorId,
      doctorName: doctorName ?? this.doctorName,
      data: data ?? this.data,
      attachments: attachments ?? this.attachments,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من وجود مرفقات
  bool get hasAttachments {
    return attachments != null && attachments!.isNotEmpty;
  }

  /// عدد المرفقات
  int get attachmentsCount {
    return attachments?.length ?? 0;
  }

  /// التحقق من وجود بيانات إضافية
  bool get hasData {
    return data != null && data!.isNotEmpty;
  }

  /// الحصول على قيمة من البيانات الإضافية
  T? getDataValue<T>(String key) {
    if (data == null) return null;
    return data![key] as T?;
  }

  /// تنسيق تاريخ السجل
  String get formattedRecordDate {
    return '${recordDate.day}/${recordDate.month}/${recordDate.year}';
  }

  /// تنسيق وقت السجل
  String get formattedRecordTime {
    return '${recordDate.hour.toString().padLeft(2, '0')}:${recordDate.minute.toString().padLeft(2, '0')}';
  }

  /// تنسيق تاريخ ووقت السجل
  String get formattedRecordDateTime {
    return '$formattedRecordDate - $formattedRecordTime';
  }

  /// التحقق من أن السجل حديث (خلال آخر 30 يوم)
  bool get isRecent {
    final now = DateTime.now();
    final difference = now.difference(recordDate);
    return difference.inDays <= 30;
  }

  /// التحقق من أن السجل قديم (أكثر من سنة)
  bool get isOld {
    final now = DateTime.now();
    final difference = now.difference(recordDate);
    return difference.inDays > 365;
  }

  /// الحصول على لون السجل حسب النوع
  String get recordTypeColor {
    switch (recordType.toLowerCase()) {
      case 'consultation':
      case 'استشارة':
        return '#4CAF50'; // أخضر
      case 'diagnosis':
      case 'تشخيص':
        return '#2196F3'; // أزرق
      case 'treatment':
      case 'علاج':
        return '#FF9800'; // برتقالي
      case 'surgery':
      case 'جراحة':
        return '#F44336'; // أحمر
      case 'follow_up':
      case 'متابعة':
        return '#9C27B0'; // بنفسجي
      default:
        return '#607D8B'; // رمادي
    }
  }

  /// الحصول على أيقونة السجل حسب النوع
  String get recordTypeIcon {
    switch (recordType.toLowerCase()) {
      case 'consultation':
      case 'استشارة':
        return '🩺';
      case 'diagnosis':
      case 'تشخيص':
        return '🔍';
      case 'treatment':
      case 'علاج':
        return '💊';
      case 'surgery':
      case 'جراحة':
        return '🏥';
      case 'follow_up':
      case 'متابعة':
        return '📋';
      default:
        return '📄';
    }
  }

  @override
  String toString() {
    return 'MedicalRecord(id: $id, title: $title, recordType: $recordType, recordDate: $recordDate)';
  }
}
