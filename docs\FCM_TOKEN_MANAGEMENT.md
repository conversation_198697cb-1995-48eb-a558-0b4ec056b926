# نظام إدارة FCM Tokens

## نظرة عامة

تم تطوير نظام شامل لإدارة FCM Tokens في تطبيق المستخدم للتكامل مع نظام الإشعارات في Admin App.

## المكونات الرئيسية

### 1. نموذج البيانات (FCMTokenModel)
```dart
class FCMTokenModel {
  final String id;
  final String userId;
  final String fcmToken;
  final Map<String, dynamic> deviceInfo;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
}
```

### 2. خدمة إدارة FCM Tokens (FCMTokenService)
- **saveToken()**: حفظ token جديد
- **updateToken()**: تحديث token موجود
- **deleteToken()**: حذف token عند تسجيل الخروج
- **refreshToken()**: تحديث token عند تغييره من Firebase
- **getCachedToken()**: الحصول على token من Cache
- **isTokenValid()**: التحقق من صحة Token
- **cleanupInactiveTokens()**: تنظيف tokens غير النشطة

### 3. خدمة التكامل (FCMIntegrationService)
- **initialize()**: تهيئة النظام
- **onUserLogin()**: حفظ token عند تسجيل الدخول
- **onUserLogout()**: حذف token عند تسجيل الخروج
- **checkTokenStatus()**: فحص حالة Token
- **performPeriodicCleanup()**: تنظيف دوري

## جدول قاعدة البيانات

```sql
CREATE TABLE user_fcm_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  fcm_token TEXT NOT NULL UNIQUE,
  device_info JSONB NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_user_fcm_tokens_user_id ON user_fcm_tokens(user_id);
CREATE INDEX idx_user_fcm_tokens_active ON user_fcm_tokens(is_active);
CREATE INDEX idx_user_fcm_tokens_device_id ON user_fcm_tokens USING GIN ((device_info->>'device_id'));
```

## معلومات الجهاز المحفوظة

```json
{
  "platform": "android/ios",
  "model": "device_model",
  "os_version": "system_version",
  "app_version": "app_version",
  "device_id": "unique_device_identifier"
}
```

## سيناريوهات الاستخدام

### 1. تسجيل دخول مستخدم جديد
```dart
// في AuthBloc
await _fcmService.onUserLogin(user.id);
```

### 2. تحديث Token تلقائياً
```dart
// في FCMIntegrationService
FirebaseMessaging.onTokenRefresh.listen((newToken) {
  _tokenService.refreshToken(newToken);
});
```

### 3. تسجيل الخروج
```dart
// في AuthBloc
await _fcmService.onUserLogout(userId);
```

### 4. معالجة الإشعارات
```dart
// إشعارات المقدمة
FirebaseMessaging.onMessage.listen((message) {
  _notificationService.handleForegroundMessage(message);
});

// النقر على الإشعار
FirebaseMessaging.onMessageOpenedApp.listen((message) {
  _notificationService.handleNotificationTap(message);
});
```

## الأمان والخصوصية

### 1. Row Level Security (RLS)
```sql
-- السماح للمستخدمين بالوصول لـ tokens الخاصة بهم فقط
CREATE POLICY "Users can view own tokens" ON user_fcm_tokens
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own tokens" ON user_fcm_tokens
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own tokens" ON user_fcm_tokens
  FOR UPDATE USING (auth.uid() = user_id);
```

### 2. تشفير البيانات الحساسة
- معرف الجهاز مشفر
- عدم حفظ tokens منتهية الصلاحية
- التحقق من صحة Token قبل الإرسال

## التحسينات المطبقة

### 1. Cache محلي
```dart
// حفظ Token في SharedPreferences
await _prefs.setString(_cacheKeyToken, fcmToken);

// استرجاع Token من Cache
final cachedToken = _prefs.getString(_cacheKeyToken);
```

### 2. معالجة الأخطاء
```dart
try {
  await _tokenService.saveToken(userId: userId);
} catch (e) {
  AppLogger.error('Error saving FCM token', error: e);
  // إعادة المحاولة أو معالجة بديلة
}
```

### 3. تنظيف دوري
```dart
// حذف tokens غير نشطة أقدم من 30 يوم
await _tokenService.cleanupInactiveTokens();
```

## التكامل مع Admin App

### 1. استرجاع Tokens للإرسال
```dart
// في Admin App
final tokens = await getUserFCMTokens(userId);
for (final token in tokens) {
  await sendNotification(token.fcmToken, message);
}
```

### 2. تتبع حالة الإرسال
```dart
// تسجيل نجاح/فشل الإرسال
await logNotificationResult(tokenId, success, error);
```

## الاختبار والمراقبة

### 1. طباعة Token للتطوير
```dart
await FCMIntegrationService().printCurrentToken();
```

### 2. إحصائيات Tokens
```dart
final stats = await _tokenService.getTokenStats(userId);
// {total: 3, active: 2, inactive: 1}
```

### 3. مراقبة الأخطاء
```dart
AppLogger.error('FCM Error', category: LogCategory.general, error: e);
```

## نقاط الاستدعاء الرئيسية

1. **main.dart**: تهيئة FCMIntegrationService
2. **AuthBloc**: حفظ/حذف tokens عند تسجيل الدخول/الخروج
3. **NotificationService**: معالجة الإشعارات الواردة
4. **NotificationsPage**: طباعة Token للتطوير

## المتطلبات

### Dependencies
```yaml
dependencies:
  firebase_core: ^3.14.0
  firebase_messaging: ^15.2.7
  device_info_plus: ^10.1.2
  package_info_plus: ^8.1.0
  uuid: ^4.5.1
```

### أذونات Android
```xml
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
<uses-permission android:name="android.permission.VIBRATE" />
```

### أذونات iOS
```xml
<key>UIBackgroundModes</key>
<array>
  <string>remote-notification</string>
</array>
```

## الصيانة والتطوير المستقبلي

### 1. تحديثات دورية
- مراجعة tokens غير النشطة شهرياً
- تحديث مكتبات Firebase
- مراقبة أداء النظام

### 2. تحسينات مقترحة
- إضافة تشفير إضافي للبيانات الحساسة
- تطوير dashboard لمراقبة Tokens
- إضافة analytics لاستخدام الإشعارات

### 3. استكشاف الأخطاء
- فحص logs في AppLogger
- التحقق من صحة Firebase configuration
- مراجعة أذونات قاعدة البيانات
