class WeeklyResultModel {
  final String id;
  final String patientId;
  final double? weight;
  final double? bodyFat;
  final double? visceralFat;
  final double? waterPercentage;
  final double? muscleMass;
  final DateTime recordedDate;
  final String? notes;
  final DateTime createdAt;

  const WeeklyResultModel({
    required this.id,
    required this.patientId,
    this.weight,
    this.bodyFat,
    this.visceralFat,
    this.waterPercentage,
    this.muscleMass,
    required this.recordedDate,
    this.notes,
    required this.createdAt,
  });

  factory WeeklyResultModel.fromMap(Map<String, dynamic> map) {
    return WeeklyResultModel(
      id: map['id'] as String,
      patientId: map['patient_id'] as String,
      weight: map['weight'] != null ? (map['weight'] as num).toDouble() : null,
      bodyFat: map['body_fat'] != null ? (map['body_fat'] as num).toDouble() : null,
      visceralFat: map['visceral_fat'] != null ? (map['visceral_fat'] as num).toDouble() : null,
      waterPercentage: map['water_percentage'] != null ? (map['water_percentage'] as num).toDouble() : null,
      muscleMass: map['muscle_mass'] != null ? (map['muscle_mass'] as num).toDouble() : null,
      recordedDate: DateTime.parse(map['recorded_date']),
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'patient_id': patientId,
      'weight': weight,
      'body_fat': bodyFat,
      'visceral_fat': visceralFat,
      'water_percentage': waterPercentage,
      'muscle_mass': muscleMass,
      'recorded_date': recordedDate.toIso8601String().split('T')[0],
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
  }

  WeeklyResultModel copyWith({
    String? id,
    String? patientId,
    double? weight,
    double? bodyFat,
    double? visceralFat,
    double? waterPercentage,
    double? muscleMass,
    DateTime? recordedDate,
    String? notes,
    DateTime? createdAt,
  }) {
    return WeeklyResultModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      weight: weight ?? this.weight,
      bodyFat: bodyFat ?? this.bodyFat,
      visceralFat: visceralFat ?? this.visceralFat,
      waterPercentage: waterPercentage ?? this.waterPercentage,
      muscleMass: muscleMass ?? this.muscleMass,
      recordedDate: recordedDate ?? this.recordedDate,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WeeklyResultModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'WeeklyResultModel(id: $id, patientId: $patientId, weight: $weight, recordedDate: $recordedDate)';
  }

  // Helper methods
  String get displayWeight => weight != null ? '${weight!.toStringAsFixed(1)} كجم' : 'غير محدد';
  String get displayBodyFat => bodyFat != null ? '${bodyFat!.toStringAsFixed(1)}%' : 'غير محدد';
  String get displayVisceralFat => visceralFat != null ? '${visceralFat!.toStringAsFixed(1)}%' : 'غير محدد';
  String get displayWaterPercentage => waterPercentage != null ? '${waterPercentage!.toStringAsFixed(1)}%' : 'غير محدد';
  String get displayMuscleMass => muscleMass != null ? '${muscleMass!.toStringAsFixed(1)} كجم' : 'غير محدد';
  String get displayRecordedDate => '${recordedDate.day}/${recordedDate.month}/${recordedDate.year}';
  
  // BMI calculation if height is provided separately
  double? calculateBMI(double? height) {
    if (weight != null && height != null && height > 0) {
      final heightInMeters = height / 100;
      return weight! / (heightInMeters * heightInMeters);
    }
    return null;
  }
  
  // Body composition analysis
  String get bodyCompositionStatus {
    if (bodyFat == null) return 'غير محدد';
    
    if (bodyFat! < 10) return 'نسبة دهون منخفضة جداً';
    if (bodyFat! < 15) return 'نسبة دهون منخفضة';
    if (bodyFat! < 20) return 'نسبة دهون طبيعية';
    if (bodyFat! < 25) return 'نسبة دهون مرتفعة قليلاً';
    return 'نسبة دهون مرتفعة';
  }
  
  String get visceralFatStatus {
    if (visceralFat == null) return 'غير محدد';
    
    if (visceralFat! < 10) return 'طبيعي';
    if (visceralFat! < 15) return 'مرتفع قليلاً';
    return 'مرتفع - يحتاج متابعة';
  }
  
  String get hydrationStatus {
    if (waterPercentage == null) return 'غير محدد';
    
    if (waterPercentage! < 50) return 'نقص في الترطيب';
    if (waterPercentage! < 60) return 'ترطيب طبيعي';
    return 'ترطيب جيد';
  }
}
