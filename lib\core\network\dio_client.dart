import 'package:dio/dio.dart';
import 'package:logger/logger.dart';
import '../constants/app_constants.dart';
import '../errors/exceptions.dart';

/// عميل Dio للتعامل مع طلبات الشبكة
class DioClient {
  late final Dio _dio;
  final Logger _logger = Logger();

  DioClient() {
    _dio = Dio();
    _setupDio();
  }

  /// إعداد Dio
  void _setupDio() {
    _dio.options = BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: AppConstants.connectTimeout,
      receiveTimeout: AppConstants.receiveTimeout,
      sendTimeout: AppConstants.sendTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'apikey': AppConstants.supabaseAnonKey,
        'Authorization': 'Bearer ${AppConstants.supabaseAnonKey}',
      },
    );

    // إضافة Interceptors
    _dio.interceptors.add(_createLoggingInterceptor());
    _dio.interceptors.add(_createErrorInterceptor());
    _dio.interceptors.add(_createRetryInterceptor());
  }

  /// إنشاء Logging Interceptor
  Interceptor _createLoggingInterceptor() {
    return LogInterceptor(
      requestBody: true,
      responseBody: true,
      logPrint: (object) => _logger.d(object),
    );
  }

  /// إنشاء Error Interceptor
  Interceptor _createErrorInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) {
        _logger.e('Dio Error: ${error.message}');

        final exception = _handleDioError(error);
        handler.reject(DioException(
          requestOptions: error.requestOptions,
          error: exception,
          type: error.type,
          response: error.response,
        ));
      },
    );
  }

  /// إنشاء Retry Interceptor
  Interceptor _createRetryInterceptor() {
    return InterceptorsWrapper(
      onError: (error, handler) async {
        if (_shouldRetry(error)) {
          try {
            await Future.delayed(AppConstants.retryDelay);
            final response = await _dio.fetch(error.requestOptions);
            handler.resolve(response);
          } catch (e) {
            handler.next(error);
          }
        } else {
          handler.next(error);
        }
      },
    );
  }

  /// التحقق من إمكانية إعادة المحاولة
  bool _shouldRetry(DioException error) {
    return error.type == DioExceptionType.connectionTimeout ||
           error.type == DioExceptionType.receiveTimeout ||
           error.type == DioExceptionType.sendTimeout ||
           (error.response?.statusCode != null &&
            error.response!.statusCode! >= 500);
  }

  /// معالجة أخطاء Dio
  AppException _handleDioError(DioException error) {
    switch (error.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        return const TimeoutException(
          message: 'انتهت مهلة الاتصال',
          code: 408,
        );

      case DioExceptionType.badResponse:
        return _handleResponseError(error.response!);

      case DioExceptionType.cancel:
        return const NetworkException(
          message: 'تم إلغاء الطلب',
          code: 0,
        );

      case DioExceptionType.connectionError:
        return const NetworkException(
          message: 'خطأ في الاتصال بالشبكة',
          code: 0,
        );

      case DioExceptionType.badCertificate:
        return const NetworkException(
          message: 'خطأ في شهادة الأمان',
          code: 0,
        );

      case DioExceptionType.unknown:
        return NetworkException(
          message: error.message ?? 'خطأ غير معروف',
          code: 0,
        );
    }
  }

  /// معالجة أخطاء الاستجابة
  AppException _handleResponseError(Response response) {
    final statusCode = response.statusCode ?? 0;
    final data = response.data;

    String message = 'حدث خطأ في الخادم';

    if (data is Map<String, dynamic>) {
      message = data['message'] ??
                data['error'] ??
                data['details'] ??
                message;
    }

    switch (statusCode) {
      case 400:
        return ValidationException(
          message: message,
          code: statusCode,
        );

      case 401:
        return AuthException(
          message: 'غير مصرح لك بالوصول',
          code: statusCode,
        );

      case 403:
        return PermissionException(
          message: 'ليس لديك صلاحية للوصول',
          code: statusCode,
        );

      case 404:
        return NotFoundException(
          message: 'البيانات المطلوبة غير موجودة',
          code: statusCode,
        );

      case 422:
        return ValidationException(
          message: message,
          code: statusCode,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        return ServerException(
          message: 'خطأ في الخادم',
          code: statusCode,
        );

      default:
        return ServerException(
          message: message,
          code: statusCode,
        );
    }
  }

  /// تحديث رأس التفويض
  void updateAuthToken(String token) {
    _dio.options.headers['Authorization'] = 'Bearer $token';
  }

  /// إزالة رأس التفويض
  void removeAuthToken() {
    _dio.options.headers.remove('Authorization');
  }

  /// الحصول على عميل Dio
  Dio get dio => _dio;

  /// طلب GET
  Future<Response<T>> get<T>(
    String path, {
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.get<T>(
        path,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// طلب POST
  Future<Response<T>> post<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.post<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// طلب PUT
  Future<Response<T>> put<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.put<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// طلب PATCH
  Future<Response<T>> patch<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
    ProgressCallback? onSendProgress,
    ProgressCallback? onReceiveProgress,
  }) async {
    try {
      return await _dio.patch<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
        onSendProgress: onSendProgress,
        onReceiveProgress: onReceiveProgress,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }

  /// طلب DELETE
  Future<Response<T>> delete<T>(
    String path, {
    dynamic data,
    Map<String, dynamic>? queryParameters,
    Options? options,
    CancelToken? cancelToken,
  }) async {
    try {
      return await _dio.delete<T>(
        path,
        data: data,
        queryParameters: queryParameters,
        options: options,
        cancelToken: cancelToken,
      );
    } on DioException catch (e) {
      throw _handleDioError(e);
    }
  }
}
