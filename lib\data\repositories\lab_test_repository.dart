import 'package:supabase_flutter/supabase_flutter.dart';
import '../../domain/entities/lab_test.dart';
import '../../core/errors/failures.dart';

/// Repository للتعامل مع بيانات التحاليل الطبية في Supabase
class LabTestRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// إنشاء تحليل طبي جديد
  Future<LabTest> createLabTest({
    required String patientId,
    required String testName,
    required String testType,
    required DateTime testDate,
    String? labName,
    String? doctorId,
    String? doctorName,
    Map<String, dynamic>? results,
    Map<String, dynamic>? referenceRanges,
    String? status,
    String? notes,
    List<String>? attachments,
  }) async {
    try {
      final response = await _supabase
          .from('lab_tests')
          .insert({
            'patient_id': patientId,
            'test_name': testName,
            'test_type': testType,
            'test_date': testDate.toIso8601String(),
            'lab_name': labName,
            'doctor_id': doctorId,
            'doctor_name': doctor<PERSON><PERSON>,
            'results': results,
            'reference_ranges': referenceRanges,
            'status': status ?? 'pending',
            'notes': notes,
            'attachments': attachments,
            'created_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .select()
          .single();

      return LabTest.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في إنشاء التحليل الطبي: ${e.toString()}');
    }
  }

  /// الحصول على التحاليل الطبية للمريض
  Future<List<LabTest>> getPatientLabTests({
    required String patientId,
    String? testType,
    String? status,
    DateTime? fromDate,
    DateTime? toDate,
    int? limit,
    int? offset,
  }) async {
    try {
      var query = _supabase
          .from('lab_tests')
          .select()
          .eq('patient_id', patientId);

      if (testType != null) {
        query = query.eq('test_type', testType);
      }

      if (status != null) {
        query = query.eq('status', status);
      }

      if (fromDate != null) {
        query = query.gte('test_date', fromDate.toIso8601String());
      }

      if (toDate != null) {
        query = query.lte('test_date', toDate.toIso8601String());
      }

      var orderedQuery = query.order('test_date', ascending: false);

      if (limit != null) {
        orderedQuery = orderedQuery.limit(limit);
      }

      if (offset != null) {
        orderedQuery = orderedQuery.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await orderedQuery;

      return response.map((json) => LabTest.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب التحاليل الطبية: ${e.toString()}');
    }
  }

  /// الحصول على تحليل طبي بواسطة ID
  Future<LabTest?> getLabTestById(String testId) async {
    try {
      final response = await _supabase
          .from('lab_tests')
          .select()
          .eq('id', testId)
          .maybeSingle();

      if (response == null) return null;

      return LabTest.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب التحليل الطبي: ${e.toString()}');
    }
  }

  /// تحديث تحليل طبي
  Future<LabTest> updateLabTest({
    required String testId,
    String? testName,
    String? testType,
    DateTime? testDate,
    String? labName,
    String? doctorId,
    String? doctorName,
    Map<String, dynamic>? results,
    Map<String, dynamic>? referenceRanges,
    String? status,
    String? notes,
    List<String>? attachments,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (testName != null) updateData['test_name'] = testName;
      if (testType != null) updateData['test_type'] = testType;
      if (testDate != null) updateData['test_date'] = testDate.toIso8601String();
      if (labName != null) updateData['lab_name'] = labName;
      if (doctorId != null) updateData['doctor_id'] = doctorId;
      if (doctorName != null) updateData['doctor_name'] = doctorName;
      if (results != null) updateData['results'] = results;
      if (referenceRanges != null) updateData['reference_ranges'] = referenceRanges;
      if (status != null) updateData['status'] = status;
      if (notes != null) updateData['notes'] = notes;
      if (attachments != null) updateData['attachments'] = attachments;

      final response = await _supabase
          .from('lab_tests')
          .update(updateData)
          .eq('id', testId)
          .select()
          .single();

      return LabTest.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في تحديث التحليل الطبي: ${e.toString()}');
    }
  }

  /// حذف تحليل طبي
  Future<void> deleteLabTest(String testId) async {
    try {
      await _supabase
          .from('lab_tests')
          .delete()
          .eq('id', testId);
    } catch (e) {
      throw ServerFailure(message: 'فشل في حذف التحليل الطبي: ${e.toString()}');
    }
  }

  /// البحث في التحاليل الطبية
  Future<List<LabTest>> searchLabTests({
    required String patientId,
    String? searchTerm,
    String? testType,
    String? status,
    String? labName,
  }) async {
    try {
      var query = _supabase
          .from('lab_tests')
          .select()
          .eq('patient_id', patientId);

      if (testType != null) {
        query = query.eq('test_type', testType);
      }

      if (status != null) {
        query = query.eq('status', status);
      }

      if (labName != null) {
        query = query.ilike('lab_name', '%$labName%');
      }

      if (searchTerm != null && searchTerm.isNotEmpty) {
        query = query.or('test_name.ilike.%$searchTerm%,notes.ilike.%$searchTerm%');
      }

      final response = await query.order('test_date', ascending: false);

      return response.map((json) => LabTest.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في البحث في التحاليل الطبية: ${e.toString()}');
    }
  }

  /// الحصول على أنواع التحاليل المتاحة
  Future<List<String>> getAvailableTestTypes(String patientId) async {
    try {
      final response = await _supabase
          .from('lab_tests')
          .select('test_type')
          .eq('patient_id', patientId);

      final types = response
          .map((item) => item['test_type'] as String)
          .toSet()
          .toList();

      types.sort();
      return types;
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب أنواع التحاليل: ${e.toString()}');
    }
  }

  /// الحصول على حالات التحاليل المتاحة
  Future<List<String>> getAvailableStatuses(String patientId) async {
    try {
      final response = await _supabase
          .from('lab_tests')
          .select('status')
          .eq('patient_id', patientId);

      final statuses = response
          .map((item) => item['status'] as String)
          .toSet()
          .toList();

      statuses.sort();
      return statuses;
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب حالات التحاليل: ${e.toString()}');
    }
  }

  /// الحصول على إحصائيات التحاليل الطبية
  Future<Map<String, dynamic>> getLabTestStats(String patientId) async {
    try {
      final response = await _supabase
          .from('lab_tests')
          .select('test_type, status, test_date')
          .eq('patient_id', patientId);

      final stats = {
        'total': response.length,
        'types': <String, int>{},
        'statuses': <String, int>{},
        'thisMonth': 0,
        'thisYear': 0,
      };

      final now = DateTime.now();
      final thisMonth = DateTime(now.year, now.month);
      final thisYear = DateTime(now.year);

      for (final test in response) {
        final testType = test['test_type'] as String;
        final status = test['status'] as String;

        final types = stats['types'] as Map<String, int>;
        types[testType] = (types[testType] ?? 0) + 1;

        final statuses = stats['statuses'] as Map<String, int>;
        statuses[status] = (statuses[status] ?? 0) + 1;

        final testDate = DateTime.parse(test['test_date'] as String);
        if (testDate.isAfter(thisMonth)) {
          stats['thisMonth'] = (stats['thisMonth'] as int) + 1;
        }
        if (testDate.isAfter(thisYear)) {
          stats['thisYear'] = (stats['thisYear'] as int) + 1;
        }
      }

      return stats;
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب إحصائيات التحاليل الطبية: ${e.toString()}');
    }
  }

  /// الحصول على آخر التحاليل الطبية
  Future<List<LabTest>> getRecentLabTests({
    required String patientId,
    int limit = 5,
  }) async {
    try {
      final response = await _supabase
          .from('lab_tests')
          .select()
          .eq('patient_id', patientId)
          .order('test_date', ascending: false)
          .limit(limit);

      return response.map((json) => LabTest.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب آخر التحاليل الطبية: ${e.toString()}');
    }
  }

  /// الحصول على التحاليل المعلقة
  Future<List<LabTest>> getPendingLabTests(String patientId) async {
    try {
      final response = await _supabase
          .from('lab_tests')
          .select()
          .eq('patient_id', patientId)
          .eq('status', 'pending')
          .order('test_date', ascending: true);

      return response.map((json) => LabTest.fromJson(json)).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في جلب التحاليل المعلقة: ${e.toString()}');
    }
  }

  /// تحديث حالة التحليل
  Future<LabTest> updateTestStatus({
    required String testId,
    required String status,
    Map<String, dynamic>? results,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'status': status,
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (results != null) {
        updateData['results'] = results;
      }

      final response = await _supabase
          .from('lab_tests')
          .update(updateData)
          .eq('id', testId)
          .select()
          .single();

      return LabTest.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في تحديث حالة التحليل: ${e.toString()}');
    }
  }

  /// إضافة نتائج التحليل
  Future<LabTest> addTestResults({
    required String testId,
    required Map<String, dynamic> results,
    Map<String, dynamic>? referenceRanges,
  }) async {
    try {
      final updateData = <String, dynamic>{
        'results': results,
        'status': 'completed',
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (referenceRanges != null) {
        updateData['reference_ranges'] = referenceRanges;
      }

      final response = await _supabase
          .from('lab_tests')
          .update(updateData)
          .eq('id', testId)
          .select()
          .single();

      return LabTest.fromJson(response);
    } catch (e) {
      throw ServerFailure(message: 'فشل في إضافة نتائج التحليل: ${e.toString()}');
    }
  }

  /// تصدير التحاليل الطبية
  Future<List<Map<String, dynamic>>> exportLabTests({
    required String patientId,
    DateTime? fromDate,
    DateTime? toDate,
    String? testType,
  }) async {
    try {
      var query = _supabase
          .from('lab_tests')
          .select()
          .eq('patient_id', patientId);

      if (testType != null) {
        query = query.eq('test_type', testType);
      }

      if (fromDate != null) {
        query = query.gte('test_date', fromDate.toIso8601String());
      }

      if (toDate != null) {
        query = query.lte('test_date', toDate.toIso8601String());
      }

      final response = await query.order('test_date', ascending: true);

      return response.map((json) => {
        'التاريخ': json['test_date'],
        'اسم التحليل': json['test_name'],
        'النوع': json['test_type'],
        'المختبر': json['lab_name'] ?? 'غير محدد',
        'الطبيب': json['doctor_name'] ?? 'غير محدد',
        'الحالة': json['status'],
        'الملاحظات': json['notes'] ?? '',
      }).toList();
    } catch (e) {
      throw ServerFailure(message: 'فشل في تصدير التحاليل الطبية: ${e.toString()}');
    }
  }
}
