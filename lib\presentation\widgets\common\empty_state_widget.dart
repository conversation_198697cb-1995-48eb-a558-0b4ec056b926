import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';

/// ويدجت الحالة الفارغة
class EmptyStateWidget extends StatelessWidget {
  final IconData icon;
  final String title;
  final String? subtitle;
  final String? buttonText;
  final VoidCallback? onButtonPressed;
  final Color? iconColor;
  final double? iconSize;

  const EmptyStateWidget({
    super.key,
    required this.icon,
    required this.title,
    this.subtitle,
    this.buttonText,
    this.onButtonPressed,
    this.iconColor,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(32.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // الأيقونة
            Container(
              width: (iconSize ?? 80).w,
              height: (iconSize ?? 80).h,
              decoration: BoxDecoration(
                color: (iconColor ?? AppColors.primary).withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: (iconSize ?? 80).sp * 0.6,
                color: iconColor ?? AppColors.primary,
              ),
            ),

            SizedBox(height: 24.h),

            // العنوان
            Text(
              title,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
                fontFamily: 'Cairo',
              ),
              textAlign: TextAlign.center,
            ),

            // العنوان الفرعي
            if (subtitle != null) ...[
              SizedBox(height: 8.h),
              Text(
                subtitle!,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.textSecondary,
                  fontFamily: 'Cairo',
                ),
                textAlign: TextAlign.center,
              ),
            ],

            // الزر
            if (buttonText != null && onButtonPressed != null) ...[
              SizedBox(height: 24.h),
              ElevatedButton(
                onPressed: onButtonPressed,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: AppColors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: 24.w,
                    vertical: 12.h,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                ),
                child: Text(
                  buttonText!,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    fontFamily: 'Cairo',
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// ويدجت حالة فارغة للقائمة
class EmptyListWidget extends StatelessWidget {
  final String message;
  final IconData? icon;

  const EmptyListWidget({
    super.key,
    required this.message,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: icon ?? Icons.inbox_outlined,
      title: message,
      iconSize: 60,
    );
  }
}

/// ويدجت حالة فارغة للبحث
class EmptySearchWidget extends StatelessWidget {
  final String searchQuery;

  const EmptySearchWidget({
    super.key,
    required this.searchQuery,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.search_off,
      title: 'لا توجد نتائج',
      subtitle: 'لم نجد أي نتائج لـ "$searchQuery"',
      iconSize: 60,
    );
  }
}

/// ويدجت حالة خطأ
class ErrorStateWidget extends StatelessWidget {
  final String title;
  final String? subtitle;
  final VoidCallback? onRetry;

  const ErrorStateWidget({
    super.key,
    required this.title,
    this.subtitle,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.error_outline,
      title: title,
      subtitle: subtitle,
      buttonText: onRetry != null ? 'إعادة المحاولة' : null,
      onButtonPressed: onRetry,
      iconColor: AppColors.error,
      iconSize: 60,
    );
  }
}

/// ويدجت حالة عدم وجود اتصال
class NoConnectionWidget extends StatelessWidget {
  final VoidCallback? onRetry;

  const NoConnectionWidget({
    super.key,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.wifi_off,
      title: 'لا يوجد اتصال بالإنترنت',
      subtitle: 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى',
      buttonText: onRetry != null ? 'إعادة المحاولة' : null,
      onButtonPressed: onRetry,
      iconColor: AppColors.warning,
      iconSize: 60,
    );
  }
}

/// ويدجت حالة الصيانة
class MaintenanceWidget extends StatelessWidget {
  const MaintenanceWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return const EmptyStateWidget(
      icon: Icons.build,
      title: 'الخدمة تحت الصيانة',
      subtitle: 'نعتذر عن الإزعاج، سنعود قريباً',
      iconColor: AppColors.warning,
      iconSize: 60,
    );
  }
}

/// ويدجت حالة قريباً
class ComingSoonWidget extends StatelessWidget {
  final String? feature;

  const ComingSoonWidget({
    super.key,
    this.feature,
  });

  @override
  Widget build(BuildContext context) {
    return EmptyStateWidget(
      icon: Icons.schedule,
      title: feature != null ? '$feature قريباً' : 'قريباً',
      subtitle: 'نعمل على إضافة هذه الميزة',
      iconColor: AppColors.info,
      iconSize: 60,
    );
  }
}
