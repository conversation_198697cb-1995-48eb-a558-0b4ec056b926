import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../data/models/weekly_result_model.dart';
import '../../../../data/models/patient_model.dart';
import '../../../widgets/common/filter_button.dart';

class WeeklyResultsTab extends StatefulWidget {
  final List<WeeklyResultModel> weeklyResults;
  final PatientModel? patient;

  const WeeklyResultsTab({
    super.key,
    required this.weeklyResults,
    this.patient,
  });

  @override
  State<WeeklyResultsTab> createState() => _WeeklyResultsTabState();
}

class _WeeklyResultsTabState extends State<WeeklyResultsTab> {
  String selectedFilter = 'latest';

  final Map<String, String> filterOptions = {
    'latest': 'أحدث النتائج',
    'all': 'جميع النتائج',
  };

  @override
  Widget build(BuildContext context) {
    if (widget.weeklyResults.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        _buildFilterChips(),
        Expanded(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(16.w),
            child: _buildContent(),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterChips() {
    return Container(
      height: 60.h,
      padding: EdgeInsets.symmetric(vertical: 8.h),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        itemCount: filterOptions.length,
        separatorBuilder: (context, index) => SizedBox(width: 8.w),
        itemBuilder: (context, index) {
          final key = filterOptions.keys.elementAt(index);
          final label = filterOptions[key]!;
          final isSelected = selectedFilter == key;

          return MedicalFilterButton(
            text: label,
            isSelected: isSelected,
            onTap: () {
              setState(() {
                selectedFilter = key;
              });
            },
          );
        },
      ),
    );
  }

  Widget _buildContent() {
    if (selectedFilter == 'latest') {
      return _buildLatestResultsSection();
    } else {
      return _buildAllResultsSection();
    }
  }

  Widget _buildLatestResultsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLatestResultCard(),
        SizedBox(height: 16.h),
        if (widget.weeklyResults.length > 1) ...[
          _buildChartsSection(),
          SizedBox(height: 16.h),
        ],
      ],
    );
  }

  Widget _buildAllResultsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [_buildResultsHistory()],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.trending_up_outlined,
            size: 80.sp,
            color: AppColors.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد نتائج أسبوعية',
            style: TextStyle(fontSize: 18.sp, color: AppColors.textSecondary),
          ),
          SizedBox(height: 8.h),
          Text(
            'سيتم عرض النتائج الأسبوعية هنا عند توفرها',
            style: TextStyle(fontSize: 14.sp, color: AppColors.textSecondary),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLatestResultCard() {
    final latestResult = widget.weeklyResults.first;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.primary,
              AppColors.primary.withValues(alpha: 0.8),
            ],
          ),
        ),
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: Colors.white, size: 24.sp),
                SizedBox(width: 8.w),
                Text(
                  'أحدث النتائج',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    latestResult.displayRecordedDate,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20.h),
            _buildLatestResultMetrics(latestResult),
          ],
        ),
      ),
    );
  }

  Widget _buildLatestResultMetrics(WeeklyResultModel result) {
    return Column(
      children: [
        _buildMetricCard(
          'الوزن',
          result.displayWeight,
          Icons.monitor_weight,
          Colors.white,
        ),
        SizedBox(height: 12.h),
        _buildMetricCard(
          'دهون الجسم',
          result.displayBodyFat,
          Icons.fitness_center,
          Colors.white,
        ),
        SizedBox(height: 12.h),
        _buildMetricCard(
          'الكتلة العضلية',
          result.displayMuscleMass,
          Icons.accessibility_new,
          Colors.white,
        ),
        SizedBox(height: 12.h),
        _buildMetricCard(
          'نسبة السوائل',
          result.displayWaterPercentage,
          Icons.water_drop,
          Colors.white,
        ),
        SizedBox(height: 12.h),
        _buildMetricCard(
          'الدهون الحشوية',
          result.displayVisceralFat,
          Icons.warning_amber,
          Colors.white,
        ),
      ],
    );
  }

  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color textColor,
  ) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.white.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(icon, color: textColor, size: 24.sp),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                fontSize: 12.sp,
                color: textColor.withValues(alpha: 0.8),
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: textColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem(String title, String value, IconData icon) {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary, size: 20.sp),
          SizedBox(width: 12.w),
          Expanded(
            child: Text(
              title,
              style: TextStyle(fontSize: 12.sp, color: AppColors.textSecondary),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.bold,
              color: AppColors.textPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الرسوم البيانية',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 12.h),
        _buildWeightChart(),
        SizedBox(height: 16.h),
        _buildBodyCompositionChart(),
      ],
    );
  }

  Widget _buildWeightChart() {
    final weightData =
        widget.weeklyResults.reversed.toList().asMap().entries.map((entry) {
          final index = entry.key;
          final result = entry.value;
          return FlSpot(index.toDouble(), result.weight ?? 0);
        }).toList();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.trending_up, color: AppColors.primary, size: 20.sp),
                SizedBox(width: 8.w),
                Text(
                  'تطور الوزن',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            SizedBox(
              height: 200.h,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: false,
                    horizontalInterval: 5,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(color: AppColors.border, strokeWidth: 1);
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    topTitles: AxisTitles(
                      sideTitles: SideTitles(showTitles: false),
                    ),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        interval: 1,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          final index = value.toInt();
                          if (index >= 0 &&
                              index < widget.weeklyResults.length) {
                            final result =
                                widget.weeklyResults.reversed.toList()[index];
                            return Transform.rotate(
                              angle: -0.785398, // 45 degrees in radians
                              child: Text(
                                result.displayRecordedDate.split(' ')[0],
                                style: TextStyle(
                                  color: AppColors.textSecondary,
                                  fontSize: 8.sp,
                                ),
                              ),
                            );
                          }
                          return Container();
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        interval: 5,
                        reservedSize: 40,
                        getTitlesWidget: (double value, TitleMeta meta) {
                          return Text(
                            '${value.toInt()}',
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 10.sp,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(color: AppColors.border),
                  ),
                  minX: 0,
                  maxX: (widget.weeklyResults.length - 1).toDouble(),
                  minY:
                      weightData
                          .map((e) => e.y)
                          .reduce((a, b) => a < b ? a : b) -
                      5,
                  maxY:
                      weightData
                          .map((e) => e.y)
                          .reduce((a, b) => a > b ? a : b) +
                      5,
                  lineBarsData: [
                    LineChartBarData(
                      spots: weightData,
                      isCurved: true,
                      color: AppColors.primary,
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 4,
                            color: AppColors.primary,
                            strokeWidth: 2,
                            strokeColor: Colors.white,
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        color: AppColors.primary.withValues(alpha: 0.1),
                      ),
                    ),
                  ],
                  lineTouchData: LineTouchData(
                    enabled: true,
                    touchTooltipData: LineTouchTooltipData(
                      getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                        return touchedBarSpots.map((barSpot) {
                          final index = barSpot.x.toInt();
                          if (index >= 0 &&
                              index < widget.weeklyResults.length) {
                            final result =
                                widget.weeklyResults.reversed.toList()[index];
                            return LineTooltipItem(
                              '${result.displayWeight}\n${result.displayRecordedDate}',
                              TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12.sp,
                              ),
                            );
                          }
                          return null;
                        }).toList();
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBodyCompositionChart() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.pie_chart, color: AppColors.primary, size: 20.sp),
                SizedBox(width: 8.w),
                Text(
                  'تركيب الجسم - أحدث قياس',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            SizedBox(
              height: 200.h,
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: PieChart(
                      PieChartData(
                        sectionsSpace: 2,
                        centerSpaceRadius: 40,
                        sections: _buildPieChartSections(),
                      ),
                    ),
                  ),
                  SizedBox(width: 16.w),
                  Expanded(child: _buildChartLegend()),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections() {
    final latestResult = widget.weeklyResults.first;
    final bodyFat = latestResult.bodyFat ?? 0;
    final muscleMass = latestResult.muscleMass ?? 0;
    final waterPercentage = latestResult.waterPercentage ?? 0;
    final visceralFat = latestResult.visceralFat ?? 0;
    final other = 100 - bodyFat - muscleMass - waterPercentage - visceralFat;

    return [
      PieChartSectionData(
        color: Colors.red.withValues(alpha: 0.8),
        value: bodyFat,
        title: '${bodyFat.toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.green.withValues(alpha: 0.8),
        value: muscleMass,
        title: '${muscleMass.toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.blue.withValues(alpha: 0.8),
        value: waterPercentage,
        title: '${waterPercentage.toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      PieChartSectionData(
        color: Colors.orange.withValues(alpha: 0.8),
        value: visceralFat,
        title: '${visceralFat.toStringAsFixed(1)}%',
        radius: 50,
        titleStyle: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      ),
      if (other > 0)
        PieChartSectionData(
          color: Colors.grey.withValues(alpha: 0.6),
          value: other,
          title: '${other.toStringAsFixed(1)}%',
          radius: 50,
          titleStyle: TextStyle(
            fontSize: 12.sp,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
    ];
  }

  Widget _buildChartLegend() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildLegendItem('دهون الجسم', Colors.red.withValues(alpha: 0.8)),
        SizedBox(height: 8.h),
        _buildLegendItem('الكتلة العضلية', Colors.green.withValues(alpha: 0.8)),
        SizedBox(height: 8.h),
        _buildLegendItem('نسبة السوائل', Colors.blue.withValues(alpha: 0.8)),
        SizedBox(height: 8.h),
        _buildLegendItem(
          'الدهون الحشوية',
          Colors.orange.withValues(alpha: 0.8),
        ),
        SizedBox(height: 8.h),
        _buildLegendItem('أخرى', Colors.grey.withValues(alpha: 0.6)),
      ],
    );
  }

  Widget _buildLegendItem(String title, Color color) {
    return Row(
      children: [
        Container(
          width: 12.w,
          height: 12.h,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            title,
            style: TextStyle(fontSize: 12.sp, color: AppColors.textPrimary),
          ),
        ),
      ],
    );
  }

  Widget _buildResultsHistory() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'تاريخ النتائج',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        SizedBox(height: 12.h),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: widget.weeklyResults.length,
          separatorBuilder: (context, index) => SizedBox(height: 12.h),
          itemBuilder: (context, index) {
            final result = widget.weeklyResults[index];
            return _buildResultHistoryCard(result, index == 0);
          },
        ),
      ],
    );
  }

  Widget _buildResultHistoryCard(WeeklyResultModel result, bool isLatest) {
    return Card(
      elevation: isLatest ? 3 : 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.r),
          border:
              isLatest ? Border.all(color: AppColors.primary, width: 2) : null,
        ),
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: isLatest ? AppColors.primary : AppColors.textSecondary,
                  size: 16.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  result.displayRecordedDate,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: isLatest ? AppColors.primary : AppColors.textPrimary,
                  ),
                ),
                if (isLatest) ...[
                  const Spacer(),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 8.w,
                      vertical: 2.h,
                    ),
                    decoration: BoxDecoration(
                      color: AppColors.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      'الأحدث',
                      style: TextStyle(
                        fontSize: 10.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ],
              ],
            ),
            SizedBox(height: 12.h),
            _buildResultMetrics(result),
            if (result.notes != null && result.notes!.isNotEmpty) ...[
              SizedBox(height: 12.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  color: AppColors.background,
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: AppColors.border),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'ملاحظات:',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      result.notes!,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildResultMetrics(WeeklyResultModel result) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricItem(
                'الوزن',
                result.displayWeight,
                Icons.monitor_weight,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildMetricItem(
                'دهون الجسم',
                result.displayBodyFat,
                Icons.fitness_center,
              ),
            ),
          ],
        ),
        SizedBox(height: 8.h),
        Row(
          children: [
            Expanded(
              child: _buildMetricItem(
                'الكتلة العضلية',
                result.displayMuscleMass,
                Icons.accessibility_new,
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildMetricItem(
                'نسبة السوائل',
                result.displayWaterPercentage,
                Icons.water_drop,
              ),
            ),
          ],
        ),
        if (result.visceralFat != null) ...[
          SizedBox(height: 8.h),
          _buildMetricItem(
            'الدهون الحشوية',
            result.displayVisceralFat,
            Icons.warning,
          ),
        ],
      ],
    );
  }
}
