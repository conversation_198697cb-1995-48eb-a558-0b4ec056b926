# 🔥 تعليمات تحديث Firebase Console - Package Name الجديد

## 📱 **Package Names الجديدة:**
- **Android**: `com.deitrxone`
- **iOS**: `com.deitrxone`

---

## 🤖 **تحديث Android App في Firebase:**

### **1. الدخول إلى Firebase Console:**
```
1. اذهب إلى: https://console.firebase.google.com
2. اختر مشروع: deit-rx-30741
3. اذهب إلى Project Settings (أيقونة الترس ⚙️)
4. اختر تبويب "General"
```

### **2. تحديث Android App:**
```
في قسم "Your apps":
1. ابحث عن Android app الحالي
2. اضغط على أيقونة الإعدادات (ثلاث نقاط)
3. اختر "App settings"
4. غير Package name من: com.deit_rx_one
5. إلى: com.deitrxone
6. احفظ التغييرات
```

### **3. تحميل google-services.json الجديد:**
```
1. في نفس صفحة App settings
2. اضغط "Download google-services.json"
3. استبدل الملف في: android/app/google-services.json
```

---

## 🍎 **تحديث iOS App في Firebase:**

### **1. تحديث iOS App:**
```
في قسم "Your apps":
1. ابحث عن iOS app الحالي
2. اضغط على أيقونة الإعدادات (ثلاث نقاط)
3. اختر "App settings"
4. غير Bundle ID من: com.deit_rx_one
5. إلى: com.deitrxone
6. احفظ التغييرات
```

### **2. تحميل GoogleService-Info.plist الجديد:**
```
1. في نفس صفحة App settings
2. اضغط "Download GoogleService-Info.plist"
3. استبدل الملف في: ios/Runner/GoogleService-Info.plist
```

---

## 🔄 **البديل: إضافة Apps جديدة (إذا لم يعمل التحديث):**

### **Android:**
```
1. في Firebase Console > Project Settings
2. اضغط "Add app" > اختر Android
3. أدخل Package name: com.deitrxone
4. أدخل App nickname: Diet RX Android
5. اضغط "Register app"
6. حمل google-services.json الجديد
7. احذف Android app القديم (اختياري)
```

### **iOS:**
```
1. في Firebase Console > Project Settings
2. اضغط "Add app" > اختر iOS
3. أدخل Bundle ID: com.deitrxone
4. أدخل App nickname: Diet RX iOS
5. اضغط "Register app"
6. حمل GoogleService-Info.plist الجديد
7. احذف iOS app القديم (اختياري)
```

---

## ✅ **التحقق من نجاح التحديث:**

### **1. فحص الملفات:**
```bash
# Android
grep "com.deitrxone" android/app/google-services.json

# iOS
grep "com.deitrxone" ios/Runner/GoogleService-Info.plist
```

### **2. اختبار Firebase:**
```dart
// في التطبيق
await Firebase.initializeApp(
  options: DefaultFirebaseOptions.currentPlatform,
);
print('✅ Firebase initialized successfully');
```

### **3. اختبار FCM:**
```dart
final token = await FirebaseMessaging.instance.getToken();
print('🔑 FCM Token: $token');
```

---

## 🚨 **ملاحظات مهمة:**

### **⚠️ قبل التحديث:**
- احفظ نسخة احتياطية من الملفات الحالية
- تأكد من أن لديك صلاحيات Admin على Firebase
- اختبر على جهاز تطوير أولاً

### **⚠️ بعد التحديث:**
- نظف المشروع: `flutter clean`
- أعد تحميل dependencies: `flutter pub get`
- اختبر جميع ميزات Firebase
- اختبر الإشعارات على جهاز حقيقي

### **⚠️ إذا واجهت مشاكل:**
- تأكد من تطابق Package Names في جميع الملفات
- تأكد من أن Firebase project يحتوي على Apps الجديدة
- تحقق من Cloud Messaging settings
- راجع console logs للأخطاء

---

## 📞 **الدعم:**
إذا واجهت مشاكل:
1. تحقق من Firebase Console logs
2. راجع Android Studio/Xcode logs
3. تأكد من صحة Package Names
4. اختبر على جهاز حقيقي وليس المحاكي فقط
