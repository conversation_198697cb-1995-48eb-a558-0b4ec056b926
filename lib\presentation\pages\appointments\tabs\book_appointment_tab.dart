import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/utils/helpers.dart';
import '../../../../data/models/holiday_model.dart';
import '../../../../data/models/time_slot_model.dart';
import '../../../../data/services/appointments_service.dart';
import '../../../widgets/common/custom_button.dart';
import '../../../widgets/common/loading_widget.dart';
import '../../../widgets/dialogs/booking_confirmation_dialog.dart';

/// تبويب حجز المواعيد
class BookAppointmentTab extends StatefulWidget {
  final String patientId;

  const BookAppointmentTab({super.key, required this.patientId});

  @override
  State<BookAppointmentTab> createState() => _BookAppointmentTabState();
}

class _BookAppointmentTabState extends State<BookAppointmentTab> {
  final AppointmentsService _appointmentsService = AppointmentsService();
  DateTime? _selectedDate;
  String? _selectedTimeSlot;
  bool _isLoading = false;
  bool _isLoadingTimeSlots = false;
  List<String> _availableTimeSlots = [];
  Map<String, Map<String, dynamic>> _timeSlotsWithDetails =
      {}; // time -> {timeSlotId, timeSlotModel}
  HolidayModel? _selectedDateHoliday;

  @override
  void initState() {
    super.initState();
    // البحث التلقائي عن تاريخ اليوم
    _selectedDate = DateTime.now();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAvailableTimeSlots(_selectedDate!);
    });
  }

  String _getArabicDayName(DateTime date) {
    const arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return arabicDays[date.weekday - 1];
  }

  Future<void> _selectDate() async {
    final now = DateTime.now();
    final firstDate = now;
    final lastDate = now.add(const Duration(days: 30));

    final date = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? now,
      firstDate: firstDate,
      lastDate: lastDate,
      locale: const Locale('ar'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: AppColors.primary,
              onPrimary: AppColors.white,
              surface: AppColors.white,
              onSurface: AppColors.textPrimary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (date != null) {
      setState(() {
        _selectedDate = date;
        _selectedTimeSlot = null;
      });
      await _loadAvailableTimeSlots(date);
    }
  }

  Future<void> _loadAvailableTimeSlots(DateTime date) async {
    print(
      '🔄 [BookAppointmentTab] Loading time slots for date: ${date.toIso8601String().split('T')[0]}',
    );
    setState(() {
      _isLoadingTimeSlots = true;
    });

    try {
      // Check if date is holiday
      final holiday = await _appointmentsService.checkHoliday(date);
      print(
        '🏖️ [BookAppointmentTab] Holiday check result: ${holiday?.occasionName ?? 'No holiday'}',
      );

      if (holiday != null) {
        setState(() {
          _selectedDateHoliday = holiday;
          _availableTimeSlots = [];
          _timeSlotsWithDetails = {};
          _isLoadingTimeSlots = false;
        });
        return;
      }

      // Get available time slots with their details
      final timeSlotsWithDetails = await _appointmentsService
          .getAvailableTimeSlotsWithDetails(date);
      print(
        '⏰ [BookAppointmentTab] Loaded ${timeSlotsWithDetails.length} time slots',
      );

      if (mounted) {
        setState(() {
          _selectedDateHoliday = null;
          _timeSlotsWithDetails = timeSlotsWithDetails;
          _availableTimeSlots = timeSlotsWithDetails.keys.toList();
          _isLoadingTimeSlots = false;
        });
      }
    } catch (e) {
      print('❌ [BookAppointmentTab] Error loading time slots: $e');
      if (mounted) {
        setState(() {
          _isLoadingTimeSlots = false;
        });
      }
      if (mounted) {
        Helpers.showErrorSnackBar(context, 'خطأ في تحميل الأوقات المتاحة: $e');
      }
    }
  }

  Future<void> _showBookingConfirmation() async {
    if (_selectedDate == null || _selectedTimeSlot == null) {
      Helpers.showErrorSnackBar(context, 'يرجى اختيار التاريخ والوقت');
      return;
    }

    // Get appointment details
    final timeSlotDetails = _timeSlotsWithDetails[_selectedTimeSlot];
    final timeSlotModel = timeSlotDetails?['timeSlotModel'] as TimeSlotModel?;
    final duration = timeSlotModel?.formattedDuration ?? 'غير محددة';

    // Show confirmation dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => BookingConfirmationDialog(
            appointmentDate:
                '${_getArabicDayName(_selectedDate!)} - ${_selectedDate!.year}-${_selectedDate!.month.toString().padLeft(2, '0')}-${_selectedDate!.day.toString().padLeft(2, '0')}',
            appointmentTime: _selectedTimeSlot!,
            duration: duration,
            onConfirm: () {
              Navigator.of(context).pop();
              _bookAppointment();
            },
            onCancel: () {
              Navigator.of(context).pop();
            },
          ),
    );
  }

  Future<void> _bookAppointment() async {
    print(
      '📅 [BookAppointmentTab] Booking appointment for ${widget.patientId}',
    );
    print(
      '📅 [BookAppointmentTab] Date: ${_selectedDate!.toIso8601String().split('T')[0]}',
    );
    print('📅 [BookAppointmentTab] Time: $_selectedTimeSlot');

    setState(() {
      _isLoading = true;
    });

    try {
      // Get the actual time slot ID for the selected time
      final timeSlotDetails = _timeSlotsWithDetails[_selectedTimeSlot];
      final timeSlotId = timeSlotDetails?['timeSlotId'];

      if (timeSlotId == null) {
        throw Exception('لم يتم العثور على معرف الوقت المحدد');
      }

      print('📅 [BookAppointmentTab] Time slot ID: $timeSlotId');

      await _appointmentsService.bookAppointment(
        patientId: widget.patientId,
        appointmentDate: _selectedDate!,
        timeSlotId: timeSlotId,
      );

      print('✅ [BookAppointmentTab] Appointment booked successfully');

      if (mounted) {
        Helpers.showSuccessSnackBar(context, AppStrings.bookingConfirmed);

        // إعادة تعيين النموذج وتحميل تاريخ اليوم مرة أخرى
        setState(() {
          _selectedDate = DateTime.now();
          _selectedTimeSlot = null;
          _availableTimeSlots = [];
          _timeSlotsWithDetails = {};
          _selectedDateHoliday = null;
        });
        // تحميل الأوقات المتاحة لتاريخ اليوم
        _loadAvailableTimeSlots(_selectedDate!);
      }
    } catch (e) {
      print('❌ [BookAppointmentTab] Error booking appointment: $e');
      if (mounted) {
        Helpers.showErrorSnackBar(context, 'فشل في حجز الموعد: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة اختيار التاريخ
          _buildDateSelectionCard(),

          SizedBox(height: 20.h),

          // بطاقة اختيار الوقت
          if (_selectedDate != null) _buildTimeSelectionCard(),

          SizedBox(height: 30.h),

          // زر الحجز
          if (_selectedDate != null && _selectedTimeSlot != null)
            _buildBookButton(),
        ],
      ),
    );
  }

  Widget _buildDateSelectionCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppStrings.selectDate,
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 16.h),
            InkWell(
              onTap: _selectDate,
              child: Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  border: Border.all(color: AppColors.border),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      color: AppColors.primary,
                      size: 20.sp,
                    ),
                    SizedBox(width: 12.w),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (_selectedDate != null) ...[
                          Text(
                            _getArabicDayName(_selectedDate!),
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                              color: AppColors.primary,
                            ),
                          ),
                          SizedBox(height: 2.h),
                          Text(
                            '${_selectedDate!.year}-${_selectedDate!.month.toString().padLeft(2, '0')}-${_selectedDate!.day.toString().padLeft(2, '0')}',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.textSecondary,
                            ),
                          ),
                        ] else ...[
                          Text(
                            'اختر التاريخ',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: AppColors.textLight,
                            ),
                          ),
                        ],
                      ],
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_drop_down,
                      color: AppColors.textSecondary,
                      size: 24.sp,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeSelectionCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'الأوقات المتاحة',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.textPrimary,
              ),
            ),
            SizedBox(height: 16.h),
            // Show holiday message if date is holiday
            if (_selectedDateHoliday != null) ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppColors.warning.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: AppColors.warning),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.event_busy,
                      color: AppColors.warning,
                      size: 32.sp,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'إجازة - ${_selectedDateHoliday!.occasionName}',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: AppColors.warning,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    if (_selectedDateHoliday!.notes != null) ...[
                      SizedBox(height: 4.h),
                      Text(
                        _selectedDateHoliday!.notes!,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textSecondary,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ]
            // Show loading indicator
            else if (_isLoadingTimeSlots) ...[
              Center(
                child: SizedBox(
                  width: 24.w,
                  height: 24.h,
                  child: const CircularProgressIndicator(strokeWidth: 2),
                ),
              ),
            ]
            // Show available time slots
            else if (_availableTimeSlots.isNotEmpty) ...[
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _availableTimeSlots.length,
                itemBuilder: (context, index) {
                  final timeSlot = _availableTimeSlots[index];
                  final isSelected = _selectedTimeSlot == timeSlot;

                  // Parse the time slot to extract start and end times
                  final parts = timeSlot.split(' - ');
                  final startTime = parts[0];
                  final endTime = parts[1];

                  // Get duration from database
                  final timeSlotDetails = _timeSlotsWithDetails[timeSlot];
                  final timeSlotModel =
                      timeSlotDetails?['timeSlotModel'] as TimeSlotModel?;
                  final duration =
                      timeSlotModel?.formattedDuration ??
                      'مدة الجلسة: غير محددة';

                  return Container(
                    margin: EdgeInsets.only(bottom: 12.h),
                    child: InkWell(
                      onTap: () {
                        setState(() {
                          _selectedTimeSlot = timeSlot;
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? AppColors.primary.withValues(alpha: 0.1)
                                  : AppColors.white,
                          border: Border.all(
                            color:
                                isSelected
                                    ? AppColors.primary
                                    : AppColors.border,
                            width: isSelected ? 2 : 1,
                          ),
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Row(
                          children: [
                            // Time icon
                            Container(
                              padding: EdgeInsets.all(8.w),
                              decoration: BoxDecoration(
                                color:
                                    isSelected
                                        ? AppColors.primary
                                        : AppColors.primary.withValues(
                                          alpha: 0.1,
                                        ),
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Icon(
                                Icons.access_time,
                                color:
                                    isSelected
                                        ? AppColors.white
                                        : AppColors.primary,
                                size: 20.sp,
                              ),
                            ),
                            SizedBox(width: 16.w),
                            // Time details
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Start time
                                  Text(
                                    'من: $startTime',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color:
                                          isSelected
                                              ? AppColors.primary
                                              : AppColors.textPrimary,
                                    ),
                                  ),
                                  SizedBox(height: 4.h),
                                  // End time
                                  Text(
                                    'إلى: $endTime',
                                    style: TextStyle(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w600,
                                      color:
                                          isSelected
                                              ? AppColors.primary
                                              : AppColors.textPrimary,
                                    ),
                                  ),
                                  SizedBox(height: 6.h),
                                  // Duration
                                  Text(
                                    duration,
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            // Selection indicator
                            if (isSelected)
                              Icon(
                                Icons.check_circle,
                                color: AppColors.primary,
                                size: 24.sp,
                              ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ]
            // Show no slots available message
            else ...[
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: AppColors.textSecondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.schedule,
                      color: AppColors.textSecondary,
                      size: 32.sp,
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'لا توجد أوقات متاحة في هذا التاريخ',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBookButton() {
    return SizedBox(
      width: double.infinity,
      child:
          _isLoading
              ? const LoadingWidget()
              : CustomButton(
                text: AppStrings.bookAppointment,
                onPressed: _showBookingConfirmation,
                icon: Icons.check,
              ),
    );
  }
}
