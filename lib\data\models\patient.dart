import 'package:equatable/equatable.dart';

/// نموذج بيانات المريض
class Patient extends Equatable {
  final String id;
  final String name;
  final String? email;
  final String? phone;
  final String? gender;
  final DateTime? birthDate;
  final double? weight;
  final double? height;
  final int? age;
  final bool isPremium;
  final String? authId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Patient({
    required this.id,
    required this.name,
    this.email,
    this.phone,
    this.gender,
    this.birthDate,
    this.weight,
    this.height,
    this.age,
    this.isPremium = false,
    this.authId,
    this.createdAt,
    this.updatedAt,
  });

  /// إنشاء Patient من JSON
  factory Patient.fromJson(Map<String, dynamic> json) {
    return Patient(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      email: json['email']?.toString(),
      phone: json['phone']?.toString(),
      gender: json['gender']?.toString(),
      birthDate: json['birth_date'] != null 
          ? DateTime.tryParse(json['birth_date'].toString())
          : null,
      weight: json['weight'] != null 
          ? double.tryParse(json['weight'].toString())
          : null,
      height: json['height'] != null 
          ? double.tryParse(json['height'].toString())
          : null,
      age: json['age'] != null 
          ? int.tryParse(json['age'].toString())
          : null,
      isPremium: json['is_premium'] == true,
      authId: json['auth_id']?.toString(),
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at'].toString())
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at'].toString())
          : null,
    );
  }

  /// تحويل Patient إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'gender': gender,
      'birth_date': birthDate?.toIso8601String(),
      'weight': weight,
      'height': height,
      'age': age,
      'is_premium': isPremium,
      'auth_id': authId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من Patient
  Patient copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? gender,
    DateTime? birthDate,
    double? weight,
    double? height,
    int? age,
    bool? isPremium,
    String? authId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Patient(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      weight: weight ?? this.weight,
      height: height ?? this.height,
      age: age ?? this.age,
      isPremium: isPremium ?? this.isPremium,
      authId: authId ?? this.authId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// حساب مؤشر كتلة الجسم (BMI)
  double? get bmi {
    if (weight == null || height == null || height == 0) return null;
    final heightInMeters = height! / 100;
    return weight! / (heightInMeters * heightInMeters);
  }

  /// تصنيف مؤشر كتلة الجسم
  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return 'غير محدد';
    
    if (bmiValue < 18.5) return 'نقص في الوزن';
    if (bmiValue < 25) return 'وزن طبيعي';
    if (bmiValue < 30) return 'زيادة في الوزن';
    return 'سمنة';
  }

  /// حساب العمر من تاريخ الميلاد
  int? get calculatedAge {
    if (birthDate == null) return age;
    final now = DateTime.now();
    int calculatedAge = now.year - birthDate!.year;
    if (now.month < birthDate!.month || 
        (now.month == birthDate!.month && now.day < birthDate!.day)) {
      calculatedAge--;
    }
    return calculatedAge;
  }

  /// الحصول على الاسم المختصر (الحرف الأول من كل كلمة)
  String get initials {
    final words = name.trim().split(' ');
    if (words.isEmpty) return '';
    if (words.length == 1) return words[0].substring(0, 1).toUpperCase();
    return '${words[0].substring(0, 1)}${words[1].substring(0, 1)}'.toUpperCase();
  }

  /// التحقق من اكتمال البيانات الأساسية
  bool get hasCompleteBasicInfo {
    return name.isNotEmpty && 
           email != null && 
           email!.isNotEmpty &&
           phone != null && 
           phone!.isNotEmpty;
  }

  /// التحقق من اكتمال البيانات الصحية
  bool get hasCompleteHealthInfo {
    return weight != null && 
           height != null && 
           (age != null || birthDate != null) &&
           gender != null;
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        gender,
        birthDate,
        weight,
        height,
        age,
        isPremium,
        authId,
        createdAt,
        updatedAt,
      ];

  @override
  String toString() {
    return 'Patient(id: $id, name: $name, email: $email, isPremium: $isPremium)';
  }
}
