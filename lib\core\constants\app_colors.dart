import 'package:flutter/material.dart';

/// ألوان التطبيق الأساسية
class AppColors {
  AppColors._();

  // الألوان الأساسية
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color primary = Color(0xFF04938c); // اللون الأساسي الجديد
  static const Color secondary = Color(0xFF0c3c4c); // اللون الثانوي الجديد

  // تدرجات الألوان الأساسية
  static const Color primaryLight = Color(0xFF26a69a);
  static const Color primaryDark = Color(0xFF00695c);
  static const Color secondaryLight = Color(0xFF37474f);
  static const Color secondaryDark = Color(0xFF263238);

  // ألوان النصوص
  static const Color textPrimary = Color(0xFF1A1A1A);
  static const Color textSecondary = Color(0xFF666666);
  static const Color textLight = Color(0xFF999999);
  static const Color textWhite = Color(0xFFFFFFFF);
  static const Color textDark = Color(0xFF1A1A1A); // إضافة textDark المفقود

  // ألوان الخلفيات
  static const Color background = Color(0xFFFAFAFA);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color surfaceVariant = Color(0xFFF5F5F5);

  // ألوان الحالات
  static const Color success = Color(0xFF4CAF50);
  static const Color error = Color(0xFFE53E3E);
  static const Color warning = Color(0xFFFF9800);
  static const Color info = Color(0xFF2196F3);

  // ألوان الحدود والفواصل
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFEEEEEE);

  // ألوان الظلال
  static const Color shadow = Color(0x1A000000);
  static const Color shadowLight = Color(0x0D000000);

  // ألوان خاصة بالتطبيق
  static const Color cardBackground = Color(0xFFFFFFFF);
  static const Color inputBackground = Color(0xFFF8F9FA);
  static const Color buttonDisabled = Color(0xFFE0E0E0);
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);

  // تدرجات للرسوم البيانية
  static const List<Color> chartColors = [
    primary,
    secondary,
    Color(0xFF66BB6A),
    Color(0xFF42A5F5),
    Color(0xFFEF5350),
    Color(0xFFFF7043),
    Color(0xFFAB47BC),
    Color(0xFF26A69A),
  ];

  // ألوان حالة المواعيد
  static const Color appointmentAvailable = Color(0xFF4CAF50);
  static const Color appointmentBooked = Color(0xFFFF9800);
  static const Color appointmentCompleted = Color(0xFF2196F3);
  static const Color appointmentCancelled = Color(0xFFE53E3E);

  // ألوان المستخدمين
  static const Color premiumUser = Color(0xFFFFD700);
  static const Color regularUser = Color(0xFF9E9E9E);

  // ألوان إضافية
  static const Color overlay = Color(0x80000000);
  static const Color transparent = Colors.transparent;
}
