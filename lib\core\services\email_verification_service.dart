import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'shared_preferences_service.dart';

/// خدمة التحقق من البريد الإلكتروني
class EmailVerificationService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static final SharedPreferencesService _prefs = SharedPreferencesService();

  /// إنشاء حساب جديد مباشرة بدون تأكيد البريد الإلكتروني
  static Future<EmailVerificationResult> createAccountDirectly({
    required String email,
    required String password,
    required String fullName,
    required String phone,
    required String birthDate,
    required String gender,
    required String weight,
    required String height,
  }) async {
    try {
      AppLogger.info(
        '📝 Creating account directly (no email verification)',
        category: LogCategory.auth,
        data: {'email': email, 'fullName': fullName},
      );

      // التحقق من صحة البريد الإلكتروني
      if (!_isValidEmail(email)) {
        return EmailVerificationResult.error('البريد الإلكتروني غير صحيح');
      }

      // التحقق من قوة كلمة المرور
      final passwordValidation = _validatePassword(password);
      if (!passwordValidation.isValid) {
        return EmailVerificationResult.error(passwordValidation.message);
      }

      // إنشاء الحساب مباشرة بدون تأكيد البريد الإلكتروني
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName, 'display_name': fullName},
      );

      if (response.user != null) {
        // إنشاء سجل المريض مباشرة
        await _createPatientRecordDirectly(
          response.user!,
          fullName,
          phone,
          birthDate,
          gender,
          weight,
          height,
        );

        AppLogger.info(
          '✅ Account created successfully without email verification',
          category: LogCategory.auth,
          data: {'email': email, 'userId': response.user!.id},
        );

        return EmailVerificationResult.success(
          'تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول',
          response.user!.id,
        );
      } else {
        return EmailVerificationResult.error('فشل في إنشاء الحساب');
      }
    } on AuthException catch (e) {
      AppLogger.error(
        '❌ Auth error sending verification code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email, 'code': e.statusCode},
      );

      return EmailVerificationResult.error(_getAuthErrorMessage(e));
    } catch (e) {
      AppLogger.error(
        '❌ Unexpected error sending verification code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return EmailVerificationResult.error('حدث خطأ غير متوقع. حاول مرة أخرى.');
    }
  }

  /// إرسال كود التحقق عبر البريد الإلكتروني (النظام القديم)
  static Future<EmailVerificationResult> sendVerificationCode({
    required String email,
    required String password,
    required String fullName,
    required String phone,
    required String birthDate,
    required String gender,
    required String weight,
    required String height,
  }) async {
    try {
      AppLogger.info(
        '📧 Sending verification code',
        category: LogCategory.auth,
        data: {'email': email, 'fullName': fullName},
      );

      // التحقق من صحة البريد الإلكتروني
      if (!_isValidEmail(email)) {
        return EmailVerificationResult.error('البريد الإلكتروني غير صحيح');
      }

      // التحقق من قوة كلمة المرور
      final passwordValidation = _validatePassword(password);
      if (!passwordValidation.isValid) {
        return EmailVerificationResult.error(passwordValidation.message);
      }

      // حفظ بيانات التسجيل مؤقتاً
      await _saveTemporaryRegistrationData(
        email,
        password,
        fullName,
        phone: phone,
        birthDate: birthDate,
        gender: gender,
        weight: weight,
        height: height,
      );

      // إنشاء الحساب مع تأكيد البريد الإلكتروني
      final response = await _supabase.auth.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName, 'display_name': fullName},
        emailRedirectTo: null, // لا نريد redirect
      );

      if (response.user != null) {
        // حفظ وقت إرسال الكود
        await _prefs.setString(
          'verification_sent_time',
          DateTime.now().toIso8601String(),
        );
        await _prefs.setString('verification_email', email);

        AppLogger.info(
          '✅ Verification code sent successfully',
          category: LogCategory.auth,
          data: {'email': email, 'userId': response.user!.id},
        );

        return EmailVerificationResult.success(
          'تم إرسال كود التحقق إلى بريدك الإلكتروني',
          response.user!.id,
        );
      } else {
        return EmailVerificationResult.error('فشل في إرسال كود التحقق');
      }
    } on AuthException catch (e) {
      AppLogger.error(
        '❌ Auth error sending verification code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email, 'code': e.statusCode},
      );

      return EmailVerificationResult.error(_getAuthErrorMessage(e));
    } catch (e) {
      AppLogger.error(
        '❌ Unexpected error sending verification code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return EmailVerificationResult.error('حدث خطأ غير متوقع. حاول مرة أخرى.');
    }
  }

  /// التحقق من كود التأكيد
  static Future<EmailVerificationResult> verifyCode({
    required String email,
    required String token,
  }) async {
    try {
      AppLogger.info(
        '🔍 Verifying email code',
        category: LogCategory.auth,
        data: {'email': email},
      );

      // التحقق من الكود
      final response = await _supabase.auth.verifyOTP(
        email: email,
        token: token,
        type: OtpType.signup,
      );

      if (response.user != null) {
        // إضافة المستخدم لجدول patients
        await _createPatientRecord(response.user!);

        // مسح البيانات المؤقتة
        await _clearTemporaryData();

        AppLogger.info(
          '✅ Email verified and patient created successfully',
          category: LogCategory.auth,
          data: {'email': email, 'userId': response.user!.id},
        );

        return EmailVerificationResult.success(
          'تم التحقق من البريد الإلكتروني بنجاح',
          response.user!.id,
        );
      } else {
        return EmailVerificationResult.error('فشل في التحقق من الكود');
      }
    } on AuthException catch (e) {
      AppLogger.error(
        '❌ Auth error verifying code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email, 'code': e.statusCode},
      );

      return EmailVerificationResult.error(_getAuthErrorMessage(e));
    } catch (e) {
      AppLogger.error(
        '❌ Unexpected error verifying code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return EmailVerificationResult.error('حدث خطأ غير متوقع. حاول مرة أخرى.');
    }
  }

  /// إعادة إرسال كود التحقق
  static Future<EmailVerificationResult> resendVerificationCode(
    String email,
  ) async {
    try {
      // التحقق من الوقت المسموح لإعادة الإرسال
      final canResend = await _canResendCode();
      if (!canResend) {
        final remainingTime = await _getRemainingTime();
        return EmailVerificationResult.error(
          'يمكنك إعادة إرسال الكود بعد $remainingTime ثانية',
        );
      }

      AppLogger.info(
        '🔄 Resending verification code',
        category: LogCategory.auth,
        data: {'email': email},
      );

      // إعادة إرسال الكود
      await _supabase.auth.resend(type: OtpType.signup, email: email);

      // تحديث وقت الإرسال
      await _prefs.setString(
        'verification_sent_time',
        DateTime.now().toIso8601String(),
      );

      AppLogger.info(
        '✅ Verification code resent successfully',
        category: LogCategory.auth,
        data: {'email': email},
      );

      return EmailVerificationResult.success('تم إعادة إرسال كود التحقق', null);
    } on AuthException catch (e) {
      AppLogger.error(
        '❌ Auth error resending code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return EmailVerificationResult.error(_getAuthErrorMessage(e));
    } catch (e) {
      AppLogger.error(
        '❌ Unexpected error resending code',
        category: LogCategory.auth,
        error: e,
        data: {'email': email},
      );

      return EmailVerificationResult.error('حدث خطأ غير متوقع. حاول مرة أخرى.');
    }
  }

  /// التحقق من إمكانية إعادة الإرسال
  static Future<bool> _canResendCode() async {
    final sentTimeStr = _prefs.getString('verification_sent_time');
    if (sentTimeStr == null) return true;

    final sentTime = DateTime.parse(sentTimeStr);
    final now = DateTime.now();
    final difference = now.difference(sentTime).inSeconds;

    return difference >= 60; // دقيقة واحدة
  }

  /// الحصول على الوقت المتبقي لإعادة الإرسال
  static Future<int> _getRemainingTime() async {
    final sentTimeStr = _prefs.getString('verification_sent_time');
    if (sentTimeStr == null) return 0;

    final sentTime = DateTime.parse(sentTimeStr);
    final now = DateTime.now();
    final difference = now.difference(sentTime).inSeconds;

    return 60 - difference;
  }

  /// إنشاء سجل المريض في قاعدة البيانات
  static Future<void> _createPatientRecord(User user) async {
    try {
      // الحصول على البيانات المؤقتة
      final fullName =
          _prefs.getString('temp_full_name') ??
          user.userMetadata?['full_name'] ??
          '';
      final birthDate = _prefs.getString('temp_birth_date');
      final gender = _prefs.getString('temp_gender');
      final weight = _prefs.getString('temp_weight');
      final height = _prefs.getString('temp_height');

      // حساب العمر من تاريخ الميلاد
      int? age;
      if (birthDate != null) {
        final birth = DateTime.parse(birthDate);
        age = DateTime.now().difference(birth).inDays ~/ 365;
      }

      // إنشاء سجل المريض
      final patientData = {
        'auth_id': user.id,
        'name': fullName,
        'email': user.email,
        'phone': _prefs.getString('temp_phone'),
        'gender': gender,
        'birth_date': birthDate,
        'age': age,
        'weight': weight != null ? double.tryParse(weight) : null,
        'height': height != null ? double.tryParse(height) : null,
        'is_premium': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response =
          await _supabase
              .from('patients')
              .insert(patientData)
              .select()
              .single();

      AppLogger.info(
        '✅ Patient record created successfully',
        category: LogCategory.auth,
        data: {
          'userId': user.id,
          'patientId': response['id'].toString(),
          'name': fullName,
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to create patient record',
        category: LogCategory.auth,
        error: e,
        data: {'userId': user.id},
      );
      // لا نرمي الخطأ هنا لأن المستخدم تم إنشاؤه بالفعل
    }
  }

  /// إنشاء سجل المريض مباشرة بدون حفظ مؤقت
  static Future<void> _createPatientRecordDirectly(
    User user,
    String fullName,
    String phone,
    String birthDate,
    String gender,
    String weight,
    String height,
  ) async {
    try {
      AppLogger.info(
        '👤 Creating patient record directly',
        category: LogCategory.auth,
        data: {'userId': user.id, 'name': fullName},
      );

      // حساب العمر من تاريخ الميلاد
      int? age;
      if (birthDate.isNotEmpty) {
        final birth = DateTime.parse(birthDate);
        age = DateTime.now().difference(birth).inDays ~/ 365;
      }

      // إنشاء سجل المريض
      final patientData = {
        'auth_id': user.id,
        'name': fullName,
        'email': user.email,
        'phone': phone.isNotEmpty ? phone : null,
        'gender': gender.isNotEmpty ? gender : null,
        'birth_date': birthDate.isNotEmpty ? birthDate : null,
        'age': age,
        'weight': weight.isNotEmpty ? double.tryParse(weight) : null,
        'height': height.isNotEmpty ? double.tryParse(height) : null,
        'is_premium': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response =
          await _supabase
              .from('patients')
              .insert(patientData)
              .select()
              .single();

      AppLogger.info(
        '✅ Patient record created successfully (direct)',
        category: LogCategory.auth,
        data: {
          'userId': user.id,
          'patientId': response['id'].toString(),
          'name': fullName,
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to create patient record (direct)',
        category: LogCategory.auth,
        error: e,
        data: {'userId': user.id},
      );
      // لا نرمي الخطأ هنا لأن المستخدم تم إنشاؤه بالفعل
    }
  }

  /// حفظ بيانات التسجيل مؤقتاً
  static Future<void> _saveTemporaryRegistrationData(
    String email,
    String password,
    String fullName, {
    required String phone,
    required String birthDate,
    required String gender,
    required String weight,
    required String height,
  }) async {
    await _prefs.setString('temp_email', email);
    await _prefs.setString('temp_password', password);
    await _prefs.setString('temp_full_name', fullName);
    await _prefs.setString('temp_phone', phone);
    await _prefs.setString('temp_birth_date', birthDate);
    await _prefs.setString('temp_gender', gender);
    await _prefs.setString('temp_weight', weight);
    await _prefs.setString('temp_height', height);
  }

  /// مسح البيانات المؤقتة
  static Future<void> _clearTemporaryData() async {
    await _prefs.remove('temp_email');
    await _prefs.remove('temp_password');
    await _prefs.remove('temp_full_name');
    await _prefs.remove('temp_phone');
    await _prefs.remove('temp_birth_date');
    await _prefs.remove('temp_gender');
    await _prefs.remove('temp_weight');
    await _prefs.remove('temp_height');
    await _prefs.remove('verification_sent_time');
    await _prefs.remove('verification_email');
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من قوة كلمة المرور
  static PasswordValidation _validatePassword(String password) {
    if (password.length < 8) {
      return PasswordValidation(
        false,
        'كلمة المرور يجب أن تكون 8 خانات على الأقل',
      );
    }
    return PasswordValidation(true, '');
  }

  /// الحصول على رسالة خطأ مناسبة
  static String _getAuthErrorMessage(AuthException e) {
    switch (e.message.toLowerCase()) {
      case 'user already registered':
      case 'email already registered':
        return 'هذا البريد الإلكتروني مسجل بالفعل';
      case 'invalid email':
        return 'البريد الإلكتروني غير صحيح';
      case 'weak password':
        return 'كلمة المرور ضعيفة';
      case 'email not confirmed':
        return 'لم يتم تأكيد البريد الإلكتروني';
      case 'invalid verification code':
      case 'token has expired':
        return 'كود التحقق غير صحيح أو منتهي الصلاحية';
      case 'too many requests':
        return 'تم إرسال طلبات كثيرة. حاول بعد قليل';
      default:
        return e.message;
    }
  }
}

/// نتيجة عملية التحقق من البريد الإلكتروني
class EmailVerificationResult {
  final bool isSuccess;
  final String message;
  final String? userId;

  EmailVerificationResult._(this.isSuccess, this.message, this.userId);

  factory EmailVerificationResult.success(String message, String? userId) {
    return EmailVerificationResult._(true, message, userId);
  }

  factory EmailVerificationResult.error(String message) {
    return EmailVerificationResult._(false, message, null);
  }
}

/// نتيجة التحقق من كلمة المرور
class PasswordValidation {
  final bool isValid;
  final String message;

  PasswordValidation(this.isValid, this.message);
}
