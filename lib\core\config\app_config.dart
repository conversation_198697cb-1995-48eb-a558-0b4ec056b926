import 'dart:developer' as developer;
import '../constants/app_constants.dart';

/// إعدادات التطبيق
class AppConfig {
  AppConfig._();

  /// البيئة الحالية
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  /// هل التطبيق في بيئة التطوير
  static bool get isDevelopment => environment == 'development';

  /// هل التطبيق في بيئة الإنتاج
  static bool get isProduction => environment == 'production';

  /// هل التطبيق في بيئة الاختبار
  static bool get isTesting => environment == 'testing';

  /// إعدادات Supabase
  static const String supabaseUrl = AppConstants.supabaseUrl;
  static const String supabaseAnonKey = AppConstants.supabaseAnonKey;

  /// إعدادات OpenAI
  static const String openAiApiKey = AppConstants.openAiApiKey;
  static const String openAiModel = AppConstants.openAiModel;
  static const String openAiBaseUrl = AppConstants.openAiBaseUrl;

  /// إعدادات التطبيق
  static const String appName = 'عيادة التغذية';
  static const String appVersion = AppConstants.appVersion;
  static const String appBuildNumber = AppConstants.appBuildNumber;

  /// إعدادات الشبكة
  static const Duration connectTimeout = AppConstants.connectTimeout;
  static const Duration receiveTimeout = AppConstants.receiveTimeout;
  static const Duration sendTimeout = AppConstants.sendTimeout;
  static const int maxRetryAttempts = AppConstants.maxRetryAttempts;
  static const Duration retryDelay = AppConstants.retryDelay;

  /// إعدادات التخزين المؤقت
  static const Duration cacheExpiration = AppConstants.cacheExpiration;
  static const Duration longCacheExpiration = AppConstants.longCacheExpiration;

  /// إعدادات الصفحات
  static const int defaultPageSize = AppConstants.defaultPageSize;
  static const int maxPageSize = AppConstants.maxPageSize;

  /// إعدادات الملفات
  static const int maxFileSize = AppConstants.maxFileSize;
  static const List<String> allowedImageTypes = AppConstants.allowedImageTypes;
  static const List<String> allowedDocumentTypes = AppConstants.allowedDocumentTypes;

  /// إعدادات التحقق
  static const int minPasswordLength = AppConstants.minPasswordLength;
  static const int maxPasswordLength = AppConstants.maxPasswordLength;
  static const int minNameLength = AppConstants.minNameLength;
  static const int maxNameLength = AppConstants.maxNameLength;
  static const int maxNotesLength = AppConstants.maxNotesLength;

  /// إعدادات الميزات
  static const bool enableAnalytics = AppConstants.enableAnalytics;
  static const bool enableCrashReporting = AppConstants.enableCrashReporting;
  static const bool enablePushNotifications = AppConstants.enablePushNotifications;
  static const bool enableBiometricAuth = AppConstants.enableBiometricAuth;
  static const bool enableDarkMode = AppConstants.enableDarkMode;

  /// إعدادات التصميم
  static const double mobileBreakpoint = AppConstants.mobileBreakpoint;
  static const double tabletBreakpoint = AppConstants.tabletBreakpoint;
  static const double desktopBreakpoint = AppConstants.desktopBreakpoint;

  /// إعدادات الرسوم المتحركة
  static const Duration shortAnimationDuration = AppConstants.shortAnimationDuration;
  static const Duration mediumAnimationDuration = AppConstants.mediumAnimationDuration;
  static const Duration longAnimationDuration = AppConstants.longAnimationDuration;

  /// إعدادات الإشعارات
  static const int appointmentReminderNotificationId = AppConstants.appointmentReminderNotificationId;
  static const int mealReminderNotificationId = AppConstants.mealReminderNotificationId;
  static const int waterReminderNotificationId = AppConstants.waterReminderNotificationId;
  static const int exerciseReminderNotificationId = AppConstants.exerciseReminderNotificationId;
  static const int medicationReminderNotificationId = AppConstants.medicationReminderNotificationId;

  /// إعدادات التواريخ
  static const String dateFormat = AppConstants.dateFormat;
  static const String timeFormat = AppConstants.timeFormat;
  static const String dateTimeFormat = AppConstants.dateTimeFormat;
  static const String displayDateFormat = AppConstants.displayDateFormat;
  static const String displayTimeFormat = AppConstants.displayTimeFormat;
  static const String displayDateTimeFormat = AppConstants.displayDateTimeFormat;

  /// التعبيرات النمطية
  static const String emailRegex = AppConstants.emailRegex;
  static const String phoneRegex = AppConstants.phoneRegex;
  static const String nameRegex = AppConstants.nameRegex;

  /// رسائل الخطأ الافتراضية
  static const String genericErrorMessage = AppConstants.genericErrorMessage;
  static const String networkErrorMessage = AppConstants.networkErrorMessage;
  static const String serverErrorMessage = AppConstants.serverErrorMessage;
  static const String timeoutErrorMessage = AppConstants.timeoutErrorMessage;

  /// رسائل النجاح الافتراضية
  static const String genericSuccessMessage = AppConstants.genericSuccessMessage;

  /// رسائل التحميل الافتراضية
  static const String loadingMessage = AppConstants.loadingMessage;
  static const String savingMessage = AppConstants.savingMessage;
  static const String deletingMessage = AppConstants.deletingMessage;
  static const String updatingMessage = AppConstants.updatingMessage;

  /// رسائل الحالة الفارغة الافتراضية
  static const String noDataMessage = AppConstants.noDataMessage;
  static const String noResultsMessage = AppConstants.noResultsMessage;
  static const String noAppointmentsMessage = AppConstants.noAppointmentsMessage;
  static const String noProductsMessage = AppConstants.noProductsMessage;
  static const String noArticlesMessage = 'لا توجد مقالات';

  /// الحصول على URL الأساسي حسب البيئة
  static String get baseUrl {
    switch (environment) {
      case 'production':
        return 'https://api.dietrx.com';
      case 'staging':
        return 'https://staging-api.dietrx.com';
      case 'development':
      default:
        return AppConstants.baseUrl;
    }
  }

  /// الحصول على مستوى السجلات حسب البيئة
  static String get logLevel {
    switch (environment) {
      case 'production':
        return 'ERROR';
      case 'staging':
        return 'WARNING';
      case 'development':
      default:
        return 'DEBUG';
    }
  }

  /// هل يجب عرض السجلات
  static bool get shouldShowLogs {
    return isDevelopment || isTesting;
  }

  /// هل يجب تفعيل التتبع
  static bool get shouldEnableTracking {
    return isProduction;
  }

  /// هل يجب تفعيل التحليلات
  static bool get shouldEnableAnalytics {
    return enableAnalytics && (isProduction || environment == 'staging');
  }

  /// هل يجب تفعيل تقارير الأخطاء
  static bool get shouldEnableCrashReporting {
    return enableCrashReporting && (isProduction || environment == 'staging');
  }

  /// طباعة إعدادات التطبيق
  static void printConfig() {
    if (shouldShowLogs) {
      // استخدام developer.log بدلاً من print
      developer.log(
        '=== App Configuration ===\n'
        'Environment: $environment\n'
        'App Name: $appName\n'
        'App Version: $appVersion\n'
        'Build Number: $appBuildNumber\n'
        'Base URL: $baseUrl\n'
        'Log Level: $logLevel\n'
        'Analytics Enabled: $shouldEnableAnalytics\n'
        'Crash Reporting Enabled: $shouldEnableCrashReporting\n'
        '========================',
        name: 'AppConfig',
      );
    }
  }
}
