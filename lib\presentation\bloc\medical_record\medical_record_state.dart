import 'package:equatable/equatable.dart';
import '../../../data/models/patient_model.dart';
import '../../../data/models/medical_info_model.dart';


abstract class MedicalRecordState extends Equatable {
  const MedicalRecordState();

  @override
  List<Object?> get props => [];
}

class MedicalRecordInitial extends MedicalRecordState {
  const MedicalRecordInitial();
}

class MedicalRecordLoading extends MedicalRecordState {
  const MedicalRecordLoading();
}

class MedicalRecordLoaded extends MedicalRecordState {
  final PatientModel? patient;
  final List<MedicalInfoModel> medicalInfo;

  final int currentTabIndex;
  final bool isRefreshing;

  const MedicalRecordLoaded({
    this.patient,
    this.medicalInfo = const [],
    this.currentTabIndex = 0,
    this.isRefreshing = false,
  });

  MedicalRecordLoaded copyWith({
    PatientModel? patient,
   
    List<MedicalInfoModel>? medicalInfo,
   
    int? currentTabIndex,
    bool? isRefreshing,
  }) {
    return MedicalRecordLoaded(
      patient: patient ?? this.patient,
      
      medicalInfo: medicalInfo ?? this.medicalInfo,
      
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  @override
  List<Object?> get props => [
        patient,
        medicalInfo,
        currentTabIndex,
        isRefreshing,
      ];
}

class PatientInfoLoaded extends MedicalRecordState {
  final PatientModel patient;

  const PatientInfoLoaded(this.patient);

  @override
  List<Object?> get props => [patient];
}



class MedicalInfoLoaded extends MedicalRecordState {
  final List<MedicalInfoModel> medicalInfo;

  const MedicalInfoLoaded(this.medicalInfo);

  @override
  List<Object?> get props => [medicalInfo];
}

class MedicalInfoByTypeLoaded extends MedicalRecordState {
  final List<MedicalInfoModel> medicalInfo;
  final String infoType;

  const MedicalInfoByTypeLoaded(this.medicalInfo, this.infoType);

  @override
  List<Object?> get props => [medicalInfo, infoType];
}








class MedicalSummaryLoaded extends MedicalRecordState {
  final Map<String, dynamic> summary;

  const MedicalSummaryLoaded(this.summary);

  @override
  List<Object?> get props => [summary];
}

class TabChanged extends MedicalRecordState {
  final int tabIndex;

  const TabChanged(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

class MedicalRecordError extends MedicalRecordState {
  final String message;
  final String? errorCode;

  const MedicalRecordError(this.message, {this.errorCode});

  @override
  List<Object?> get props => [message, errorCode];
}

class MedicalRecordEmpty extends MedicalRecordState {
  final String message;

  const MedicalRecordEmpty(this.message);

  @override
  List<Object?> get props => [message];
}
