import 'package:equatable/equatable.dart';
import '../../../data/models/patient_model.dart';
import '../../../data/models/weekly_result_model.dart';
import '../../../data/models/medical_info_model.dart';
import '../../../data/models/lab_test_model.dart';
import '../../../data/models/reminder_model.dart';

abstract class MedicalRecordState extends Equatable {
  const MedicalRecordState();

  @override
  List<Object?> get props => [];
}

class MedicalRecordInitial extends MedicalRecordState {
  const MedicalRecordInitial();
}

class MedicalRecordLoading extends MedicalRecordState {
  const MedicalRecordLoading();
}

class MedicalRecordLoaded extends MedicalRecordState {
  final PatientModel? patient;
  final List<WeeklyResultModel> weeklyResults;
  final List<MedicalInfoModel> medicalInfo;
  final List<LabTestModel> labTests;
  final List<ReminderModel> reminders;
  final int currentTabIndex;
  final bool isRefreshing;

  const MedicalRecordLoaded({
    this.patient,
    this.weeklyResults = const [],
    this.medicalInfo = const [],
    this.labTests = const [],
    this.reminders = const [],
    this.currentTabIndex = 0,
    this.isRefreshing = false,
  });

  MedicalRecordLoaded copyWith({
    PatientModel? patient,
    List<WeeklyResultModel>? weeklyResults,
    List<MedicalInfoModel>? medicalInfo,
    List<LabTestModel>? labTests,
    List<ReminderModel>? reminders,
    int? currentTabIndex,
    bool? isRefreshing,
  }) {
    return MedicalRecordLoaded(
      patient: patient ?? this.patient,
      weeklyResults: weeklyResults ?? this.weeklyResults,
      medicalInfo: medicalInfo ?? this.medicalInfo,
      labTests: labTests ?? this.labTests,
      reminders: reminders ?? this.reminders,
      currentTabIndex: currentTabIndex ?? this.currentTabIndex,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }

  @override
  List<Object?> get props => [
        patient,
        weeklyResults,
        medicalInfo,
        labTests,
        reminders,
        currentTabIndex,
        isRefreshing,
      ];
}

class PatientInfoLoaded extends MedicalRecordState {
  final PatientModel patient;

  const PatientInfoLoaded(this.patient);

  @override
  List<Object?> get props => [patient];
}

class WeeklyResultsLoaded extends MedicalRecordState {
  final List<WeeklyResultModel> weeklyResults;

  const WeeklyResultsLoaded(this.weeklyResults);

  @override
  List<Object?> get props => [weeklyResults];
}

class MedicalInfoLoaded extends MedicalRecordState {
  final List<MedicalInfoModel> medicalInfo;

  const MedicalInfoLoaded(this.medicalInfo);

  @override
  List<Object?> get props => [medicalInfo];
}

class MedicalInfoByTypeLoaded extends MedicalRecordState {
  final List<MedicalInfoModel> medicalInfo;
  final String infoType;

  const MedicalInfoByTypeLoaded(this.medicalInfo, this.infoType);

  @override
  List<Object?> get props => [medicalInfo, infoType];
}

class LabTestsLoaded extends MedicalRecordState {
  final List<LabTestModel> labTests;

  const LabTestsLoaded(this.labTests);

  @override
  List<Object?> get props => [labTests];
}

class RemindersLoaded extends MedicalRecordState {
  final List<ReminderModel> reminders;

  const RemindersLoaded(this.reminders);

  @override
  List<Object?> get props => [reminders];
}

class ActiveRemindersLoaded extends MedicalRecordState {
  final List<ReminderModel> activeReminders;

  const ActiveRemindersLoaded(this.activeReminders);

  @override
  List<Object?> get props => [activeReminders];
}

class RecentLabTestsLoaded extends MedicalRecordState {
  final List<LabTestModel> recentLabTests;

  const RecentLabTestsLoaded(this.recentLabTests);

  @override
  List<Object?> get props => [recentLabTests];
}

class MedicalSummaryLoaded extends MedicalRecordState {
  final Map<String, dynamic> summary;

  const MedicalSummaryLoaded(this.summary);

  @override
  List<Object?> get props => [summary];
}

class TabChanged extends MedicalRecordState {
  final int tabIndex;

  const TabChanged(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

class MedicalRecordError extends MedicalRecordState {
  final String message;
  final String? errorCode;

  const MedicalRecordError(this.message, {this.errorCode});

  @override
  List<Object?> get props => [message, errorCode];
}

class MedicalRecordEmpty extends MedicalRecordState {
  final String message;

  const MedicalRecordEmpty(this.message);

  @override
  List<Object?> get props => [message];
}
