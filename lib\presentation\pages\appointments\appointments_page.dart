import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/app_logger.dart';
import '../../widgets/common/custom_app_bar.dart';
import 'tabs/my_appointments_tab.dart';
import 'tabs/clinic_info_tab.dart';

/// صفحة المواعيد
class AppointmentsPage extends StatefulWidget {
  final String authId; // معرف المصادقة

  const AppointmentsPage({super.key, required this.authId});

  @override
  State<AppointmentsPage> createState() => _AppointmentsPageState();
}

class _AppointmentsPageState extends State<AppointmentsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String? _patientId; // معرف المريض الحقيقي

  @override
  void initState() {
    super.initState();
    AppLogger.info(
      'AppointmentsPage initializing with authId: ${widget.authId}',
      category: LogCategory.ui,
    );
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
    _loadPatientId(); // تحميل معرف المريض الحقيقي
  }

  /// تحميل معرف المريض الحقيقي من جدول patients باستخدام auth_id
  Future<void> _loadPatientId() async {
    try {
      final response =
          await Supabase.instance.client
              .from('patients')
              .select('id')
              .eq('auth_id', widget.authId)
              .single();

      if (mounted) {
        setState(() {
          _patientId = response['id'] as String;
        });
        AppLogger.info(
          'Patient ID loaded: $_patientId for auth_id: ${widget.authId}',
          category: LogCategory.ui,
        );
      }
    } catch (e) {
      AppLogger.error(
        'Failed to load patient ID for auth_id: ${widget.authId}',
        category: LogCategory.ui,
        error: e,
      );
    }
  }

  void _onTabChanged() {
    final tabNames = ['مواعيدي', 'معلومات العيادة'];
    AppLogger.info(
      'AppointmentsPage tab changed to: ${tabNames[_tabController.index]}',
      category: LogCategory.ui,
    );
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: CustomAppBar(
        title: AppStrings.appointments,
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textLight,
          indicatorColor: AppColors.primary,
          labelStyle: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
          unselectedLabelStyle: TextStyle(
            fontFamily: 'Cairo',
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.calendar_today), text: 'مواعيدي'),
            Tab(icon: Icon(Icons.info_outline), text: 'معلومات العيادة'),
          ],
        ),
      ),
      body:
          _patientId != null
              ? TabBarView(
                controller: _tabController,
                children: [
                  MyAppointmentsTab(patientId: _patientId!),
                  const ClinicInfoTab(),
                ],
              )
              : const Center(child: CircularProgressIndicator()),
    );
  }
}
