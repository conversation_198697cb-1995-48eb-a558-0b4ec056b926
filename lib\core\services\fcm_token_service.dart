import 'dart:io';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

import '../../data/models/fcm_token_model.dart';
import '../utils/app_logger.dart';
import 'shared_preferences_service.dart';

/// خدمة إدارة FCM Tokens
class FCMTokenService {
  static final FCMTokenService _instance = FCMTokenService._internal();
  factory FCMTokenService() => _instance;
  FCMTokenService._internal();

  SupabaseClient get _supabase => Supabase.instance.client;
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  final SharedPreferencesService _prefs = SharedPreferencesService();
  final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();
  final Uuid _uuid = const Uuid();

  static const String _tableName = 'user_fcm_tokens';
  static const String _cacheKeyToken = 'cached_fcm_token';
  static const String _cacheKeyDeviceId = 'cached_device_id';

  /// حفظ FCM Token جديد
  Future<bool> saveToken({required String userId, String? customToken}) async {
    try {
      AppLogger.info(
        'Saving FCM token for user: $userId',
        category: LogCategory.general,
      );

      // الحصول على FCM Token
      final fcmToken = customToken ?? await _messaging.getToken();
      if (fcmToken == null) {
        AppLogger.warning(
          'FCM token is null, cannot save',
          category: LogCategory.general,
        );
        return false;
      }

      // الحصول على معلومات الجهاز
      final deviceInfo = await _getDeviceInfo();

      // استخدام UPSERT لضمان سجل واحد لكل مستخدم
      final tokenData = {
        'user_id': userId,
        'fcm_token': fcmToken,
        'device_info': deviceInfo,
        'is_active': true,
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from(_tableName).upsert(tokenData, onConflict: 'user_id');

      // تحديث Cache
      await _prefs.setString(_cacheKeyToken, fcmToken);

      AppLogger.info(
        'FCM token saved/updated successfully',
        category: LogCategory.general,
      );
      return true;
    } catch (e) {
      AppLogger.error(
        'Error saving FCM token',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// تحديث FCM Token موجود
  Future<bool> updateToken({
    required String userId,
    required String newToken,
  }) async {
    try {
      AppLogger.info(
        'Updating FCM token for user: $userId',
        category: LogCategory.general,
      );

      // الحصول على معلومات الجهاز
      final deviceInfo = await _getDeviceInfo();
      final deviceId = deviceInfo['device_id'] as String;

      // البحث عن Token بنفس device_id
      final response =
          await _supabase
              .from(_tableName)
              .select()
              .eq('user_id', userId)
              .eq('device_info->device_id', deviceId)
              .eq('is_active', true)
              .maybeSingle();

      if (response != null) {
        // تحديث Token موجود
        await _supabase
            .from(_tableName)
            .update({
              'fcm_token': newToken,
              'device_info': deviceInfo,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', response['id']);

        // تحديث Cache
        await _prefs.setString(_cacheKeyToken, newToken);

        AppLogger.info(
          'FCM token updated successfully',
          category: LogCategory.general,
        );
        return true;
      } else {
        // إنشاء Token جديد
        return await _createNewToken(userId, newToken, deviceInfo);
      }
    } catch (e) {
      AppLogger.error(
        'Error updating FCM token',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// حذف FCM Token (عند تسجيل الخروج)
  Future<bool> deleteToken({
    required String userId,
    String? specificToken,
  }) async {
    try {
      AppLogger.info(
        'Deleting FCM token for user: $userId',
        category: LogCategory.general,
      );

      if (specificToken != null) {
        // حذف token محدد
        await _supabase
            .from(_tableName)
            .update({
              'is_active': false,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('user_id', userId)
            .eq('fcm_token', specificToken);
      } else {
        // حذف جميع tokens للمستخدم
        await _supabase
            .from(_tableName)
            .update({
              'is_active': false,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('user_id', userId);
      }

      // مسح Cache
      await _prefs.remove(_cacheKeyToken);

      AppLogger.info(
        'FCM token deleted successfully',
        category: LogCategory.general,
      );
      return true;
    } catch (e) {
      AppLogger.error(
        'Error deleting FCM token',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// تحديث Token عند تغييره من Firebase
  Future<void> refreshToken(String newToken) async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        AppLogger.warning(
          'No current user, cannot refresh token',
          category: LogCategory.general,
        );
        return;
      }

      await updateToken(userId: currentUser.id, newToken: newToken);
    } catch (e) {
      AppLogger.error(
        'Error refreshing FCM token',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// الحصول على معلومات الجهاز
  Future<Map<String, dynamic>> _getDeviceInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      String platform = '';
      String model = '';
      String osVersion = '';
      String deviceId = '';

      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo.androidInfo;
        platform = 'android';
        model = androidInfo.model;
        osVersion = androidInfo.version.release;
        deviceId = androidInfo.id;
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo.iosInfo;
        platform = 'ios';
        model = iosInfo.model;
        osVersion = iosInfo.systemVersion;
        deviceId = iosInfo.identifierForVendor ?? _uuid.v4();
      }

      // Cache device ID
      await _prefs.setString(_cacheKeyDeviceId, deviceId);

      return {
        'platform': platform,
        'model': model,
        'os_version': osVersion,
        'app_version': packageInfo.version,
        'device_id': deviceId,
      };
    } catch (e) {
      AppLogger.error(
        'Error getting device info',
        category: LogCategory.general,
        error: e,
      );

      // استخدام معلومات افتراضية
      return {
        'platform': Platform.isAndroid ? 'android' : 'ios',
        'model': 'unknown',
        'os_version': 'unknown',
        'app_version': '1.0.0',
        'device_id': _uuid.v4(),
      };
    }
  }

  /// إنشاء Token جديد
  Future<bool> _createNewToken(
    String userId,
    String fcmToken,
    Map<String, dynamic> deviceInfo,
  ) async {
    try {
      // إلغاء تفعيل Tokens القديمة لنفس الجهاز
      await _deactivateOldTokensForDevice(userId, deviceInfo['device_id']);

      // إنشاء Token جديد
      final tokenData = {
        'id': _uuid.v4(),
        'user_id': userId,
        'fcm_token': fcmToken,
        'device_info': deviceInfo,
        'is_active': true,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      await _supabase.from(_tableName).insert(tokenData);

      // تحديث Cache
      await _prefs.setString(_cacheKeyToken, fcmToken);

      AppLogger.info(
        'New FCM token created successfully',
        category: LogCategory.general,
      );
      return true;
    } catch (e) {
      AppLogger.error(
        'Error creating new token',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// إلغاء تفعيل Tokens القديمة لنفس الجهاز
  Future<void> _deactivateOldTokensForDevice(
    String userId,
    String deviceId,
  ) async {
    try {
      await _supabase
          .from(_tableName)
          .update({
            'is_active': false,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('device_info->device_id', deviceId);

      AppLogger.info(
        'Old tokens deactivated for device: $deviceId',
        category: LogCategory.general,
      );
    } catch (e) {
      AppLogger.error(
        'Error deactivating old tokens',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// الحصول على Token من Cache
  Future<String?> getCachedToken() async {
    try {
      return _prefs.getString(_cacheKeyToken);
    } catch (e) {
      AppLogger.error(
        'Error getting cached token',
        category: LogCategory.general,
        error: e,
      );
      return null;
    }
  }

  /// التحقق من صحة Token
  Future<bool> isTokenValid(String token) async {
    try {
      // التحقق من طول Token
      if (token.length < 100) return false;

      // التحقق من وجود Token في قاعدة البيانات
      final response =
          await _supabase
              .from(_tableName)
              .select('id')
              .eq('fcm_token', token)
              .eq('is_active', true)
              .maybeSingle();

      return response != null;
    } catch (e) {
      AppLogger.error(
        'Error validating token',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// تنظيف Tokens غير النشطة (للاستخدام الدوري)
  Future<void> cleanupInactiveTokens() async {
    try {
      // حذف Tokens غير النشطة أقدم من 30 يوم
      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));

      await _supabase
          .from(_tableName)
          .delete()
          .eq('is_active', false)
          .lt('updated_at', cutoffDate.toIso8601String());

      AppLogger.info(
        'Inactive tokens cleaned up',
        category: LogCategory.general,
      );
    } catch (e) {
      AppLogger.error(
        'Error cleaning up inactive tokens',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// الحصول على جميع Tokens النشطة للمستخدم
  Future<List<FCMTokenModel>> getUserActiveTokens(String userId) async {
    try {
      final response = await _supabase
          .from(_tableName)
          .select()
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('updated_at', ascending: false);

      return (response as List)
          .map((json) => FCMTokenModel.fromJson(json))
          .toList();
    } catch (e) {
      AppLogger.error(
        'Error getting user active tokens',
        category: LogCategory.general,
        error: e,
      );
      return [];
    }
  }

  /// إحصائيات Tokens
  Future<Map<String, int>> getTokenStats(String userId) async {
    try {
      final response = await _supabase
          .from(_tableName)
          .select('is_active')
          .eq('user_id', userId);

      final tokens = response as List;
      final activeCount = tokens.where((t) => t['is_active'] == true).length;
      final inactiveCount = tokens.where((t) => t['is_active'] == false).length;

      return {
        'total': tokens.length,
        'active': activeCount,
        'inactive': inactiveCount,
      };
    } catch (e) {
      AppLogger.error(
        'Error getting token stats',
        category: LogCategory.general,
        error: e,
      );
      return {'total': 0, 'active': 0, 'inactive': 0};
    }
  }
}
