import 'package:equatable/equatable.dart';

/// نموذج الإشعار
class NotificationModel extends Equatable {
  final String id;
  final String title;
  final String body;
  final String? imageUrl;
  final Map<String, dynamic>? data;
  final DateTime createdAt;
  final bool isRead;
  final NotificationType type;
  final String? actionUrl;

  const NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    this.imageUrl,
    this.data,
    required this.createdAt,
    this.isRead = false,
    this.type = NotificationType.general,
    this.actionUrl,
  });

  /// إنشاء من JSON
  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      body: json['body'] ?? '',
      imageUrl: json['imageUrl'],
      data: json['data'] as Map<String, dynamic>?,
      createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
      isRead: json['isRead'] ?? false,
      type: NotificationType.fromString(json['type'] ?? 'general'),
      actionUrl: json['actionUrl'],
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'data': data,
      'createdAt': createdAt.toIso8601String(),
      'isRead': isRead,
      'type': type.name,
      'actionUrl': actionUrl,
    };
  }

  /// إنشاء من Firebase Message
  factory NotificationModel.fromFirebaseMessage(
    Map<String, dynamic> message, {
    String? messageId,
  }) {
    final notification = message['notification'] as Map<String, dynamic>?;
    final data = message['data'] as Map<String, dynamic>?;

    return NotificationModel(
      id: messageId ?? DateTime.now().millisecondsSinceEpoch.toString(),
      title: notification?['title'] ?? data?['title'] ?? 'إشعار جديد',
      body: notification?['body'] ?? data?['body'] ?? '',
      imageUrl: notification?['image'] ?? data?['image'],
      data: data,
      createdAt: DateTime.now(),
      isRead: false,
      type: NotificationType.fromString(data?['type'] ?? 'general'),
      actionUrl: data?['actionUrl'],
    );
  }

  /// نسخ مع تعديل
  NotificationModel copyWith({
    String? id,
    String? title,
    String? body,
    String? imageUrl,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    bool? isRead,
    NotificationType? type,
    String? actionUrl,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      body: body ?? this.body,
      imageUrl: imageUrl ?? this.imageUrl,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      type: type ?? this.type,
      actionUrl: actionUrl ?? this.actionUrl,
    );
  }

  /// تحديد ما إذا كان الإشعار جديد (أقل من 24 ساعة)
  bool get isNew {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    return difference.inHours < 24;
  }

  /// تنسيق الوقت للعرض
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  @override
  List<Object?> get props => [
        id,
        title,
        body,
        imageUrl,
        data,
        createdAt,
        isRead,
        type,
        actionUrl,
      ];
}

/// أنواع الإشعارات
enum NotificationType {
  general('general', 'عام', '📢'),
  appointment('appointment', 'موعد', '📅'),
  medical('medical', 'طبي', '🏥'),
  reminder('reminder', 'تذكير', '⏰'),
  promotion('promotion', 'عرض', '🎉'),
  system('system', 'نظام', '⚙️');

  const NotificationType(this.name, this.displayName, this.icon);

  final String name;
  final String displayName;
  final String icon;

  /// إنشاء من النص
  static NotificationType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'appointment':
        return NotificationType.appointment;
      case 'medical':
        return NotificationType.medical;
      case 'reminder':
        return NotificationType.reminder;
      case 'promotion':
        return NotificationType.promotion;
      case 'system':
        return NotificationType.system;
      default:
        return NotificationType.general;
    }
  }
}
