import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../../../domain/entities/appointment.dart';
import '../../../core/errors/failures.dart';
import '../../../data/repositories/appointment_repository.dart';

part 'appointment_event.dart';
part 'appointment_state.dart';

/// BLoC للمواعيد
class AppointmentBloc extends Bloc<AppointmentEvent, AppointmentState> {
  final AppointmentRepository _appointmentRepository;

  AppointmentBloc({AppointmentRepository? appointmentRepository})
      : _appointmentRepository = appointmentRepository ?? AppointmentRepository(),
        super(AppointmentInitial()) {
    on<LoadAppointments>(_onLoadAppointments);
    on<CreateAppointment>(_onCreateAppointment);
    on<UpdateAppointment>(_onUpdateAppointment);
    on<CancelAppointment>(_onCancelAppointment);
    on<DeleteAppointment>(_onDeleteAppointment);
    on<LoadAvailableTimeSlots>(_onLoadAvailableTimeSlots);
    on<SearchAppointments>(_onSearchAppointments);
    on<LoadAppointmentStats>(_onLoadAppointmentStats);
    on<RefreshAppointments>(_onRefreshAppointments);
  }

  /// تحميل المواعيد
  Future<void> _onLoadAppointments(
    LoadAppointments event,
    Emitter<AppointmentState> emit,
  ) async {
    emit(AppointmentLoading());

    try {
      final appointments = await _appointmentRepository.getPatientAppointments(
        patientId: event.patientId,
        status: event.status,
        fromDate: event.fromDate,
        toDate: event.toDate,
        limit: event.limit,
        offset: event.offset,
      );

      emit(AppointmentLoaded(appointments: appointments));
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// إنشاء موعد جديد
  Future<void> _onCreateAppointment(
    CreateAppointment event,
    Emitter<AppointmentState> emit,
  ) async {
    emit(AppointmentLoading());

    try {
      final appointment = await _appointmentRepository.createAppointment(
        patientId: event.patientId,
        appointmentDate: event.appointmentDate,
        timeSlot: event.timeSlot,
        type: event.type,
        notes: event.notes,
        doctorId: event.doctorId,
      );

      emit(AppointmentCreated(appointment: appointment));
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحديث موعد
  Future<void> _onUpdateAppointment(
    UpdateAppointment event,
    Emitter<AppointmentState> emit,
  ) async {
    emit(AppointmentLoading());

    try {
      final appointment = await _appointmentRepository.updateAppointment(
        appointmentId: event.appointmentId,
        appointmentDate: event.appointmentDate,
        timeSlot: event.timeSlot,
        type: event.type,
        status: event.status,
        notes: event.notes,
        doctorId: event.doctorId,
      );

      emit(AppointmentUpdated(appointment: appointment));
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// إلغاء موعد
  Future<void> _onCancelAppointment(
    CancelAppointment event,
    Emitter<AppointmentState> emit,
  ) async {
    emit(AppointmentLoading());

    try {
      final appointment = await _appointmentRepository.cancelAppointment(
        event.appointmentId,
      );

      emit(AppointmentCancelled(appointment: appointment));
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// حذف موعد
  Future<void> _onDeleteAppointment(
    DeleteAppointment event,
    Emitter<AppointmentState> emit,
  ) async {
    emit(AppointmentLoading());

    try {
      await _appointmentRepository.deleteAppointment(event.appointmentId);
      emit(AppointmentDeleted());
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل الأوقات المتاحة
  Future<void> _onLoadAvailableTimeSlots(
    LoadAvailableTimeSlots event,
    Emitter<AppointmentState> emit,
  ) async {
    emit(AppointmentLoading());

    try {
      final timeSlots = await _appointmentRepository.getAvailableTimeSlots(
        event.date,
      );

      emit(AvailableTimeSlotsLoaded(timeSlots: timeSlots));
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// البحث في المواعيد
  Future<void> _onSearchAppointments(
    SearchAppointments event,
    Emitter<AppointmentState> emit,
  ) async {
    emit(AppointmentLoading());

    try {
      final appointments = await _appointmentRepository.searchAppointments(
        patientId: event.patientId,
        searchTerm: event.searchTerm,
        status: event.status,
        type: event.type,
      );

      emit(AppointmentSearchResults(appointments: appointments));
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحميل إحصائيات المواعيد
  Future<void> _onLoadAppointmentStats(
    LoadAppointmentStats event,
    Emitter<AppointmentState> emit,
  ) async {
    emit(AppointmentLoading());

    try {
      final stats = await _appointmentRepository.getAppointmentStats(
        event.patientId,
      );

      emit(AppointmentStatsLoaded(stats: stats));
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }

  /// تحديث المواعيد
  Future<void> _onRefreshAppointments(
    RefreshAppointments event,
    Emitter<AppointmentState> emit,
  ) async {
    try {
      final appointments = await _appointmentRepository.getPatientAppointments(
        patientId: event.patientId,
        status: event.status,
        fromDate: event.fromDate,
        toDate: event.toDate,
        limit: event.limit,
        offset: event.offset,
      );

      emit(AppointmentLoaded(appointments: appointments));
    } catch (e) {
      emit(AppointmentError(
        failure: e is ServerFailure ? e : ServerFailure(message: e.toString()),
      ));
    }
  }
}
