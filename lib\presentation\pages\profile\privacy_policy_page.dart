import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../core/constants/app_colors.dart';
import '../../widgets/common/custom_app_bar.dart';
import 'package:url_launcher/url_launcher.dart';

/// صفحة الأمان والخصوصية
class PrivacyPolicyPage extends StatelessWidget {
  const PrivacyPolicyPage({super.key});

  Future<void> _sendEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      query:
          'subject=استفسار حول الأمان والخصوصية&body=مرحباً،%0A%0Aأود الاستفسار عن...',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        throw 'Could not launch email';
      }
    } catch (e) {
      // Handle email launch error silently
      debugPrint('Error launching email: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const CustomAppBar(
          title: 'الأمان والخصوصية',
          showBackButton: true,
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(24.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primary,
                      AppColors.primary.withValues(alpha: 0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Column(
                  children: [
                    Icon(Icons.security, size: 48.sp, color: Colors.white),
                    SizedBox(height: 12.h),
                    Text(
                      'الأمان والخصوصية',
                      style: TextStyle(
                        fontSize: 24.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'نحن ملتزمون بحماية خصوصيتك وأمان بياناتك',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 32.h),

              // جمع البيانات
              _buildSection(
                title: 'جمع البيانات وتخزينها',
                icon: Icons.data_usage,
                content: [
                  'المعلومات الشخصية: الاسم، البريد الإلكتروني، رقم الهاتف - لإنشاء حسابك والتواصل معك',
                  'البيانات الصحية: الوزن، الطول، العمر، النوع - لحساب احتياجاتك الغذائية وتقديم خطط مخصصة',
                  'التاريخ الطبي والأدوية: لتجنب التداخلات الغذائية وتقديم نصائح آمنة',
                  'صور الطعام (بإذنك): لتحليل محتوى الوجبات وحساب السعرات الحرارية تلقائياً',
                  'بيانات الموقع (اختيارية): لإيجاد أقرب العيادات والصيدليات',
                  'معلومات الجهاز: نوع الجهاز ونظام التشغيل لضمان توافق التطبيق',
                  'بيانات الاستخدام: الصفحات المزارة وأوقات الاستخدام لتحسين التجربة',
                ],
              ),

              SizedBox(height: 24.h),

              // استخدام البيانات
              _buildSection(
                title: 'كيفية استخدام البيانات',
                icon: Icons.settings,
                content: [
                  'إنشاء خطط غذائية مخصصة: نستخدم وزنك وطولك وعمرك لحساب احتياجاتك من السعرات والعناصر الغذائية',
                  'تحليل الطعام الذكي: نحلل صور وجباتك لحساب القيم الغذائية ومساعدتك في تتبع نظامك الغذائي',
                  'إرسال التذكيرات الطبية: نستخدم بياناتك لإرسال تذكيرات الأدوية والمواعيد في الأوقات المناسبة',
                  'متابعة التقدم الصحي: نحلل بياناتك لإنشاء تقارير عن تقدمك وتحسين خطتك العلاجية',
                  'التواصل معك: نرسل إشعارات مهمة حول صحتك وتحديثات الخدمة عبر البريد الإلكتروني أو الرسائل',
                  'تحسين الخدمة: نحلل أنماط الاستخدام لتطوير ميزات جديدة وتحسين أداء التطبيق',
                  'الدعم الفني: نستخدم معلومات جهازك لحل المشاكل التقنية وتقديم الدعم المناسب',
                ],
              ),

              SizedBox(height: 24.h),

              // حماية البيانات
              _buildSection(
                title: 'حماية وأمان البيانات',
                icon: Icons.shield,
                content: [
                  'التشفير المتقدم: نستخدم تشفير AES-256 لحماية جميع بياناتك الطبية والشخصية',
                  'النقل الآمن: جميع البيانات تُنقل عبر بروتوكول HTTPS/SSL المشفر',
                  'التحكم في الوصول: فقط الموظفون المصرح لهم يمكنهم الوصول لبياناتك',
                  'النسخ الاحتياطي: نحتفظ بنسخ احتياطية مشفرة لحماية بياناتك من الفقدان',
                  'مراقبة الأمان: نراقب أنظمتنا على مدار الساعة لاكتشاف أي محاولات اختراق',
                  'عدم المشاركة: لا نبيع أو نشارك بياناتك مع أطراف ثالثة لأغراض تجارية',
                  'الامتثال القانوني: نلتزم بقوانين حماية البيانات المحلية والدولية',
                ],
              ),

              SizedBox(height: 24.h),

              // حقوقك
              _buildSection(
                title: 'حقوقك في البيانات',
                icon: Icons.account_balance,
                content: [
                  'الوصول للبيانات: يمكنك طلب نسخة من جميع بياناتك المحفوظة لدينا',
                  'التصحيح: يمكنك تعديل أي معلومات خاطئة في ملفك الشخصي',
                  'الحذف: يمكنك طلب حذف حسابك وجميع بياناتك نهائياً',
                  'النقل: يمكنك طلب نقل بياناتك لخدمة أخرى بصيغة قابلة للقراءة',
                  'سحب الموافقة: يمكنك إيقاف استخدام بيانات معينة في أي وقت',
                  'التحكم في الإشعارات: يمكنك اختيار أنواع الإشعارات التي تريد استقبالها',
                  'الشكاوى: يمكنك تقديم شكوى لسلطات حماية البيانات إذا لزم الأمر',
                ],
              ),

              SizedBox(height: 24.h),

              // الأذونات والموارد المحمية
              _buildSection(
                title: 'الأذونات والموارد المحمية',
                icon: Icons.security,
                content: [
                  'الكاميرا: نطلب إذن الكاميرا لتصوير الوجبات وتحليل محتواها الغذائي تلقائياً',
                  'الصور: نطلب الوصول لمعرض الصور لاختيار صور الوجبات المحفوظة للتحليل',
                  'الإشعارات: نرسل تذكيرات الأدوية والمواعيد والنصائح الصحية المهمة',
                  'الموقع (اختياري): لإيجاد أقرب العيادات والصيدليات ومراكز الرعاية الصحية',
                  'جهات الاتصال (اختياري): لمشاركة تقاريرك الصحية مع طبيبك أو أفراد العائلة',
                  'التقويم (اختياري): لإضافة مواعيدك الطبية وتذكيرات الأدوية للتقويم',
                  'الميكروفون (اختياري): لتسجيل ملاحظات صوتية حول حالتك الصحية',
                ],
              ),

              SizedBox(height: 24.h),

              // مشاركة البيانات
              _buildSection(
                title: 'مشاركة البيانات',
                icon: Icons.people_outline,
                content: [
                  'مقدمو الرعاية الصحية: نشارك بياناتك الطبية مع طبيبك المعالج بموافقتك الصريحة',
                  'خدمات الطوارئ: في حالات الطوارئ الطبية، قد نشارك معلوماتك الحيوية لإنقاذ حياتك',
                  'مقدمو الخدمات التقنية: نستخدم خدمات سحابية آمنة لحفظ بياناتك مع ضمان الحماية الكاملة',
                  'السلطات القانونية: فقط عند وجود أمر قضائي أو طلب حكومي رسمي',
                  'البحث العلمي: قد نستخدم بيانات مجهولة الهوية للبحوث الطبية (بموافقتك)',
                  'لا نبيع البيانات: لا نبيع أو نؤجر بياناتك الشخصية لأي جهة تجارية',
                ],
              ),

              SizedBox(height: 24.h),

              // الاحتفاظ بالبيانات وحذفها
              _buildSection(
                title: 'الاحتفاظ بالبيانات وحذفها',
                icon: Icons.delete_forever,
                content: [
                  'مدة الاحتفاظ: نحتفظ ببياناتك طالما كان حسابك نشطاً أو حسب المتطلبات القانونية',
                  'البيانات الطبية: نحتفظ بسجلاتك الطبية لمدة 7 سنوات كما تتطلب القوانين الصحية',
                  'بيانات الاستخدام: نحذف بيانات الاستخدام التفصيلية بعد 3 سنوات من عدم النشاط',
                  'الحذف التلقائي: نحذف الحسابات غير النشطة بعد 5 سنوات مع إشعار مسبق',
                  'الحذف بناءً على الطلب: يمكنك طلب حذف حسابك فوراً من إعدادات التطبيق',
                  'النسخ الاحتياطية: تُحذف من النسخ الاحتياطية خلال 90 يوماً من حذف الحساب',
                  'البيانات المجهولة: قد نحتفظ ببيانات إحصائية مجهولة الهوية للبحث العلمي',
                ],
              ),

              SizedBox(height: 24.h),

              // حماية خصوصية الأطفال
              _buildSection(
                title: 'حماية خصوصية الأطفال',
                icon: Icons.child_care,
                content: [
                  'العمر المطلوب: التطبيق مخصص للأشخاص من عمر 13 سنة فما فوق',
                  'موافقة ولي الأمر: للمراهقين 13-17 سنة، نطلب موافقة ولي الأمر قبل جمع أي بيانات',
                  'حماية خاصة: نطبق حماية إضافية لبيانات المراهقين ونحد من جمع المعلومات',
                  'عدم التسويق: لا نرسل محتوى تسويقي للمستخدمين تحت 18 سنة',
                  'المراقبة الأبوية: نوفر أدوات للآباء لمراقبة استخدام أطفالهم للتطبيق',
                  'الحذف السريع: يمكن لولي الأمر طلب حذف بيانات الطفل فوراً',
                ],
              ),

              SizedBox(height: 32.h),

              // Contact section
              Container(
                width: double.infinity,
                padding: EdgeInsets.all(20.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(
                    color: AppColors.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.contact_support,
                      size: 32.sp,
                      color: AppColors.primary,
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      'هل لديك أسئلة حول الخصوصية؟',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      'نحن ملتزمون بالرد على جميع استفساراتك حول الخصوصية وحماية البيانات خلال 48 ساعة',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.email,
                          size: 16.sp,
                          color: AppColors.primary,
                        ),
                        SizedBox(width: 8.w),
                        Text(
                          '<EMAIL>',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColors.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 12.h),
                    Text(
                      'مسؤول حماية البيانات: <EMAIL>',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: AppColors.textSecondary,
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                    SizedBox(height: 16.h),
                    ElevatedButton(
                      onPressed: _sendEmail,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                      ),
                      child: Text(
                        'تواصل معنا حول الخصوصية',
                        style: TextStyle(fontSize: 14.sp, color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(height: 24.h),

              // التحديثات والامتثال
              _buildSection(
                title: 'التحديثات والامتثال',
                icon: Icons.update,
                content: [
                  'إشعار التحديثات: سنخطرك بأي تغييرات مهمة في سياسة الخصوصية قبل 30 يوماً',
                  'الامتثال المحلي: نلتزم بقوانين حماية البيانات في المملكة العربية السعودية',
                  'المعايير الدولية: متوافقون مع معايير GDPR الأوروبية وقوانين CCPA الأمريكية',
                  'مراجعة دورية: نراجع سياسة الخصوصية كل 6 أشهر لضمان الامتثال',
                  'تدقيق خارجي: نخضع لتدقيق أمني خارجي سنوياً من جهات معتمدة',
                  'شهادات الأمان: حاصلون على شهادات ISO 27001 و SOC 2 لأمان المعلومات',
                  'آخر تحديث: ديسمبر 2024 - الإصدار 3.2.2',
                ],
              ),

              SizedBox(height: 32.h),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection({
    required String title,
    required IconData icon,
    required List<String> content,
  }) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8.w),
                decoration: BoxDecoration(
                  color: AppColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8.r),
                ),
                child: Icon(icon, size: 20.sp, color: AppColors.primary),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w600,
                    color: AppColors.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          ...content.map(
            (item) => Padding(
              padding: EdgeInsets.only(bottom: 8.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 6.w,
                    height: 6.h,
                    margin: EdgeInsets.only(top: 6.h, left: 8.w),
                    decoration: BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      item,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
