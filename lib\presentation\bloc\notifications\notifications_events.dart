import 'package:equatable/equatable.dart';
import '../../../data/models/notification_model.dart';

/// أحداث الإشعارات
abstract class NotificationsEvent extends Equatable {
  const NotificationsEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل الإشعارات
class LoadNotifications extends NotificationsEvent {
  const LoadNotifications();
}

/// إضافة إشعار جديد
class AddNotification extends NotificationsEvent {
  final NotificationModel notification;

  const AddNotification(this.notification);

  @override
  List<Object?> get props => [notification];
}

/// تحديد إشعار كمقروء
class MarkNotificationAsRead extends NotificationsEvent {
  final String notificationId;

  const MarkNotificationAsRead(this.notificationId);

  @override
  List<Object?> get props => [notificationId];
}

/// تحديد جميع الإشعارات كمقروءة
class MarkAllNotificationsAsRead extends NotificationsEvent {
  const MarkAllNotificationsAsRead();
}

/// مسح جميع الإشعارات
class ClearAllNotifications extends NotificationsEvent {
  const ClearAllNotifications();
}

/// تحديث عداد الإشعارات غير المقروءة
class UpdateUnreadCount extends NotificationsEvent {
  const UpdateUnreadCount();
}

/// طباعة FCM Token
class PrintFCMToken extends NotificationsEvent {
  const PrintFCMToken();
}
