import 'package:flutter_bloc/flutter_bloc.dart';
import 'medical_record_event.dart';
import 'medical_record_state.dart';
import '../../../core/utils/app_logger.dart';
import '../../../data/models/patient_model.dart';

class MedicalRecordBloc extends Bloc<MedicalRecordEvent, MedicalRecordState> {
  MedicalRecordBloc() : super(const MedicalRecordInitial()) {
    on<LoadMedicalRecord>(_onLoadMedicalRecord);
    on<LoadMedicalRecordByAuthId>(_onLoadMedicalRecordByAuthId);
    on<RefreshMedicalRecord>(_onRefreshMedicalRecord);
    on<LoadPatientInfo>(_onLoadPatientInfo);
    on<LoadWeeklyResults>(_onLoadWeeklyResults);
    on<LoadMedicalInfo>(_onLoadMedicalInfo);
    on<LoadMedicalInfoByType>(_onLoadMedicalInfoByType);
    on<LoadLabTests>(_onLoadLabTests);
    on<LoadReminders>(_onLoadReminders);
    on<LoadActiveReminders>(_onLoadActiveReminders);
    on<LoadRecentLabTests>(_onLoadRecentLabTests);
    on<LoadMedicalSummary>(_onLoadMedicalSummary);
    on<ChangeTab>(_onChangeTab);
    on<ClearMedicalRecord>(_onClearMedicalRecord);
  }

  Future<void> _onLoadMedicalRecord(
    LoadMedicalRecord event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      emit(const MedicalRecordLoading());

      AppLogger.info(
        'Loading complete medical record for patient: ${event.patientId}',
        category: LogCategory.bloc,
      );

      // Load all data concurrently
      final results = await Future.wait([
        MedicalRecordService.getPatientInfo(event.patientId),
        MedicalRecordService.getWeeklyResults(event.patientId),
        MedicalRecordService.getMedicalInfo(event.patientId),
        MedicalRecordService.getLabTests(event.patientId),
        MedicalRecordService.getReminders(event.patientId),
      ]);

      final patient = results[0] as PatientModel?;
      final weeklyResults = results[1] as List<WeeklyResultModel>;
      final medicalInfo = results[2] as List<MedicalInfoModel>;
      final labTests = results[3] as List<LabTestModel>;
      final reminders = results[4] as List<ReminderModel>;

      if (patient == null) {
        emit(const MedicalRecordError('لم يتم العثور على بيانات المريض'));
        return;
      }

      emit(
        MedicalRecordLoaded(
          patient: patient,
          medicalInfo: medicalInfo,
         
        ),
      );

      AppLogger.info(
        'Medical record loaded successfully',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading medical record',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحميل السجل الطبي: ${e.toString()}'));
    }
  }

  Future<void> _onLoadMedicalRecordByAuthId(
    LoadMedicalRecordByAuthId event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      emit(const MedicalRecordLoading());

      AppLogger.info(
        'Loading medical record by auth ID: ${event.authId}',
        category: LogCategory.bloc,
      );

      final patient = await MedicalRecordService.getPatientByAuthId(
        event.authId,
      );

      if (patient == null) {
        emit(const MedicalRecordError('لم يتم العثور على بيانات المريض'));
        return;
      }

      // Now load the complete record using patient ID
      add(LoadMedicalRecord(patient.id));
    } catch (e) {
      AppLogger.error(
        'Error loading medical record by auth ID',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحميل السجل الطبي: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshMedicalRecord(
    RefreshMedicalRecord event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is MedicalRecordLoaded) {
        emit(currentState.copyWith(isRefreshing: true));
      }

      AppLogger.info(
        'Refreshing medical record for patient: ${event.patientId}',
        category: LogCategory.bloc,
      );

      // Load all data concurrently
      final results = await Future.wait([
        MedicalRecordService.getPatientInfo(event.patientId),
        MedicalRecordService.getWeeklyResults(event.patientId),
        MedicalRecordService.getMedicalInfo(event.patientId),
        MedicalRecordService.getLabTests(event.patientId),
        MedicalRecordService.getReminders(event.patientId),
      ]);

      final patient = results[0] as PatientModel?;

      if (patient == null) {
        emit(const MedicalRecordError('لم يتم العثور على بيانات المريض'));
        return;
      }

      final currentTabIndex =
          currentState is MedicalRecordLoaded
              ? currentState.currentTabIndex
              : 0;

      emit(
        MedicalRecordLoaded(
          patient: patient,
        
          currentTabIndex: currentTabIndex,
          isRefreshing: false,
        ),
      );

      AppLogger.info(
        'Medical record refreshed successfully',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error refreshing medical record',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحديث السجل الطبي: ${e.toString()}'));
    }
  }

  Future<void> _onLoadPatientInfo(
    LoadPatientInfo event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading patient info: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final patient = await MedicalRecordService.getPatientInfo(
        event.patientId,
      );

      if (patient == null) {
        emit(const MedicalRecordError('لم يتم العثور على بيانات المريض'));
        return;
      }

      emit(PatientInfoLoaded(patient));
      AppLogger.info(
        'Patient info loaded successfully',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading patient info',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError('حدث خطأ في تحميل بيانات المريض: ${e.toString()}'),
      );
    }
  }

  Future<void> _onLoadWeeklyResults(
    LoadWeeklyResults event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading weekly results: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final weeklyResults = await MedicalRecordService.getWeeklyResults(
        event.patientId,
      );

      emit(WeeklyResultsLoaded(weeklyResults));
      AppLogger.info(
        'Weekly results loaded successfully: ${weeklyResults.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading weekly results',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError(
          'حدث خطأ في تحميل النتائج الأسبوعية: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadMedicalInfo(
    LoadMedicalInfo event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading medical info: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final medicalInfo = await MedicalRecordService.getMedicalInfo(
        event.patientId,
      );

      emit(MedicalInfoLoaded(medicalInfo));
      AppLogger.info(
        'Medical info loaded successfully: ${medicalInfo.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading medical info',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError(
          'حدث خطأ في تحميل المعلومات الطبية: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadMedicalInfoByType(
    LoadMedicalInfoByType event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading medical info by type: ${event.infoType} for patient: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final medicalInfo = await MedicalRecordService.getMedicalInfoByType(
        event.patientId,
        event.infoType,
      );

      emit(MedicalInfoByTypeLoaded(medicalInfo, event.infoType));
      AppLogger.info(
        'Medical info by type loaded successfully: ${medicalInfo.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading medical info by type',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError(
          'حدث خطأ في تحميل المعلومات الطبية: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadLabTests(
    LoadLabTests event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading lab tests: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final labTests = await MedicalRecordService.getLabTests(event.patientId);

      emit(LabTestsLoaded(labTests));
      AppLogger.info(
        'Lab tests loaded successfully: ${labTests.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading lab tests',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError(
          'حدث خطأ في تحميل الفحوصات المخبرية: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadReminders(
    LoadReminders event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading reminders: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final reminders = await MedicalRecordService.getReminders(
        event.patientId,
      );

      emit(RemindersLoaded(reminders));
      AppLogger.info(
        'Reminders loaded successfully: ${reminders.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading reminders',
        category: LogCategory.bloc,
        error: e,
      );
      emit(MedicalRecordError('حدث خطأ في تحميل التذكيرات: ${e.toString()}'));
    }
  }

  Future<void> _onLoadActiveReminders(
    LoadActiveReminders event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading active reminders: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final activeReminders = await MedicalRecordService.getActiveReminders(
        event.patientId,
      );

      emit(ActiveRemindersLoaded(activeReminders));
      AppLogger.info(
        'Active reminders loaded successfully: ${activeReminders.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading active reminders',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError(
          'حدث خطأ في تحميل التذكيرات النشطة: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadRecentLabTests(
    LoadRecentLabTests event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading recent lab tests: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final recentLabTests = await MedicalRecordService.getRecentLabTests(
        event.patientId,
      );

      emit(RecentLabTestsLoaded(recentLabTests));
      AppLogger.info(
        'Recent lab tests loaded successfully: ${recentLabTests.length} records',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading recent lab tests',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError(
          'حدث خطأ في تحميل الفحوصات الحديثة: ${e.toString()}',
        ),
      );
    }
  }

  Future<void> _onLoadMedicalSummary(
    LoadMedicalSummary event,
    Emitter<MedicalRecordState> emit,
  ) async {
    try {
      AppLogger.info(
        'Loading medical summary: ${event.patientId}',
        category: LogCategory.bloc,
      );

      final summary = await MedicalRecordService.getMedicalSummary(
        event.patientId,
      );

      emit(MedicalSummaryLoaded(summary));
      AppLogger.info(
        'Medical summary loaded successfully',
        category: LogCategory.bloc,
      );
    } catch (e) {
      AppLogger.error(
        'Error loading medical summary',
        category: LogCategory.bloc,
        error: e,
      );
      emit(
        MedicalRecordError(
          'حدث خطأ في تحميل ملخص السجل الطبي: ${e.toString()}',
        ),
      );
    }
  }

  void _onChangeTab(ChangeTab event, Emitter<MedicalRecordState> emit) {
    final currentState = state;
    if (currentState is MedicalRecordLoaded) {
      emit(currentState.copyWith(currentTabIndex: event.tabIndex));
      AppLogger.info(
        'Tab changed to index: ${event.tabIndex}',
        category: LogCategory.bloc,
      );
    } else {
      emit(TabChanged(event.tabIndex));
    }
  }

  void _onClearMedicalRecord(
    ClearMedicalRecord event,
    Emitter<MedicalRecordState> emit,
  ) {
    emit(const MedicalRecordInitial());
    AppLogger.info('Medical record cleared', category: LogCategory.bloc);
  }
}
