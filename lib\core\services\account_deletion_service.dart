import 'package:supabase_flutter/supabase_flutter.dart';
import '../utils/app_logger.dart';
import 'shared_preferences_service.dart';

/// خدمة حذف الحساب بالكامل
class AccountDeletionService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static final SharedPreferencesService _prefs = SharedPreferencesService();

  /// حذف الحساب بالكامل
  static Future<void> deleteAccount() async {
    try {
      AppLogger.info(
        '🗑️ Starting account deletion process',
        category: LogCategory.auth,
      );

      // الحصول على المستخدم الحالي
      final user = _supabase.auth.currentUser;
      if (user == null) {
        throw Exception('لا يوجد مستخدم مسجل دخول');
      }

      final userId = user.id;
      final userEmail = user.email;

      AppLogger.info(
        '📧 Deleting account for user',
        category: LogCategory.auth,
        data: {
          'userId': userId,
          'email': userEmail ?? 'unknown',
        },
      );

      // استدعاء Edge Function لحذف الحساب
      final response = await _supabase.functions.invoke(
        'delete_user',
        headers: {
          'Authorization': 'Bearer ${_supabase.auth.currentSession?.accessToken}',
          'Content-Type': 'application/json',
        },
        body: {
          'user_id': userId,
        },
      );

      AppLogger.info(
        '📡 Edge function response',
        category: LogCategory.auth,
        data: {
          'status': response.status.toString(),
          'data': response.data.toString(),
        },
      );

      // التحقق من نجاح العملية
      if (response.status != 200) {
        throw Exception('فشل في حذف الحساب: ${response.data}');
      }

      // حذف البيانات المحلية
      await _clearLocalData();

      AppLogger.info(
        '✅ Account deleted successfully',
        category: LogCategory.auth,
        data: {
          'userId': userId,
          'email': userEmail ?? 'unknown',
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to delete account',
        category: LogCategory.auth,
        error: e,
      );
      rethrow;
    }
  }

  /// حذف البيانات المحلية
  static Future<void> _clearLocalData() async {
    try {
      AppLogger.info(
        '🧹 Clearing local data',
        category: LogCategory.auth,
      );

      // حذف جميع البيانات من SharedPreferences
      await _prefs.clearAll();

      // تسجيل الخروج من Supabase
      await _supabase.auth.signOut();

      AppLogger.info(
        '✅ Local data cleared successfully',
        category: LogCategory.auth,
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to clear local data',
        category: LogCategory.auth,
        error: e,
      );
      // لا نرمي الخطأ هنا لأن الحساب تم حذفه بالفعل
    }
  }

  /// التحقق من إمكانية حذف الحساب
  static bool canDeleteAccount() {
    final user = _supabase.auth.currentUser;
    return user != null;
  }

  /// الحصول على معلومات المستخدم للتأكيد
  static Map<String, String?> getUserInfoForConfirmation() {
    final user = _supabase.auth.currentUser;
    return {
      'email': user?.email,
      'id': user?.id,
      'createdAt': user?.createdAt,
    };
  }
}
