import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../core/constants/app_colors.dart';
import '../../../core/constants/app_strings.dart';
import '../../../core/utils/helpers.dart';
import '../../../core/utils/app_logger.dart';
import '../../../domain/entities/product.dart';
import '../../../domain/entities/category.dart';
import '../../../data/repositories/product_repository.dart';
import '../../widgets/common/custom_app_bar.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/common/empty_state_widget.dart';
import '../../widgets/common/custom_text_field.dart';
import '../../widgets/common/filter_button.dart';
import '../../widgets/products/product_card.dart';

/// صفحة المنتجات
class ProductsPage extends StatefulWidget {
  const ProductsPage({super.key});

  @override
  State<ProductsPage> createState() => _ProductsPageState();
}

class _ProductsPageState extends State<ProductsPage> {
  final TextEditingController _searchController = TextEditingController();
  final ProductRepository _productRepository = ProductRepository();

  bool _isLoading = false;
  List<Product> _products = [];
  List<Product> _filteredProducts = [];
  List<Category> _categories = [];
  String _selectedCategoryId = 'الكل';
  bool _hasLoadedData = false;

  @override
  void initState() {
    super.initState();
    // لا نحمل البيانات عند التهيئة - سيتم التحميل عند الحاجة
  }

  void _loadDataIfNeeded() {
    if (!_hasLoadedData) {
      _loadProducts();
      _hasLoadedData = true;
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadProducts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      AppLogger.info(
        '🛍️ Loading products and categories',
        category: LogCategory.ui,
      );

      // تحميل الفئات والمنتجات بشكل متوازي
      final results = await Future.wait([
        _productRepository.getAllCategories(),
        _productRepository.getAllProducts(
          categoryId:
              _selectedCategoryId == 'الكل' ? null : _selectedCategoryId,
          isAvailable: true,
        ),
      ]);

      _categories = [
        const Category(id: 'الكل', name: 'الكل'),
        ...results[0] as List<Category>,
      ];
      _products = results[1] as List<Product>;
      _filteredProducts = _products;

      AppLogger.info(
        '✅ Products and categories loaded successfully',
        category: LogCategory.ui,
        data: {
          'productsCount': _products.length.toString(),
          'categoriesCount': _categories.length.toString(),
        },
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to load products',
        category: LogCategory.ui,
        error: e,
      );
      if (mounted) {
        Helpers.showErrorSnackBar(context, 'فشل في تحميل المنتجات');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _filterProducts() {
    setState(() {
      _filteredProducts =
          _products.where((product) {
            final matchesSearch = product.name.toLowerCase().contains(
              _searchController.text.toLowerCase(),
            );
            final matchesCategory =
                _selectedCategoryId == 'الكل' ||
                product.categoryId == _selectedCategoryId;
            return matchesSearch && matchesCategory;
          }).toList();
    });
  }

  Future<void> _onCategoryChanged(String categoryId) async {
    setState(() {
      _selectedCategoryId = categoryId;
      _isLoading = true;
    });

    try {
      final products = await _productRepository.getAllProducts(
        categoryId: categoryId == 'الكل' ? null : categoryId,
        isAvailable: true,
      );

      setState(() {
        _products = products;
        _filteredProducts = products;
        _isLoading = false;
      });

      _filterProducts(); // تطبيق فلتر البحث إذا كان موجود
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        Helpers.showErrorSnackBar(context, 'فشل في تحميل المنتجات');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // تحميل البيانات عند أول عرض للصفحة
    _loadDataIfNeeded();

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.background,
        appBar: const CustomAppBar(title: AppStrings.products),
        body: Column(
          children: [
            // شريط البحث والفلترة
            _buildSearchAndFilter(),

            // قائمة المنتجات
            Expanded(child: _buildProductsList()),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter() {
    return Container(
      color: AppColors.white,
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // شريط البحث
          SearchTextField(
            controller: _searchController,
            hint: 'ابحث عن المنتجات...',
            onChanged: (_) => _filterProducts(),
            onClear: () {
              _searchController.clear();
              _filterProducts();
            },
          ),

          SizedBox(height: 16.h),

          // فلتر الفئات
          SizedBox(
            height: 40.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _categories.length,
              itemBuilder: (context, index) {
                final category = _categories[index];
                final isSelected = _selectedCategoryId == category.id;

                return ProductFilterButton(
                  text: category.name,
                  isSelected: isSelected,
                  onTap: () => _onCategoryChanged(category.id),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList() {
    if (_isLoading) {
      return const GridLoadingWidget();
    }

    if (_filteredProducts.isEmpty) {
      return _searchController.text.isNotEmpty
          ? EmptySearchWidget(searchQuery: _searchController.text)
          : const EmptyStateWidget(
            icon: Icons.shopping_bag_outlined,
            title: AppStrings.noProductsMessage,
            subtitle: 'لا توجد منتجات متاحة حالياً',
          );
    }

    return RefreshIndicator(
      onRefresh: _loadProducts,
      color: AppColors.primary,
      child: GridView.builder(
        padding: EdgeInsets.all(8.w),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: Helpers.getGridCrossAxisCount(context),
          crossAxisSpacing: 8.w,
          mainAxisSpacing: 8.h,
          childAspectRatio: 0.7, // نسبة محسنة للنصوص الأكبر
        ),
        itemCount: _filteredProducts.length,
        itemBuilder: (context, index) {
          final product = _filteredProducts[index];
          return ProductCard(
            product: product,
            onTap: () => _showProductDetails(product),
          );
        },
      ),
    );
  }

  void _showProductDetails(Product product) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Directionality(
            textDirection: TextDirection.rtl,
            child: ProductDetailsSheet(product: product),
          ),
    );
  }
}

/// ورقة تفاصيل المنتج
class ProductDetailsSheet extends StatefulWidget {
  final Product product;

  const ProductDetailsSheet({super.key, required this.product});

  @override
  State<ProductDetailsSheet> createState() => _ProductDetailsSheetState();
}

class _ProductDetailsSheetState extends State<ProductDetailsSheet> {
  late PageController _pageController;
  int _currentImageIndex = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.85,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: Column(
          children: [
            // مقبض السحب
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.only(top: 12.h),
              decoration: BoxDecoration(
                color: AppColors.border,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),

            // المحتوى
            Expanded(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(16.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // معرض الصور
                    _buildImageGallery(),

                    SizedBox(height: 16.h),

                    // اسم المنتج
                    _buildProductName(),

                    SizedBox(height: 8.h),

                    // الفئة
                    if (widget.product.categoryName != null)
                      _buildCategoryChip(),

                    SizedBox(height: 12.h),

                    // السعر والخصم
                    _buildPriceSection(),

                    SizedBox(height: 12.h),

                    // حالة المخزون
                    _buildStockInfo(),

                    SizedBox(height: 16.h),

                    // الوصف
                    if (widget.product.hasDescription) _buildDescription(),

                    SizedBox(height: 16.h),

                    // معلومات إضافية
                    _buildAdditionalInfo(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageGallery() {
    if (widget.product.images.isEmpty) {
      return _buildPlaceholderImage();
    }

    return SizedBox(
      height: 280.h,
      width: double.infinity,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: Stack(
          children: [
            // PageView للصور
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentImageIndex = index;
                });
              },
              itemCount: widget.product.images.length,
              itemBuilder: (context, index) {
                final image = widget.product.images[index];
                return GestureDetector(
                  onTap: () => _showFullScreenImage(index),
                  child: Hero(
                    tag: 'product_image_$index',
                    child: CachedNetworkImage(
                      imageUrl: image.imageUrl,
                      fit: BoxFit.cover,
                      placeholder:
                          (context, url) => _buildImageLoadingPlaceholder(),
                      errorWidget:
                          (context, url, error) =>
                              _buildImageErrorPlaceholder(),
                      fadeInDuration: Duration(milliseconds: 300),
                      fadeOutDuration: Duration(milliseconds: 100),
                    ),
                  ),
                );
              },
            ),

            // أزرار التنقل
            if (widget.product.images.length > 1) ...[
              // زر السابق
              Positioned(
                left: 8.w,
                top: 0,
                bottom: 0,
                child: Center(
                  child: Container(
                    width: 40.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () {
                        if (_currentImageIndex > 0) {
                          _pageController.previousPage(
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        }
                      },
                      icon: Icon(
                        Icons.arrow_back_ios,
                        color: Colors.white,
                        size: 18.sp,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ),

              // زر التالي
              Positioned(
                right: 8.w,
                top: 0,
                bottom: 0,
                child: Center(
                  child: Container(
                    width: 40.w,
                    height: 40.h,
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () {
                        if (_currentImageIndex <
                            widget.product.images.length - 1) {
                          _pageController.nextPage(
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        }
                      },
                      icon: Icon(
                        Icons.arrow_forward_ios,
                        color: Colors.white,
                        size: 18.sp,
                      ),
                      padding: EdgeInsets.zero,
                    ),
                  ),
                ),
              ),

              // مؤشر عدد الصور
              Positioned(
                top: 12.h,
                right: 12.w,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    '${_currentImageIndex + 1} / ${widget.product.images.length}',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ],

            // مؤشرات النقاط
            if (widget.product.images.length > 1)
              Positioned(
                bottom: 12.h,
                left: 0,
                right: 0,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    widget.product.images.length,
                    (index) => GestureDetector(
                      onTap: () {
                        _pageController.animateToPage(
                          index,
                          duration: Duration(milliseconds: 300),
                          curve: Curves.easeInOut,
                        );
                      },
                      child: AnimatedContainer(
                        duration: Duration(milliseconds: 200),
                        margin: EdgeInsets.symmetric(horizontal: 3.w),
                        width: _currentImageIndex == index ? 24.w : 8.w,
                        height: 8.h,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4.r),
                          color:
                              _currentImageIndex == index
                                  ? Colors.white
                                  : Colors.white.withValues(alpha: 0.5),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholderImage() {
    return Container(
      height: 280.h,
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: AppColors.textLight.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.image_outlined,
              size: 60.sp,
              color: AppColors.primary.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 16.h),
          Text(
            'لا توجد صورة متاحة',
            style: TextStyle(
              fontSize: 16.sp,
              color: AppColors.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            'سيتم إضافة الصور قريباً',
            style: TextStyle(fontSize: 12.sp, color: AppColors.textLight),
          ),
        ],
      ),
    );
  }

  Widget _buildImageLoadingPlaceholder() {
    return Container(
      color: AppColors.surfaceVariant,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 40.w,
              height: 40.h,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  CircularProgressIndicator(
                    color: AppColors.primary,
                    strokeWidth: 3,
                  ),
                  Container(
                    width: 24.w,
                    height: 24.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        'assets/images/logo.jpeg',
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Icon(
                            Icons.health_and_safety_rounded,
                            color: AppColors.primary,
                            size: 16.w,
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 12.h),
            Text(
              'جاري التحميل...',
              style: TextStyle(fontSize: 12.sp, color: AppColors.textSecondary),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageErrorPlaceholder() {
    return Container(
      color: AppColors.surfaceVariant,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image_outlined,
              size: 40.sp,
              color: AppColors.textLight,
            ),
            SizedBox(height: 8.h),
            Text(
              'فشل في تحميل الصورة',
              style: TextStyle(fontSize: 10.sp, color: AppColors.textLight),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductName() {
    return Text(
      widget.product.name,
      style: TextStyle(
        fontSize: 22.sp,
        fontWeight: FontWeight.bold,
        color: AppColors.textPrimary,
        height: 1.3,
      ),
      textAlign: TextAlign.start,
    );
  }

  Widget _buildCategoryChip() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(25.r),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.category_outlined, size: 14.sp, color: AppColors.primary),
          SizedBox(width: 6.w),
          Text(
            widget.product.categoryName!,
            style: TextStyle(
              fontSize: 13.sp,
              color: AppColors.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSection() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: AppColors.primary.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.local_offer_outlined,
            color: AppColors.primary,
            size: 20.sp,
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.product.hasDiscount) ...[
                  Text(
                    'السعر الأصلي',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textLight,
                    ),
                  ),
                  Text(
                    Helpers.formatPrice(widget.product.price!),
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: AppColors.textLight,
                      decoration: TextDecoration.lineThrough,
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    'السعر بعد الخصم',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
                Text(
                  Helpers.formatPrice(widget.product.discountedPrice!),
                  style: TextStyle(
                    fontSize: 28.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
              ],
            ),
          ),
          if (widget.product.hasDiscount)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: AppColors.error,
                borderRadius: BorderRadius.circular(20.r),
              ),
              child: Text(
                'خصم ${widget.product.discountPercentage.toInt()}%',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStockInfo() {
    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color:
            widget.product.isAvailable
                ? AppColors.success.withValues(alpha: 0.1)
                : AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(
          color:
              widget.product.isAvailable
                  ? AppColors.success.withValues(alpha: 0.3)
                  : AppColors.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(6.w),
            decoration: BoxDecoration(
              color:
                  widget.product.isAvailable
                      ? AppColors.success
                      : AppColors.error,
              shape: BoxShape.circle,
            ),
            child: Icon(
              widget.product.isAvailable ? Icons.check : Icons.close,
              color: AppColors.white,
              size: 16.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حالة المخزون',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  widget.product.stockStatus,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color:
                        widget.product.isAvailable
                            ? AppColors.success
                            : AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (widget.product.isAvailable)
                  Text(
                    '${widget.product.stock} قطعة متوفرة',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.textSecondary,
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.description_outlined,
                color: AppColors.primary,
                size: 20.sp,
              ),
              SizedBox(width: 8.w),
              Text(
                'وصف المنتج',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            widget.product.description!,
            style: TextStyle(
              fontSize: 15.sp,
              color: AppColors.textSecondary,
              height: 1.6,
            ),
            textAlign: TextAlign.justify,
          ),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border, width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: AppColors.primary, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                'معلومات إضافية',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildInfoRow(
            'كود المنتج',
            widget.product.productCode,
            Icons.qr_code,
          ),
          if (widget.product.categoryName != null)
            _buildInfoRow(
              'الفئة',
              widget.product.categoryName!,
              Icons.category,
            ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: AppColors.surfaceVariant,
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6.r),
            ),
            child: Icon(icon, size: 16.sp, color: AppColors.primary),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 15.sp,
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showFullScreenImage(int initialIndex) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => FullScreenImageGallery(
              images: widget.product.images,
              initialIndex: initialIndex,
              productName: widget.product.name,
            ),
      ),
    );
  }
}

class FullScreenImageGallery extends StatefulWidget {
  final List<ProductImage> images;
  final int initialIndex;
  final String productName;

  const FullScreenImageGallery({
    super.key,
    required this.images,
    required this.initialIndex,
    required this.productName,
  });

  @override
  State<FullScreenImageGallery> createState() => _FullScreenImageGalleryState();
}

class _FullScreenImageGalleryState extends State<FullScreenImageGallery> {
  late PageController _pageController;
  late int _currentIndex;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: Colors.black,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: Colors.black.withValues(alpha: 0.7),
          foregroundColor: Colors.white,
          elevation: 0,
          title: Text(
            widget.productName,
            style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w600),
          ),
          actions: [
            if (widget.images.length > 1)
              Container(
                margin: EdgeInsets.only(left: 16.w),
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20.r),
                ),
                child: Text(
                  '${_currentIndex + 1} / ${widget.images.length}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
          ],
        ),
        body: Stack(
          children: [
            // معرض الصور
            PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              itemCount: widget.images.length,
              itemBuilder: (context, index) {
                return InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 4.0,
                  child: Center(
                    child: Hero(
                      tag: 'product_image_$index',
                      child: CachedNetworkImage(
                        imageUrl: widget.images[index].imageUrl,
                        fit: BoxFit.contain,
                        placeholder:
                            (context, url) => Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SizedBox(
                                    width: 60.w,
                                    height: 60.h,
                                    child: Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        CircularProgressIndicator(
                                          color: AppColors.primary,
                                          strokeWidth: 4,
                                        ),
                                        Container(
                                          width: 36.w,
                                          height: 36.h,
                                          decoration: BoxDecoration(
                                            color: Colors.white,
                                            shape: BoxShape.circle,
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.black.withValues(
                                                  alpha: 0.2,
                                                ),
                                                blurRadius: 3,
                                              ),
                                            ],
                                          ),
                                          child: ClipOval(
                                            child: Image.asset(
                                              'assets/images/logo.jpeg',
                                              fit: BoxFit.cover,
                                              errorBuilder: (
                                                context,
                                                error,
                                                stackTrace,
                                              ) {
                                                return Icon(
                                                  Icons
                                                      .health_and_safety_rounded,
                                                  color: AppColors.primary,
                                                  size: 24.w,
                                                );
                                              },
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 20.h),
                                  Text(
                                    'جاري تحميل الصورة...',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 16.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        errorWidget:
                            (context, url, error) => Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.broken_image_outlined,
                                    color: Colors.white.withValues(alpha: 0.7),
                                    size: 80.sp,
                                  ),
                                  SizedBox(height: 20.h),
                                  Text(
                                    'فشل في تحميل الصورة',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 18.sp,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  SizedBox(height: 8.h),
                                  Text(
                                    'تحقق من اتصال الإنترنت',
                                    style: TextStyle(
                                      color: Colors.white.withValues(
                                        alpha: 0.7,
                                      ),
                                      fontSize: 14.sp,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                      ),
                    ),
                  ),
                );
              },
            ),

            // مؤشرات الصور في الأسفل
            if (widget.images.length > 1)
              Positioned(
                bottom: 40.h,
                left: 0,
                right: 0,
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 12.h),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(
                      widget.images.length,
                      (index) => GestureDetector(
                        onTap: () {
                          _pageController.animateToPage(
                            index,
                            duration: Duration(milliseconds: 300),
                            curve: Curves.easeInOut,
                          );
                        },
                        child: AnimatedContainer(
                          duration: Duration(milliseconds: 200),
                          margin: EdgeInsets.symmetric(horizontal: 4.w),
                          width: _currentIndex == index ? 32.w : 8.w,
                          height: 8.h,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.r),
                            color:
                                _currentIndex == index
                                    ? Colors.white
                                    : Colors.white.withValues(alpha: 0.4),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
