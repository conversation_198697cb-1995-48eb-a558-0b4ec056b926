import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/patient.dart';
import '../../core/utils/app_logger.dart';

/// Repository للتعامل مع بيانات المرضى في Supabase
class PatientRepository {
  final SupabaseClient _supabase = Supabase.instance.client;

  /// إنشاء مريض جديد
  Future<Patient> createPatient({
    required String userId,
    required String name,
    required String email,
    String? phone,
    String? gender,
    DateTime? birthDate,
    double? weight,
    double? height,
    int? age,
  }) async {
    AppLogger.info(
      '🏥 Creating patient record',
      category: LogCategory.database,
      data: {
        'authId': userId,
        'name': name,
        'email': email,
        'phone': phone ?? 'null',
        'gender': gender ?? 'null',
        'hasWeight': (weight != null).toString(),
        'hasHeight': (height != null).toString(),
        'age': age?.toString() ?? 'null',
        'hasBirthDate': (birthDate != null).toString(),
      },
    );

    try {
      final insertData = {
        'user_id': userId, // استخدام user_id بدلاً من auth_id
        'name': name,
        'email': email,
        'phone': phone,
        'gender': gender,
        'birth_date': birthDate?.toIso8601String(),
        'weight': weight,
        'height': height,
        'age': age,
        'is_premium': false,
        // لا نحتاج created_at و updated_at لأنهما يتم توليدهما تلقائياً
      };

      AppLogger.database('INSERT', 'patients', data: insertData);

      final response =
          await _supabase.from('patients').insert(insertData).select().single();

      AppLogger.info(
        '✅ Patient record created successfully',
        category: LogCategory.database,
        data: {
          'patientId': response['id']?.toString() ?? 'null',
          'authId': userId,
          'name': name,
          'generatedId': response['id']?.toString() ?? 'null',
        },
      );

      return Patient.fromJson(response);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to create patient record',
        category: LogCategory.database,
        data: {
          'authId': userId,
          'name': name,
          'email': email,
          'errorType': e.runtimeType.toString(),
        },
        error: e,
      );

      throw Exception('فشل في إنشاء مل�� المريض: ${e.toString()}');
    }
  }

  /// الحصول على بيانات المريض بواسطة user_id
  Future<Patient?> getPatientByUserId(String authId) async {
    try {
      AppLogger.info(
        '🔍 Getting patient by user_id',
        category: LogCategory.database,
        data: {'authId': authId},
      );

      final response =
          await _supabase
              .from('patients')
              .select()
              .eq('user_id', authId) // استخدام user_id بدلاً من auth_id
              .maybeSingle();

      if (response == null) {
        AppLogger.info(
          'ℹ️ No patient found for user_id',
          category: LogCategory.database,
          data: {'authId': authId},
        );
        return null;
      }

      AppLogger.info(
        '✅ Patient found successfully',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'patientId': response['id']?.toString() ?? 'null',
          'name': response['name']?.toString() ?? 'null',
        },
      );

      return Patient.fromJson(response);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to get patient by user_id',
        category: LogCategory.database,
        data: {'authId': authId},
        error: e,
      );
      throw Exception('فشل في جلب بيانات المريض: ${e.toString()}');
    }
  }

  /// تحديث بيانات المريض
  Future<Patient> updatePatient({
    required String authId,
    String? name,
    String? phone,
    String? gender,
    DateTime? birthDate,
    double? weight,
    double? height,
    int? age,
    bool? isPremium,
  }) async {
    try {
      AppLogger.info(
        '🔄 Updating patient record',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'hasName': (name != null).toString(),
          'hasPhone': (phone != null).toString(),
          'hasGender': (gender != null).toString(),
          'hasBirthDate': (birthDate != null).toString(),
          'hasWeight': (weight != null).toString(),
          'hasHeight': (height != null).toString(),
          'hasAge': (age != null).toString(),
          'hasPremium': (isPremium != null).toString(),
        },
      );

      final updateData = <String, dynamic>{
        // updated_at سيتم تحديثه تلقائياً بواسطة trigger
      };

      if (name != null) updateData['name'] = name;
      if (phone != null) updateData['phone'] = phone;
      if (gender != null) updateData['gender'] = gender;
      if (birthDate != null) {
        updateData['birth_date'] = birthDate.toIso8601String();
      }
      if (weight != null) updateData['weight'] = weight;
      if (height != null) updateData['height'] = height;
      if (age != null) updateData['age'] = age;
      if (isPremium != null) updateData['is_premium'] = isPremium;

      AppLogger.database('UPDATE', 'patients', data: updateData);

      final response =
          await _supabase
              .from('patients')
              .update(updateData)
              .eq('user_id', authId) // استخدام user_id بدلاً من auth_id
              .select()
              .single();

      AppLogger.info(
        '✅ Patient record updated successfully',
        category: LogCategory.database,
        data: {
          'authId': authId,
          'patientId': response['id']?.toString() ?? 'null',
        },
      );

      return Patient.fromJson(response);
    } catch (e) {
      AppLogger.error(
        '❌ Failed to update patient record',
        category: LogCategory.database,
        data: {'authId': authId},
        error: e,
      );
      throw Exception('فشل في تحديث بيانات المريض: ${e.toString()}');
    }
  }

  /// حذف بيانات المريض
  Future<void> deletePatient(String authId) async {
    try {
      AppLogger.info(
        '🗑️ Deleting patient record',
        category: LogCategory.database,
        data: {'authId': authId},
      );

      await _supabase
          .from('patients')
          .delete()
          .eq('user_id', authId); // استخدام user_id بدلاً من auth_id

      AppLogger.info(
        '✅ Patient record deleted successfully',
        category: LogCategory.database,
        data: {'authId': authId},
      );
    } catch (e) {
      AppLogger.error(
        '❌ Failed to delete patient record',
        category: LogCategory.database,
        data: {'authId': authId},
        error: e,
      );
      throw Exception('فشل في حذف بيانات المريض: ${e.toString()}');
    }
  }

  /// الحصول على جميع المرضى (للإدارة)
  Future<List<Patient>> getAllPatients({int? limit, int? offset}) async {
    try {
      var query = _supabase
          .from('patients')
          .select()
          .order('created_at', ascending: false);

      if (limit != null) {
        query = query.limit(limit);
      }

      if (offset != null) {
        query = query.range(offset, offset + (limit ?? 10) - 1);
      }

      final response = await query;

      return response.map((json) => Patient.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في جلب قائمة المرضى: ${e.toString()}');
    }
  }

  /// البحث عن المرضى
  Future<List<Patient>> searchPatients(String searchTerm) async {
    try {
      final response = await _supabase
          .from('patients')
          .select()
          .or(
            'name.ilike.%$searchTerm%,email.ilike.%$searchTerm%,phone.ilike.%$searchTerm%',
          )
          .order('created_at', ascending: false);

      return response.map((json) => Patient.fromJson(json)).toList();
    } catch (e) {
      throw Exception('فشل في البحث عن المرضى: ${e.toString()}');
    }
  }
}