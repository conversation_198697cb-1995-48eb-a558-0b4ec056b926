import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

import '../../data/models/notification_model.dart';
import '../utils/app_logger.dart';
import 'shared_preferences_service.dart';

/// خدمة إدارة الإشعارات
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  static const String _notificationsKey = 'stored_notifications';
  static const String _fcmTokenKey = 'fcm_token';

  late FirebaseMessaging _firebaseMessaging;
  late FlutterLocalNotificationsPlugin _localNotifications;
  late SharedPreferencesService _prefs;

  /// Callback عند استلام إشعار
  Function(NotificationModel)? onNotificationReceived;

  /// Callback عند النقر على إشعار
  Function(NotificationModel)? onNotificationTapped;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    try {
      AppLogger.info('🔔 Initializing Notification Service...');

      _prefs = SharedPreferencesService();
      _firebaseMessaging = FirebaseMessaging.instance;
      _localNotifications = FlutterLocalNotificationsPlugin();

      // تهيئة المناطق الزمنية (إذا لزم الأمر لاحقاً)
      // tz.initializeTimeZones();

      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // تهيئة Firebase Messaging
      await _initializeFirebaseMessaging();

      // طلب الأذونات
      await _requestPermissions();

      // إعداد معالجات الإشعارات
      _setupNotificationHandlers();

      AppLogger.info('✅ Notification Service initialized successfully');
    } catch (e, stackTrace) {
      AppLogger.error(
        'Failed to initialize Notification Service',
        category: LogCategory.general,
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// تهيئة الإشعارات المحلية
  Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@drawable/notilogo');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
      requestCriticalPermission: false,
      requestProvisionalPermission: false,
      defaultPresentAlert: true,
      defaultPresentBadge: true,
      defaultPresentSound: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onLocalNotificationTapped,
    );

    // إنشاء قناة الإشعارات للأندرويد
    if (Platform.isAndroid) {
      await _createNotificationChannels();
    }
  }

  /// إنشاء قنوات الإشعارات للأندرويد
  Future<void> _createNotificationChannels() async {
    const channels = [
      AndroidNotificationChannel(
        'general_channel',
        'الإشعارات العامة',
        description: 'إشعارات عامة من التطبيق',
        importance: Importance.high,
        sound: RawResourceAndroidNotificationSound('notification'),
      ),
      AndroidNotificationChannel(
        'appointment_channel',
        'إشعارات المواعيد',
        description: 'إشعارات متعلقة بالمواعيد الطبية',
        importance: Importance.max,
        sound: RawResourceAndroidNotificationSound('notification'),
      ),
      AndroidNotificationChannel(
        'medical_channel',
        'الإشعارات الطبية',
        description: 'إشعارات طبية مهمة',
        importance: Importance.max,
        sound: RawResourceAndroidNotificationSound('notification'),
      ),
    ];

    for (final channel in channels) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.createNotificationChannel(channel);
    }
  }

  /// تهيئة Firebase Messaging
  Future<void> _initializeFirebaseMessaging() async {
    // الحصول على التوكن
    final token = await _firebaseMessaging.getToken();
    if (token != null) {
      await _prefs.setString(_fcmTokenKey, token);
      AppLogger.info('🔑 FCM Token: $token');
    }

    // مراقبة تحديث التوكن
    _firebaseMessaging.onTokenRefresh.listen((newToken) async {
      await _prefs.setString(_fcmTokenKey, newToken);
      AppLogger.info('🔄 FCM Token updated: $newToken');
    });
  }

  /// طلب الأذونات
  Future<void> _requestPermissions() async {
    // أذونات Firebase
    final settings = await _firebaseMessaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
      announcement: false,
      carPlay: false,
      criticalAlert: false,
    );

    AppLogger.info(
      '🔐 Notification permissions: ${settings.authorizationStatus}',
    );

    // أذونات الإشعارات المحلية للأندرويد
    if (Platform.isAndroid) {
      await _localNotifications
          .resolvePlatformSpecificImplementation<
            AndroidFlutterLocalNotificationsPlugin
          >()
          ?.requestNotificationsPermission();
    }
  }

  /// إعداد معالجات الإشعارات
  void _setupNotificationHandlers() {
    // معالج الإشعارات في المقدمة
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);

    // معالج الإشعارات عند النقر (التطبيق في الخلفية)
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);

    // معالج الإشعارات عند فتح التطبيق من إشعار
    _handleInitialMessage();
  }

  /// معالجة الإشعارات في المقدمة
  Future<void> _handleForegroundMessage(RemoteMessage message) async {
    AppLogger.info('📱 Foreground message received: ${message.messageId}');

    final notification = NotificationModel.fromFirebaseMessage(
      message.toMap(),
      messageId: message.messageId,
    );

    // حفظ الإشعار
    await _saveNotification(notification);

    // عرض إشعار محلي
    await _showLocalNotification(notification);

    // استدعاء callback
    onNotificationReceived?.call(notification);
  }

  /// معالجة النقر على الإشعار
  Future<void> _handleNotificationTap(RemoteMessage message) async {
    AppLogger.info('👆 Notification tapped: ${message.messageId}');

    final notification = NotificationModel.fromFirebaseMessage(
      message.toMap(),
      messageId: message.messageId,
    );

    // تحديث حالة القراءة
    await markAsRead(notification.id);

    // استدعاء callback
    onNotificationTapped?.call(notification);
  }

  /// معالجة الإشعار الأولي عند فتح التطبيق
  Future<void> _handleInitialMessage() async {
    final initialMessage = await _firebaseMessaging.getInitialMessage();
    if (initialMessage != null) {
      await _handleNotificationTap(initialMessage);
    }
  }

  /// معالجة النقر على الإشعار المحلي
  void _onLocalNotificationTapped(NotificationResponse response) async {
    if (response.payload != null) {
      try {
        final data = jsonDecode(response.payload!);
        final notification = NotificationModel.fromJson(data);

        // تحديث حالة القراءة
        await markAsRead(notification.id);

        // استدعاء callback
        onNotificationTapped?.call(notification);
      } catch (e) {
        AppLogger.error('Failed to parse notification payload', error: e);
      }
    }
  }

  /// عرض إشعار محلي
  Future<void> _showLocalNotification(NotificationModel notification) async {
    const androidDetails = AndroidNotificationDetails(
      'general_channel',
      'الإشعارات العامة',
      channelDescription: 'إشعارات عامة من التطبيق',
      importance: Importance.high,
      priority: Priority.high,
      icon: '@drawable/notilogo',
      largeIcon: DrawableResourceAndroidBitmap('@drawable/notilogo'),
      color: Color(0xFF2196F3), // خلفية زرقاء للإشعار
      colorized: true,
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
      sound: 'default',
      badgeNumber: null,
      threadIdentifier: 'diet_rx_notifications',
      categoryIdentifier: 'GENERAL',
    );

    const details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      notification.id.hashCode,
      notification.title,
      notification.body,
      details,
      payload: jsonEncode(notification.toJson()),
    );
  }

  /// حفظ الإشعار محلياً
  Future<void> _saveNotification(NotificationModel notification) async {
    try {
      final notifications = await getStoredNotifications();
      notifications.insert(0, notification);

      // الاحتفاظ بآخر 100 إشعار فقط
      if (notifications.length > 100) {
        notifications.removeRange(100, notifications.length);
      }

      final jsonList = notifications.map((n) => n.toJson()).toList();
      await _prefs.setString(_notificationsKey, jsonEncode(jsonList));

      AppLogger.info('💾 Notification saved: ${notification.id}');
    } catch (e) {
      AppLogger.error('Failed to save notification', error: e);
    }
  }

  /// الحصول على الإشعارات المحفوظة
  Future<List<NotificationModel>> getStoredNotifications() async {
    try {
      final jsonString = _prefs.getString(_notificationsKey);
      if (jsonString == null) return [];

      final jsonList = jsonDecode(jsonString) as List;
      return jsonList.map((json) => NotificationModel.fromJson(json)).toList();
    } catch (e) {
      AppLogger.error('Failed to get stored notifications', error: e);
      return [];
    }
  }

  /// تحديد إشعار كمقروء
  Future<void> markAsRead(String notificationId) async {
    try {
      final notifications = await getStoredNotifications();
      final index = notifications.indexWhere((n) => n.id == notificationId);

      if (index != -1) {
        notifications[index] = notifications[index].copyWith(isRead: true);
        final jsonList = notifications.map((n) => n.toJson()).toList();
        await _prefs.setString(_notificationsKey, jsonEncode(jsonList));
      }
    } catch (e) {
      AppLogger.error('Failed to mark notification as read', error: e);
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<void> markAllAsRead() async {
    try {
      final notifications = await getStoredNotifications();
      final updatedNotifications =
          notifications.map((n) => n.copyWith(isRead: true)).toList();

      final jsonList = updatedNotifications.map((n) => n.toJson()).toList();
      await _prefs.setString(_notificationsKey, jsonEncode(jsonList));

      AppLogger.info('✅ All notifications marked as read');
    } catch (e) {
      AppLogger.error('Failed to mark all notifications as read', error: e);
    }
  }

  /// مسح جميع الإشعارات
  Future<void> clearAllNotifications() async {
    try {
      await _prefs.remove(_notificationsKey);
      AppLogger.info('🗑️ All notifications cleared');
    } catch (e) {
      AppLogger.error('Failed to clear notifications', error: e);
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  Future<int> getUnreadCount() async {
    final notifications = await getStoredNotifications();
    return notifications.where((n) => !n.isRead).length;
  }

  /// الحصول على FCM Token
  Future<String?> getFCMToken() async {
    return _prefs.getString(_fcmTokenKey);
  }

  /// طباعة FCM Token في الكونسول
  Future<void> printFCMToken() async {
    final token = await getFCMToken();
    if (token != null) {
      AppLogger.info('🔑 FCM Token: $token');
    } else {
      AppLogger.warning('❌ No FCM Token available');
    }
  }

  /// معالجة الإشعارات في المقدمة
  void handleForegroundMessage(RemoteMessage message) {
    try {
      AppLogger.info(
        'Handling foreground message: ${message.messageId}',
        category: LogCategory.general,
      );

      // عرض إشعار محلي
      _showForegroundNotification(
        title: message.notification?.title ?? 'إشعار جديد',
        body: message.notification?.body ?? '',
        data: message.data,
      );
    } catch (e) {
      AppLogger.error(
        'Error handling foreground message',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// معالجة النقر على الإشعار
  void handleNotificationTap(RemoteMessage message) {
    try {
      AppLogger.info(
        'Handling notification tap: ${message.messageId}',
        category: LogCategory.general,
      );

      // يمكن إضافة منطق التنقل هنا
      if (onNotificationTapped != null) {
        final notification = NotificationModel(
          id: message.messageId ?? '',
          title: message.notification?.title ?? '',
          body: message.notification?.body ?? '',
          type: NotificationType.general,
          isRead: false,
          createdAt: DateTime.now(),
          data: message.data,
        );
        onNotificationTapped!(notification);
      }
    } catch (e) {
      AppLogger.error(
        'Error handling notification tap',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// عرض إشعار محلي للمقدمة
  Future<void> _showForegroundNotification({
    required String title,
    required String body,
    Map<String, dynamic>? data,
  }) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'default_channel',
        'Default Channel',
        channelDescription: 'Default notification channel',
        importance: Importance.high,
        priority: Priority.high,
        showWhen: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch.remainder(100000),
        title,
        body,
        details,
        payload: data != null ? jsonEncode(data) : null,
      );
    } catch (e) {
      AppLogger.error(
        'Error showing local notification',
        category: LogCategory.general,
        error: e,
      );
    }
  }
}

/// معالج الإشعارات في الخلفية
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  AppLogger.info('📱 Background message received: ${message.messageId}');
}
