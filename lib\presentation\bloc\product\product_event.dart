part of 'product_bloc.dart';

/// أحداث المنتجات
abstract class ProductEvent extends Equatable {
  const ProductEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل المنتجات
class LoadProducts extends ProductEvent {
  final String? category;
  final bool? isAvailable;
  final String? searchTerm;
  final int? limit;
  final int? offset;
  final String? sortBy;
  final bool ascending;

  const LoadProducts({
    this.category,
    this.isAvailable,
    this.searchTerm,
    this.limit,
    this.offset,
    this.sortBy,
    this.ascending = true,
  });

  @override
  List<Object?> get props => [category, isAvailable, searchTerm, limit, offset, sortBy, ascending];
}

/// تحميل المنتجات المميزة
class LoadFeaturedProducts extends ProductEvent {
  final int? limit;

  const LoadFeaturedProducts({this.limit});

  @override
  List<Object?> get props => [limit];
}

/// تحميل المنتجات حسب الفئة
class LoadProductsByCategory extends ProductEvent {
  final String category;
  final int? limit;

  const LoadProductsByCategory({
    required this.category,
    this.limit,
  });

  @override
  List<Object?> get props => [category, limit];
}

/// تحميل فئات المنتجات
class LoadProductCategories extends ProductEvent {
  const LoadProductCategories();
}

/// البحث في المنتجات
class SearchProducts extends ProductEvent {
  final String searchTerm;
  final String? category;
  final double? minPrice;
  final double? maxPrice;
  final int? limit;

  const SearchProducts({
    required this.searchTerm,
    this.category,
    this.minPrice,
    this.maxPrice,
    this.limit,
  });

  @override
  List<Object?> get props => [searchTerm, category, minPrice, maxPrice, limit];
}

/// تحميل منتج بواسطة ID
class LoadProductById extends ProductEvent {
  final String productId;

  const LoadProductById({required this.productId});

  @override
  List<Object?> get props => [productId];
}

/// تحميل إحصائيات المنتجات
class LoadProductStats extends ProductEvent {
  const LoadProductStats();
}

/// تحديث المنتجات
class RefreshProducts extends ProductEvent {
  final String? category;
  final bool? isAvailable;
  final String? searchTerm;
  final int? limit;
  final int? offset;
  final String? sortBy;
  final bool ascending;

  const RefreshProducts({
    this.category,
    this.isAvailable,
    this.searchTerm,
    this.limit,
    this.offset,
    this.sortBy,
    this.ascending = true,
  });

  @override
  List<Object?> get props => [category, isAvailable, searchTerm, limit, offset, sortBy, ascending];
}
