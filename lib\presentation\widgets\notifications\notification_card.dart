import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/constants/app_colors.dart';
import '../../../data/models/notification_model.dart';

/// بطاقة الإشعار
class NotificationCard extends StatelessWidget {
  final NotificationModel notification;
  final VoidCallback? onTap;

  const NotificationCard({
    super.key,
    required this.notification,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16.r),
          child: Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: notification.isRead 
                  ? AppColors.white 
                  : AppColors.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(16.r),
              border: Border.all(
                color: notification.isRead 
                    ? AppColors.border 
                    : AppColors.primary.withValues(alpha: 0.2),
                width: notification.isRead ? 1 : 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // أيقونة نوع الإشعار
                _buildNotificationIcon(),
                
                SizedBox(width: 12.w),
                
                // محتوى الإشعار
                Expanded(
                  child: _buildNotificationContent(),
                ),
                
                SizedBox(width: 8.w),
                
                // مؤشر الحالة والوقت
                _buildStatusIndicator(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء أيقونة نوع الإشعار
  Widget _buildNotificationIcon() {
    return Container(
      width: 48.w,
      height: 48.h,
      decoration: BoxDecoration(
        color: _getTypeColor().withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: notification.imageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12.r),
              child: CachedNetworkImage(
                imageUrl: notification.imageUrl!,
                fit: BoxFit.cover,
                placeholder: (context, url) => _buildDefaultIcon(),
                errorWidget: (context, url, error) => _buildDefaultIcon(),
              ),
            )
          : _buildDefaultIcon(),
    );
  }

  /// بناء الأيقونة الافتراضية
  Widget _buildDefaultIcon() {
    return Center(
      child: Text(
        notification.type.icon,
        style: TextStyle(
          fontSize: 20.sp,
        ),
      ),
    );
  }

  /// بناء محتوى الإشعار
  Widget _buildNotificationContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // العنوان
        Text(
          notification.title,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
            color: AppColors.textPrimary,
            height: 1.3,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        
        SizedBox(height: 4.h),
        
        // المحتوى
        Text(
          notification.body,
          style: TextStyle(
            fontSize: 14.sp,
            color: AppColors.textSecondary,
            height: 1.4,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
        
        SizedBox(height: 8.h),
        
        // نوع الإشعار والوقت
        Row(
          children: [
            // نوع الإشعار
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
              decoration: BoxDecoration(
                color: _getTypeColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Text(
                notification.type.displayName,
                style: TextStyle(
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w500,
                  color: _getTypeColor(),
                ),
              ),
            ),
            
            SizedBox(width: 8.w),
            
            // الوقت
            Icon(
              Icons.access_time,
              size: 12.sp,
              color: AppColors.textLight,
            ),
            SizedBox(width: 2.w),
            Text(
              notification.formattedTime,
              style: TextStyle(
                fontSize: 12.sp,
                color: AppColors.textLight,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء مؤشر الحالة
  Widget _buildStatusIndicator() {
    return Column(
      children: [
        // مؤشر عدم القراءة
        if (!notification.isRead)
          Container(
            width: 8.w,
            height: 8.h,
            decoration: BoxDecoration(
              color: AppColors.primary,
              shape: BoxShape.circle,
            ),
          ),
        
        // مؤشر الجديد
        if (notification.isNew && notification.isRead)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: AppColors.secondary,
              borderRadius: BorderRadius.circular(8.r),
            ),
            child: Text(
              'جديد',
              style: TextStyle(
                fontSize: 8.sp,
                fontWeight: FontWeight.w600,
                color: AppColors.white,
              ),
            ),
          ),
      ],
    );
  }

  /// الحصول على لون نوع الإشعار
  Color _getTypeColor() {
    switch (notification.type) {
      case NotificationType.appointment:
        return AppColors.primary;
      case NotificationType.medical:
        return AppColors.error;
      case NotificationType.reminder:
        return AppColors.warning;
      case NotificationType.promotion:
        return AppColors.success;
      case NotificationType.system:
        return AppColors.secondary;
      default:
        return AppColors.textSecondary;
    }
  }
}
