import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/appointment_model.dart';
import '../models/time_slot_model.dart';
import '../models/holiday_model.dart';
import '../models/clinic_info_model.dart';

class AppointmentsService {
  final SupabaseClient _supabase = Supabase.instance.client;

  // Get patient appointments
  Future<List<AppointmentModel>> getPatientAppointments(
    String patientId,
  ) async {
    try {
      print(
        '🔄 [AppointmentsService] Fetching appointments for patient: $patientId',
      );

      // First get appointments
      final appointmentsResponse = await _supabase
          .from('appointments')
          .select('*')
          .eq('patient_id', patientId)
          .order('appointment_date', ascending: false);

      print(
        '📊 [AppointmentsService] Raw response: ${appointmentsResponse.length} appointments',
      );

      if (appointmentsResponse.isEmpty) {
        print('📭 [AppointmentsService] No appointments found for patient');
        return [];
      }

      final appointments = <AppointmentModel>[];

      for (final appointmentJson in appointmentsResponse as List) {
        try {
          print(
            '🔍 [AppointmentsService] Processing appointment: ${appointmentJson['id']}',
          );
          print('📋 [AppointmentsService] Appointment data: $appointmentJson');

          // Get time slot for each appointment
          TimeSlotModel? timeSlot;
          if (appointmentJson['time_slot_id'] != null) {
            try {
              timeSlot = await getTimeSlotById(appointmentJson['time_slot_id']);
              print(
                '⏰ [AppointmentsService] Time slot loaded: ${timeSlot?.displayTimeRange}',
              );
            } catch (e) {
              print(
                '⚠️ [AppointmentsService] Failed to load time slot ${appointmentJson['time_slot_id']}: $e',
              );
              // Continue without time slot
            }
          }

          // Create appointment with time slot
          final appointment = AppointmentModel.fromJson({
            ...appointmentJson,
            'time_slot': timeSlot?.toJson(),
          });

          appointments.add(appointment);
          print(
            '✅ [AppointmentsService] Successfully processed appointment: ${appointment.id}',
          );
        } catch (e) {
          print(
            '❌ [AppointmentsService] Error processing appointment ${appointmentJson['id']}: $e',
          );
          // Continue with next appointment
          continue;
        }
      }

      print(
        '✅ [AppointmentsService] Successfully processed ${appointments.length} appointments',
      );
      return appointments;
    } catch (e) {
      print('❌ [AppointmentsService] Error fetching appointments: $e');
      throw Exception('Failed to fetch appointments: $e');
    }
  }

  // Get available time slots for a specific date
  Future<List<String>> getAvailableTimeSlots(DateTime date) async {
    try {
      // Convert Dart weekday (1=Monday, 7=Sunday) to database format (0=Sunday, 6=Saturday)
      final dayOfWeek = date.weekday == 7 ? 0 : date.weekday;

      // Check if date is a holiday
      final holidayResponse = await _supabase
          .from('holidays')
          .select('*')
          .eq('holiday_date', date.toIso8601String().split('T')[0])
          .eq('is_active', true);

      if (holidayResponse.isNotEmpty) {
        return []; // No slots available on holidays
      }

      // Get time slots for this day of week
      final timeSlotsResponse = await _supabase
          .from('time_slots')
          .select('*')
          .eq('day_of_week', dayOfWeek)
          .eq('is_active', true);

      final timeSlots =
          (timeSlotsResponse as List)
              .map((json) => TimeSlotModel.fromJson(json))
              .toList();

      // Get booked appointments for this date (excluding cancelled)
      final bookedResponse = await _supabase
          .from('appointments')
          .select('time_slot_id')
          .eq('appointment_date', date.toIso8601String().split('T')[0])
          .neq('status', 'cancelled');

      final bookedTimeSlotIds =
          (bookedResponse as List)
              .map((json) => json['time_slot_id'] as String)
              .toSet();

      // Generate available time slots
      final availableSlots = <String>[];
      for (final timeSlot in timeSlots) {
        if (!bookedTimeSlotIds.contains(timeSlot.id)) {
          availableSlots.addAll(timeSlot.generateTimeSlots());
        }
      }

      return availableSlots;
    } catch (e) {
      throw Exception('Failed to fetch available time slots: $e');
    }
  }

  // Get available time slots with their IDs and models for a specific date
  Future<Map<String, Map<String, dynamic>>> getAvailableTimeSlotsWithDetails(
    DateTime date,
  ) async {
    try {
      // Convert Dart weekday (1=Monday, 7=Sunday) to database format (0=Sunday, 6=Saturday)
      final dayOfWeek = date.weekday == 7 ? 0 : date.weekday;
      print(
        '📅 [AppointmentsService] Date: ${date.toIso8601String().split('T')[0]}',
      );
      print('📅 [AppointmentsService] Dart weekday: ${date.weekday}');
      print('📅 [AppointmentsService] Database day_of_week: $dayOfWeek');

      // Check if date is a holiday
      final holidayResponse = await _supabase
          .from('holidays')
          .select('*')
          .eq('holiday_date', date.toIso8601String().split('T')[0])
          .eq('is_active', true);

      if (holidayResponse.isNotEmpty) {
        return {}; // No slots available on holidays
      }

      // Get time slots for this day of week
      final timeSlotsResponse = await _supabase
          .from('time_slots')
          .select('*')
          .eq('day_of_week', dayOfWeek)
          .eq('is_active', true)
          .order('start_time', ascending: true);

      print(
        '🕐 [AppointmentsService] Found ${timeSlotsResponse.length} time slots for day $dayOfWeek',
      );

      final timeSlots =
          (timeSlotsResponse as List)
              .map((json) => TimeSlotModel.fromJson(json))
              .toList();

      // Get booked appointments for this date (excluding cancelled)
      final bookedResponse = await _supabase
          .from('appointments')
          .select('time_slot_id')
          .eq('appointment_date', date.toIso8601String().split('T')[0])
          .neq('status', 'cancelled');

      final bookedTimeSlotIds =
          (bookedResponse as List)
              .map((json) => json['time_slot_id'] as String)
              .toSet();

      // Return available time slots with complete details
      final availableSlots =
          <
            String,
            Map<String, dynamic>
          >{}; // displayTime -> {timeSlotId, timeSlotModel}
      for (final timeSlot in timeSlots) {
        print(
          '🔍 [AppointmentsService] Processing time slot: ${timeSlot.id} (${timeSlot.startTime}-${timeSlot.endTime})',
        );
        if (!bookedTimeSlotIds.contains(timeSlot.id)) {
          // Use the complete time range as display text
          final displayTime = timeSlot.displayTimeRange12Hour;
          availableSlots[displayTime] = {
            'timeSlotId': timeSlot.id,
            'timeSlotModel': timeSlot,
          };
          print('✅ [AppointmentsService] Available slot: $displayTime');
        } else {
          print(
            '❌ [AppointmentsService] Time slot ${timeSlot.id} is already booked',
          );
        }
      }

      print(
        '✅ [AppointmentsService] Final available slots: ${availableSlots.keys.toList()}',
      );
      return availableSlots;
    } catch (e) {
      throw Exception('Failed to fetch available time slots: $e');
    }
  }

  // Book an appointment
  Future<AppointmentModel> bookAppointment({
    required String patientId,
    required DateTime appointmentDate,
    required String timeSlotId,
    String? notes,
  }) async {
    try {
      final response =
          await _supabase
              .from('appointments')
              .insert({
                'patient_id': patientId,
                'appointment_date':
                    appointmentDate.toIso8601String().split('T')[0],
                'time_slot_id': timeSlotId,
                'status': 'booked',
                'notes': notes,
              })
              .select('''
            *,
            time_slot:time_slots(*)
          ''')
              .single();

      return AppointmentModel.fromJson(response);
    } catch (e) {
      throw Exception('Failed to book appointment: $e');
    }
  }

  // Cancel an appointment
  Future<void> cancelAppointment(String appointmentId) async {
    try {
      await _supabase
          .from('appointments')
          .update({'status': 'cancelled'})
          .eq('id', appointmentId);
    } catch (e) {
      throw Exception('Failed to cancel appointment: $e');
    }
  }

  // Get holidays
  Future<List<HolidayModel>> getHolidays() async {
    try {
      final response = await _supabase
          .from('holidays')
          .select('*')
          .eq('is_active', true)
          .order('holiday_date', ascending: true);

      return (response as List)
          .map((json) => HolidayModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch holidays: $e');
    }
  }

  // Get clinic info
  Future<List<ClinicInfoModel>> getClinicInfo() async {
    try {
      final response = await _supabase
          .from('clinic_info')
          .select('*')
          .eq('is_active', true)
          .order('display_order', ascending: true);

      return (response as List)
          .map((json) => ClinicInfoModel.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch clinic info: $e');
    }
  }

  // Get time slot by ID
  Future<TimeSlotModel?> getTimeSlotById(String timeSlotId) async {
    try {
      final response =
          await _supabase
              .from('time_slots')
              .select('*')
              .eq('id', timeSlotId)
              .single();

      return TimeSlotModel.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // Check if date is holiday
  Future<HolidayModel?> checkHoliday(DateTime date) async {
    try {
      final response = await _supabase
          .from('holidays')
          .select('*')
          .eq('holiday_date', date.toIso8601String().split('T')[0])
          .eq('is_active', true);

      if (response.isNotEmpty) {
        return HolidayModel.fromJson(response.first);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
