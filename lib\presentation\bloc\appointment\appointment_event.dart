part of 'appointment_bloc.dart';

/// أحداث المواعيد
abstract class AppointmentEvent extends Equatable {
  const AppointmentEvent();

  @override
  List<Object?> get props => [];
}

/// تحميل المواعيد
class LoadAppointments extends AppointmentEvent {
  final String patientId;
  final String? status;
  final DateTime? fromDate;
  final DateTime? toDate;
  final int? limit;
  final int? offset;

  const LoadAppointments({
    required this.patientId,
    this.status,
    this.fromDate,
    this.toDate,
    this.limit,
    this.offset,
  });

  @override
  List<Object?> get props => [patientId, status, fromDate, toDate, limit, offset];
}

/// إنشاء موعد جديد
class CreateAppointment extends AppointmentEvent {
  final String patientId;
  final DateTime appointmentDate;
  final String timeSlot;
  final String type;
  final String? notes;
  final String? doctorId;

  const CreateAppointment({
    required this.patientId,
    required this.appointmentDate,
    required this.timeSlot,
    required this.type,
    this.notes,
    this.doctorId,
  });

  @override
  List<Object?> get props => [patientId, appointmentDate, timeSlot, type, notes, doctorId];
}

/// تحديث موعد
class UpdateAppointment extends AppointmentEvent {
  final String appointmentId;
  final DateTime? appointmentDate;
  final String? timeSlot;
  final String? type;
  final String? status;
  final String? notes;
  final String? doctorId;

  const UpdateAppointment({
    required this.appointmentId,
    this.appointmentDate,
    this.timeSlot,
    this.type,
    this.status,
    this.notes,
    this.doctorId,
  });

  @override
  List<Object?> get props => [appointmentId, appointmentDate, timeSlot, type, status, notes, doctorId];
}

/// إلغاء موعد
class CancelAppointment extends AppointmentEvent {
  final String appointmentId;

  const CancelAppointment({required this.appointmentId});

  @override
  List<Object?> get props => [appointmentId];
}

/// حذف موعد
class DeleteAppointment extends AppointmentEvent {
  final String appointmentId;

  const DeleteAppointment({required this.appointmentId});

  @override
  List<Object?> get props => [appointmentId];
}

/// تحميل الأوقات المتاحة
class LoadAvailableTimeSlots extends AppointmentEvent {
  final DateTime date;

  const LoadAvailableTimeSlots({required this.date});

  @override
  List<Object?> get props => [date];
}

/// البحث في المواعيد
class SearchAppointments extends AppointmentEvent {
  final String patientId;
  final String? searchTerm;
  final String? status;
  final String? type;

  const SearchAppointments({
    required this.patientId,
    this.searchTerm,
    this.status,
    this.type,
  });

  @override
  List<Object?> get props => [patientId, searchTerm, status, type];
}

/// تحميل إحصائيات المواعيد
class LoadAppointmentStats extends AppointmentEvent {
  final String patientId;

  const LoadAppointmentStats({required this.patientId});

  @override
  List<Object?> get props => [patientId];
}

/// تحديث المواعيد
class RefreshAppointments extends AppointmentEvent {
  final String patientId;
  final String? status;
  final DateTime? fromDate;
  final DateTime? toDate;
  final int? limit;
  final int? offset;

  const RefreshAppointments({
    required this.patientId,
    this.status,
    this.fromDate,
    this.toDate,
    this.limit,
    this.offset,
  });

  @override
  List<Object?> get props => [patientId, status, fromDate, toDate, limit, offset];
}
