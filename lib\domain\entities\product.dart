import 'package:equatable/equatable.dart';

/// كيان صورة المنتج
class ProductImage extends Equatable {
  final String id;
  final String imageUrl;
  final String? imagePath;
  final bool isPrimary;
  final int sortOrder;
  final DateTime? createdAt;

  const ProductImage({
    required this.id,
    required this.imageUrl,
    this.imagePath,
    this.isPrimary = false,
    this.sortOrder = 0,
    this.createdAt,
  });

  @override
  List<Object?> get props => [id, imageUrl, imagePath, isPrimary, sortOrder, createdAt];

  /// تحويل من JSON
  factory ProductImage.fromJson(Map<String, dynamic> json) {
    return ProductImage(
      id: json['id'] ?? '',
      imageUrl: json['image_url'] ?? '',
      imagePath: json['image_path'],
      isPrimary: json['is_primary'] ?? false,
      sortOrder: json['sort_order'] ?? 0,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'image_url': imageUrl,
      'image_path': imagePath,
      'is_primary': isPrimary,
      'sort_order': sortOrder,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}

/// كيان المنتج
class Product extends Equatable {
  final String id;
  final String name;
  final String? description;
  final double? price;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? categoryId;
  final String? categoryName;
  final List<ProductImage> images;
  final int stock;
  final String productCode;
  final double discountPercentage;

  const Product({
    required this.id,
    required this.name,
    this.description,
    this.price,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.categoryId,
    this.categoryName,
    this.images = const [],
    this.stock = 0,
    required this.productCode,
    this.discountPercentage = 0,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        price,
        isActive,
        createdAt,
        updatedAt,
        categoryId,
        images,
        stock,
        productCode,
        discountPercentage,
      ];

  /// التحقق من توفر المنتج
  bool get isAvailable => isActive && stock > 0;

  /// التحقق من نفاد المخزون
  bool get isOutOfStock => stock <= 0;

  /// التحقق من وجود خصم
  bool get hasDiscount => discountPercentage > 0;

  /// حساب السعر بعد الخصم
  double? get discountedPrice {
    if (price == null || !hasDiscount) return price;
    return price! * (1 - discountPercentage / 100);
  }

  /// حساب مبلغ الخصم
  double? get discountAmount {
    if (price == null || !hasDiscount) return 0;
    return price! * (discountPercentage / 100);
  }

  /// الحصول على الصورة الأولى
  String? get primaryImage {
    if (images.isEmpty) return null;
    final primaryImg = images.firstWhere(
      (img) => img.isPrimary,
      orElse: () => images.first,
    );
    return primaryImg.imageUrl;
  }

  /// التحقق من وجود صور
  bool get hasImages => images.isNotEmpty;

  /// التحقق من وجود وصف
  bool get hasDescription => description != null && description!.isNotEmpty;

  /// حالة المخزون
  String get stockStatus {
    if (stock <= 0) {
      return 'غير متوفر';
    } else if (stock <= 5) {
      return 'كمية محدودة';
    } else {
      return 'متوفر';
    }
  }

  /// لون حالة المخزون
  String get stockStatusColor {
    if (stock <= 0) {
      return 'red';
    } else if (stock <= 5) {
      return 'orange';
    } else {
      return 'green';
    }
  }

  /// معالجة الصور من JSON
  static List<ProductImage> _parseImages(Map<String, dynamic> json) {
    if (json['images'] != null && json['images'] is List) {
      return (json['images'] as List)
          .map((img) => ProductImage.fromJson(img as Map<String, dynamic>))
          .toList();
    } else if (json['image_url'] != null) {
      return [
        ProductImage(
          id: '${json['id']}_0',
          imageUrl: json['image_url'].toString(),
          isPrimary: true,
        )
      ];
    }
    return [];
  }

  /// إنشاء Product من JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    return Product(
      id: json['id']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      description: json['description']?.toString(),
      price: json['price'] != null ? double.tryParse(json['price'].toString()) : null,
      isActive: json['is_available'] == true || json['is_active'] == true,
      createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at'].toString()) : null,
      updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at'].toString()) : null,
      categoryId: json['category_id']?.toString() ?? json['category']?.toString(),
      categoryName: json['category_name']?.toString(),
      images: _parseImages(json),
      stock: json['stock_quantity'] != null ? int.tryParse(json['stock_quantity'].toString()) ?? 0 :
             json['stock'] != null ? int.tryParse(json['stock'].toString()) ?? 0 : 0,
      productCode: json['product_code']?.toString() ?? json['id']?.toString() ?? '',
      discountPercentage: json['discount_percentage'] != null ?
                         double.tryParse(json['discount_percentage'].toString()) ?? 0 : 0,
    );
  }

  /// تحويل Product إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'price': price,
      'is_available': isActive,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'category_id': categoryId,
      'category': categoryId,
      'images': images,
      'image_url': primaryImage,
      'stock_quantity': stock,
      'stock': stock,
      'product_code': productCode,
      'discount_percentage': discountPercentage,
    };
  }

  /// إنشاء نسخة محدثة من Product
  Product copyWith({
    String? id,
    String? name,
    String? description,
    double? price,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? categoryId,
    String? categoryName,
    List<ProductImage>? images,
    int? stock,
    String? productCode,
    double? discountPercentage,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      price: price ?? this.price,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      categoryId: categoryId ?? this.categoryId,
      categoryName: categoryName ?? this.categoryName,
      images: images ?? this.images,
      stock: stock ?? this.stock,
      productCode: productCode ?? this.productCode,
      discountPercentage: discountPercentage ?? this.discountPercentage,
    );
  }
}
