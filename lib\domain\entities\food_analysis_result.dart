import 'package:equatable/equatable.dart';

/// كيان نتيجة تحليل الطعام
class FoodAnalysisResult extends Equatable {
  final String foodName;
  final double calories;
  final double protein;
  final double carbs;
  final double fats;
  final double saturatedFats;
  final double unsaturatedFats;
  final double sugars;
  final double addedSugars;
  final double fiber;
  final double sodium;
  final String servingSize;
  final List<String> ingredients;
  final String healthNotes;
  final String recommendations;

  const FoodAnalysisResult({
    required this.foodName,
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fats,
    required this.saturatedFats,
    required this.unsaturatedFats,
    required this.sugars,
    required this.addedSugars,
    required this.fiber,
    required this.sodium,
    required this.servingSize,
    required this.ingredients,
    required this.healthNotes,
    required this.recommendations,
  });

  @override
  List<Object?> get props => [
        foodName,
        calories,
        protein,
        carbs,
        fats,
        saturatedFats,
        unsaturatedFats,
        sugars,
        addedSugars,
        fiber,
        sodium,
        servingSize,
        ingredients,
        healthNotes,
        recommendations,
      ];

  factory FoodAnalysisResult.fromJson(Map<String, dynamic> json) {
    return FoodAnalysisResult(
      foodName: json['food_name'] ?? '',
      calories: (json['calories'] ?? 0).toDouble(),
      protein: (json['protein'] ?? 0).toDouble(),
      carbs: (json['carbs'] ?? 0).toDouble(),
      fats: (json['fats'] ?? 0).toDouble(),
      saturatedFats: (json['saturated_fats'] ?? 0).toDouble(),
      unsaturatedFats: (json['unsaturated_fats'] ?? 0).toDouble(),
      sugars: (json['sugars'] ?? 0).toDouble(),
      addedSugars: (json['added_sugars'] ?? 0).toDouble(),
      fiber: (json['fiber'] ?? 0).toDouble(),
      sodium: (json['sodium'] ?? 0).toDouble(),
      servingSize: json['serving_size'] ?? '',
      ingredients: List<String>.from(json['ingredients'] ?? []),
      healthNotes: json['health_notes'] ?? '',
      recommendations: json['recommendations'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'food_name': foodName,
      'calories': calories,
      'protein': protein,
      'carbs': carbs,
      'fats': fats,
      'saturated_fats': saturatedFats,
      'unsaturated_fats': unsaturatedFats,
      'sugars': sugars,
      'added_sugars': addedSugars,
      'fiber': fiber,
      'sodium': sodium,
      'serving_size': servingSize,
      'ingredients': ingredients,
      'health_notes': healthNotes,
      'recommendations': recommendations,
    };
  }

  /// كيان المعلومات الغذائية للاستخدام في الواجهة
  NutritionInfo get nutritionInfo => NutritionInfo(
        calories: calories,
        protein: protein,
        carbs: carbs,
        fat: fats,
        sugars: sugars,
        fiber: fiber,
        sodium: sodium,
      );

  /// حساب السعرات من البروتين
  double get proteinCalories => protein * 4;

  /// حساب السعرات من الكربوهيدرات
  double get carbsCalories => carbs * 4;

  /// حساب السعرات من الدهون
  double get fatsCalories => fats * 9;

  /// التحقق من كون الطعام مناسب للحمية وإنقاص الوزن
  bool get isHealthyForWeightLoss {
    // معايير مخصصة للحمية وإنقاص الوزن
    return calories < 300 && // سعرات قليلة
           fats < 8 && // دهون قليلة
           sugars < 10 && // سكريات قليلة
           sodium < 500 && // صوديوم قليل
           fiber > 2 && // ألياف جيدة للشبع
           protein > 5; // بروتين كافي للعضلات
  }

  /// تقييم الطعام بالنجوم (1-5) مع التركيز على إنقاص الوزن
  int get healthRating {
    int score = 5;

    // تقييم السعرات الحرارية (الأهم في إنقاص الوزن)
    if (calories > 400) score--;
    if (calories > 600) score--;

    // تقييم الدهون
    if (fats > 10) score--;
    if (fats > 20) score--;

    // تقييم السكريات
    if (sugars > 15) score--;
    if (sugars > 25) score--;

    // تقييم الصوديوم (يسبب احتباس الماء)
    if (sodium > 600) score--;

    // مكافأة للألياف والبروتين
    if (fiber > 5) score = (score + 1).clamp(1, 5);
    if (protein > 15) score = (score + 1).clamp(1, 5);

    return score.clamp(1, 5);
  }

  /// تقييم الطعام النصي مع التركيز على الحمية
  String get healthRatingText {
    final rating = healthRating;
    if (rating >= 4) return 'ممتاز للحمية';
    if (rating >= 3) return 'جيد للحمية';
    if (rating >= 2) return 'مقبول بكميات قليلة';
    return 'تجنبه في الحمية';
  }

  /// نصائح مخصصة للحمية
  String get dietAdvice {
    final rating = healthRating;
    if (rating >= 4) {
      return 'هذا الطعام ممتاز لحميتك! يمكنك تناوله بانتظام ضمن خطة إنقاص الوزن.';
    } else if (rating >= 3) {
      return 'طعام جيد للحمية، يمكن تناوله باعتدال مع ممارسة الرياضة.';
    } else if (rating >= 2) {
      return 'يمكن تناوله أحياناً بكميات صغيرة، لكن ليس الخيار الأفضل للحمية.';
    } else {
      return 'يُفضل تجنب هذا الطعام أثناء الحمية أو تناوله نادراً جداً.';
    }
  }
}

/// كيان المعلومات الغذائية
class NutritionInfo extends Equatable {
  final double calories;
  final double protein;
  final double carbs;
  final double fat;
  final double sugars;
  final double fiber;
  final double sodium;

  const NutritionInfo({
    required this.calories,
    required this.protein,
    required this.carbs,
    required this.fat,
    required this.sugars,
    required this.fiber,
    required this.sodium,
  });

  @override
  List<Object?> get props => [
        calories,
        protein,
        carbs,
        fat,
        sugars,
        fiber,
        sodium,
      ];
}
