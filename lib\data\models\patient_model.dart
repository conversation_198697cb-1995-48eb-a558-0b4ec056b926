class PatientModel {
  final String id;
  final String authId;
  final String name;
  final String? email;
  final String? phone;
  final int? age;
  final String? gender;
  final double? height;
  final double? weight;
  final DateTime? birthDate;
  final bool isPremium;
  final String? medicalConditions;
  final String? allergies;
  final String? medications;
  final String? supplements;
  final String? physicalActivity;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const PatientModel({
    required this.id,
    required this.authId,
    required this.name,
    this.email,
    this.phone,
    this.age,
    this.gender,
    this.height,
    this.weight,
    this.birthDate,
    this.isPremium = false,
    this.medicalConditions,
    this.allergies,
    this.medications,
    this.supplements,
    this.physicalActivity,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PatientModel.fromMap(Map<String, dynamic> map) {
    return PatientModel(
      id: map['id'] as String,
      authId: map['auth_id'] as String,
      name: map['name'] as String,
      email: map['email'] as String?,
      phone: map['phone'] as String?,
      age: map['age'] as int?,
      gender: map['gender'] as String?,
      height: map['height'] != null ? (map['height'] as num).toDouble() : null,
      weight: map['weight'] != null ? (map['weight'] as num).toDouble() : null,
      birthDate: map['birth_date'] != null ? DateTime.parse(map['birth_date']) : null,
      isPremium: map['is_premium'] as bool? ?? false,
      medicalConditions: map['medical_conditions'] as String?,
      allergies: map['allergies'] as String?,
      medications: map['medications'] as String?,
      supplements: map['supplements'] as String?,
      physicalActivity: map['physical_activity'] as String?,
      notes: map['notes'] as String?,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'auth_id': authId,
      'name': name,
      'email': email,
      'phone': phone,
      'age': age,
      'gender': gender,
      'height': height,
      'weight': weight,
      'birth_date': birthDate?.toIso8601String().split('T')[0],
      'is_premium': isPremium,
      'medical_conditions': medicalConditions,
      'allergies': allergies,
      'medications': medications,
      'supplements': supplements,
      'physical_activity': physicalActivity,
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  PatientModel copyWith({
    String? id,
    String? authId,
    String? name,
    String? email,
    String? phone,
    int? age,
    String? gender,
    double? height,
    double? weight,
    DateTime? birthDate,
    bool? isPremium,
    String? medicalConditions,
    String? allergies,
    String? medications,
    String? supplements,
    String? physicalActivity,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PatientModel(
      id: id ?? this.id,
      authId: authId ?? this.authId,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      birthDate: birthDate ?? this.birthDate,
      isPremium: isPremium ?? this.isPremium,
      medicalConditions: medicalConditions ?? this.medicalConditions,
      allergies: allergies ?? this.allergies,
      medications: medications ?? this.medications,
      supplements: supplements ?? this.supplements,
      physicalActivity: physicalActivity ?? this.physicalActivity,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PatientModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PatientModel(id: $id, name: $name, email: $email)';
  }

  // Helper methods
  String get displayName => name;
  String get displayGender => gender ?? 'غير محدد';
  String get displayAge => age != null ? '$age سنة' : 'غير محدد';
  String get displayHeight => height != null ? '${height!.toStringAsFixed(1)} سم' : 'غير محدد';
  String get displayWeight => weight != null ? '${weight!.toStringAsFixed(1)} كجم' : 'غير محدد';
  String get displayBirthDate => birthDate != null ? '${birthDate!.day}/${birthDate!.month}/${birthDate!.year}' : 'غير محدد';
  
  double? get bmi {
    if (height != null && weight != null && height! > 0) {
      final heightInMeters = height! / 100;
      return weight! / (heightInMeters * heightInMeters);
    }
    return null;
  }
  
  String get bmiCategory {
    final bmiValue = bmi;
    if (bmiValue == null) return 'غير محدد';
    
    if (bmiValue < 18.5) return 'نقص في الوزن';
    if (bmiValue < 25) return 'وزن طبيعي';
    if (bmiValue < 30) return 'زيادة في الوزن';
    return 'سمنة';
  }
}
