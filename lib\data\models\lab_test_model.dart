class LabTestModel {
  final String id;
  final String patientId;
  final String testName;
  final DateTime testDate;
  final String? imageUrl;
  final String? notes;
  final String? testType;
  final String? results;
  final String? doctorNotes;
  final bool? isNormal;
  final DateTime createdAt;
  final DateTime updatedAt;

  const LabTestModel({
    required this.id,
    required this.patientId,
    required this.testName,
    required this.testDate,
    this.imageUrl,
    this.notes,
    this.testType,
    this.results,
    this.doctorNotes,
    this.isNormal,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LabTestModel.fromMap(Map<String, dynamic> map) {
    return LabTestModel(
      id: map['id'] as String,
      patientId: map['patient_id'] as String,
      testName: map['test_name'] as String,
      testDate: DateTime.parse(map['test_date']),
      imageUrl: map['image_url'] as String?,
      notes: map['notes'] as String?,
      testType: map['test_type'] as String?,
      results: map['results'] as String?,
      doctorNotes: map['doctor_notes'] as String?,
      isNormal: map['is_normal'] as bool?,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'patient_id': patientId,
      'test_name': testName,
      'test_date': testDate.toIso8601String().split('T')[0],
      'image_url': imageUrl,
      'notes': notes,
      'test_type': testType,
      'results': results,
      'doctor_notes': doctorNotes,
      'is_normal': isNormal,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  LabTestModel copyWith({
    String? id,
    String? patientId,
    String? testName,
    DateTime? testDate,
    String? imageUrl,
    String? notes,
    String? testType,
    String? results,
    String? doctorNotes,
    bool? isNormal,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LabTestModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      testName: testName ?? this.testName,
      testDate: testDate ?? this.testDate,
      imageUrl: imageUrl ?? this.imageUrl,
      notes: notes ?? this.notes,
      testType: testType ?? this.testType,
      results: results ?? this.results,
      doctorNotes: doctorNotes ?? this.doctorNotes,
      isNormal: isNormal ?? this.isNormal,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LabTestModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'LabTestModel(id: $id, testName: $testName, testDate: $testDate, isNormal: $isNormal)';
  }

  // Helper methods
  String get displayTestDate =>
      '${testDate.day}/${testDate.month}/${testDate.year}';

  String get displayTestType {
    if (testType == null) return 'غير محدد';
    switch (testType!.toLowerCase()) {
      case 'blood':
        return 'فحص دم';
      case 'urine':
        return 'فحص بول';
      case 'stool':
        return 'فحص براز';
      case 'xray':
        return 'أشعة سينية';
      case 'ct':
        return 'أشعة مقطعية';
      case 'mri':
        return 'رنين مغناطيسي';
      case 'ultrasound':
        return 'موجات فوق صوتية';
      case 'ecg':
        return 'رسم قلب';
      case 'echo':
        return 'إيكو القلب';
      case 'endoscopy':
        return 'منظار';
      case 'biopsy':
        return 'خزعة';
      case 'culture':
        return 'مزرعة';
      case 'hormone':
        return 'فحص هرمونات';
      case 'vitamin':
        return 'فحص فيتامينات';
      case 'lipid':
        return 'فحص دهون';
      case 'liver':
        return 'فحص كبد';
      case 'kidney':
        return 'فحص كلى';
      case 'thyroid':
        return 'فحص غدة درقية';
      case 'diabetes':
        return 'فحص سكري';
      default:
        return testType!;
    }
  }

  String get statusText {
    if (isNormal == null) return 'في انتظار النتائج';
    return isNormal! ? 'طبيعي' : 'غير طبيعي';
  }

  String get statusColor {
    if (isNormal == null) return '#FFC107'; // Amber
    return isNormal! ? '#4CAF50' : '#F44336'; // Green or Red
  }

  bool get hasImage => imageUrl != null && imageUrl!.isNotEmpty;
  bool get hasResults => results != null && results!.isNotEmpty;
  bool get hasDoctorNotes => doctorNotes != null && doctorNotes!.isNotEmpty;

  // Calculate days since test
  int get daysSinceTest {
    return DateTime.now().difference(testDate).inDays;
  }

  String get timeSinceTest {
    final days = daysSinceTest;
    if (days == 0) return 'اليوم';
    if (days == 1) return 'أمس';
    if (days < 7) return 'منذ $days أيام';
    if (days < 30) return 'منذ ${(days / 7).round()} أسابيع';
    if (days < 365) return 'منذ ${(days / 30).round()} أشهر';
    return 'منذ ${(days / 365).round()} سنوات';
  }

  // Priority based on abnormal results and recency
  int get priority {
    int score = 0;

    // Recent tests get higher priority
    if (daysSinceTest <= 7) {
      score += 3;
    } else if (daysSinceTest <= 30) {
      score += 2;
    } else if (daysSinceTest <= 90) {
      score += 1;
    }

    // Abnormal results get higher priority
    if (isNormal == false) {
      score += 5;
    } else if (isNormal == null) {
      score += 2;
    }

    return score;
  }

  // Get icon based on test type
  String get iconName {
    switch (testType?.toLowerCase()) {
      case 'blood':
        return 'water_drop';
      case 'urine':
        return 'science';
      case 'xray':
      case 'ct':
      case 'mri':
        return 'medical_services';
      case 'ultrasound':
        return 'monitor_heart';
      case 'ecg':
      case 'echo':
        return 'favorite';
      case 'endoscopy':
        return 'visibility';
      default:
        return 'biotech';
    }
  }
}
