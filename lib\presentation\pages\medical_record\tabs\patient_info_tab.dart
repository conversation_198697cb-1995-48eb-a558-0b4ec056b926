import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../data/models/patient_model.dart';

class PatientInfoTab extends StatelessWidget {
  final PatientModel? patient;

  const PatientInfoTab({super.key, required this.patient});

  @override
  Widget build(BuildContext context) {
    if (patient == null) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPersonalInfoCard(),
          <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
          _buildPhysicalInfoCard(),
          SizedBox(height: 16.h),
          _buildContactInfoCard(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.person_outline,
            size: 80.sp,
            color: AppColors.textSecondary,
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 16.h),
          Text(
            'لا توجد معلومات شخصية',
            style: TextStyle(fontSize: 18.sp, color: AppColors.textSecondary),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.person, color: AppColors.primary, size: 24.sp),
                SizedBox(width: 8.w),
                Text(
                  'المعلومات الشخصية',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildInfoRow('الاسم', patient!.displayName),
            _buildInfoRow('العمر', patient!.displayAge),
            _buildInfoRow('الجنس', patient!.displayGender),
            _buildInfoRow('تاريخ الميلاد', patient!.displayBirthDate),
            if (patient!.isPremium)
              Container(
                margin: EdgeInsets.only(top: 12.h),
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: Colors.amber.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20.r),
                  border: Border.all(color: Colors.amber),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.star, color: Colors.amber, size: 16.sp),
                    SizedBox(width: 4.w),
                    Text(
                      'عضو مميز',
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber[800],
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhysicalInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.monitor_weight,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  'المعلومات الجسدية',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            _buildInfoRow('الطول', patient!.displayHeight),
            _buildInfoRow('الوزن', patient!.displayWeight),
            if (patient!.bmi != null) ...[
              _buildInfoRow(
                'مؤشر كتلة الجسم',
                patient!.bmi!.toStringAsFixed(1),
              ),
              _buildBMIIndicator(),
              SizedBox(height: 12.h),
              _buildWeightAnalysis(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildContactInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.r)),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.contact_phone,
                  color: AppColors.primary,
                  size: 24.sp,
                ),
                SizedBox(width: 8.w),
                Text(
                  'معلومات الاتصال',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
              ],
            ),
            SizedBox(height: 16.h),
            if (patient!.email != null && patient!.email!.isNotEmpty)
              _buildInfoRow('البريد الإلكتروني', patient!.email!),
            if (patient!.phone != null && patient!.phone!.isNotEmpty)
              _buildInfoRow('رقم الهاتف', patient!.phone!),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 12.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100.w,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: AppColors.textPrimary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBMIIndicator() {
    final bmi = patient!.bmi!;
    final category = patient!.bmiCategory;

    Color indicatorColor;
    if (bmi < 18.5) {
      indicatorColor = Colors.blue;
    } else if (bmi < 25) {
      indicatorColor = Colors.green;
    } else if (bmi < 30) {
      indicatorColor = Colors.orange;
    } else {
      indicatorColor = Colors.red;
    }

    return Container(
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: indicatorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: indicatorColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: indicatorColor, size: 16.sp),
          SizedBox(width: 8.w),
          Text(
            category,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: indicatorColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWeightAnalysis() {
    if (patient!.height == null || patient!.weight == null) {
      return const SizedBox.shrink();
    }

    final heightInMeters = patient!.height! / 100;
    final currentWeight = patient!.weight!;

    // حساب الوزن المثالي (BMI = 22.5)
    final idealWeight = 22.5 * heightInMeters * heightInMeters;

    // حساب النطاق الطبيعي للوزن (BMI 18.5-24.9)
    final minNormalWeight = 18.5 * heightInMeters * heightInMeters;
    final maxNormalWeight = 24.9 * heightInMeters * heightInMeters;

    // حساب الفرق مع الوزن المثالي
    final weightDifference = currentWeight - idealWeight;

    return Container(
      margin: EdgeInsets.only(top: 8.h),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: AppColors.primary, size: 18.sp),
              SizedBox(width: 8.w),
              Text(
                'تحليل الوزن',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          _buildWeightInfoRow(
            'الوزن المثالي',
            '${idealWeight.toStringAsFixed(1)} كجم',
            Icons.gps_fixed,
            Colors.green,
          ),
          _buildWeightInfoRow(
            'النطاق الطبيعي',
            '${minNormalWeight.toStringAsFixed(1)} - ${maxNormalWeight.toStringAsFixed(1)} كجم',
            Icons.straighten,
            Colors.blue,
          ),
          _buildWeightInfoRow(
            weightDifference >= 0 ? 'الزيادة في الوزن' : 'النقص في الوزن',
            '${weightDifference.abs().toStringAsFixed(1)} كجم',
            weightDifference >= 0 ? Icons.trending_up : Icons.trending_down,
            weightDifference >= 0 ? Colors.orange : Colors.blue,
          ),
        ],
      ),
    );
  }

  Widget _buildWeightInfoRow(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16.sp),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13.sp,
                fontWeight: FontWeight.w500,
                color: AppColors.textSecondary,
              ),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 13.sp,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
