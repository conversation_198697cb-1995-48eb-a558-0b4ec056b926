import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// مستويات السجل
enum LogLevel {
  debug,
  info,
  warning,
  error,
  critical,
}

/// فئات السجل
enum LogCategory {
  auth,
  api,
  database,
  ui,
  navigation,
  bloc,
  general,
}

/// مسجل التطبيق المخصص
class AppLogger {
  static bool _isEnabled = kDebugMode;
  static LogLevel _minimumLevel = LogLevel.debug;

  /// تفعيل/إلغاء تفعيل السجل
  static void setEnabled(bool enabled) {
    _isEnabled = enabled;
  }

  /// تعيين الحد الأدنى لمستوى السجل
  static void setMinimumLevel(LogLevel level) {
    _minimumLevel = level;
  }

  /// تسجيل رسالة debug
  static void debug(String message, {
    LogCategory category = LogCategory.general,
    Map<String, dynamic>? data,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.debug, message, category, data, error, stackTrace);
  }

  /// تسجيل رسالة info
  static void info(String message, {
    LogCategory category = LogCategory.general,
    Map<String, dynamic>? data,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.info, message, category, data, error, stackTrace);
  }

  /// تسجيل رسالة warning
  static void warning(String message, {
    LogCategory category = LogCategory.general,
    Map<String, dynamic>? data,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.warning, message, category, data, error, stackTrace);
  }

  /// تسجيل رسالة error
  static void error(String message, {
    LogCategory category = LogCategory.general,
    Map<String, dynamic>? data,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.error, message, category, data, error, stackTrace);
  }

  /// تسجيل رسالة critical
  static void critical(String message, {
    LogCategory category = LogCategory.general,
    Map<String, dynamic>? data,
    Object? error,
    StackTrace? stackTrace,
  }) {
    _log(LogLevel.critical, message, category, data, error, stackTrace);
  }

  /// الدالة الأساسية للتسجيل
  static void _log(
    LogLevel level,
    String message,
    LogCategory category,
    Map<String, dynamic>? data,
    Object? error,
    StackTrace? stackTrace,
  ) {
    if (!_isEnabled || level.index < _minimumLevel.index) {
      return;
    }

    final timestamp = DateTime.now().toIso8601String();
    final levelName = level.name.toUpperCase();
    final categoryName = category.name.toUpperCase();
    final emoji = _getLevelEmoji(level);
    final color = _getLevelColor(level);

    // تنسيق الرسالة
    final formattedMessage = _formatMessage(
      emoji,
      levelName,
      categoryName,
      message,
      timestamp,
      data,
      error,
    );

    // طباعة ملونة في الكونسول (فقط في وضع التطوير)
    if (kDebugMode) {
      print('$color$formattedMessage\x1B[0m');
    }

    // تسجيل في developer log
    developer.log(
      formattedMessage,
      name: 'AppLogger',
      level: _getDeveloperLogLevel(level),
      error: error,
      stackTrace: stackTrace,
    );

    // إرسال للخدمات الخارجية في حالة الأخطاء الحرجة
    if (level == LogLevel.critical || level == LogLevel.error) {
      _reportToExternalServices(level, message, category, data, error, stackTrace);
    }
  }

  /// تنسيق الرسالة
  static String _formatMessage(
    String emoji,
    String level,
    String category,
    String message,
    String timestamp,
    Map<String, dynamic>? data,
    Object? error,
  ) {
    final buffer = StringBuffer();

    buffer.writeln('┌─────────────────────────────────────────────────────────────');
    buffer.writeln('│ $emoji [$level] [$category] $timestamp');
    buffer.writeln('├─────────────────────────────────────────────────────────────');
    buffer.writeln('│ Message: $message');

    if (data != null && data.isNotEmpty) {
      buffer.writeln('├─────────────────────────────────────────────────────────────');
      buffer.writeln('│ Data:');
      data.forEach((key, value) {
        buffer.writeln('│   $key: $value');
      });
    }

    if (error != null) {
      buffer.writeln('├─────────────────────────────────────────────────────────────');
      buffer.writeln('│ Error: $error');
    }

    buffer.writeln('└─────────────────────────────────────────────────────────────');

    return buffer.toString();
  }

  /// الحصول على emoji للمستوى
  static String _getLevelEmoji(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '🐛';
      case LogLevel.info:
        return 'ℹ️';
      case LogLevel.warning:
        return '⚠️';
      case LogLevel.error:
        return '❌';
      case LogLevel.critical:
        return '🚨';
    }
  }

  /// الحصول على لون للمستوى
  static String _getLevelColor(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return '\x1B[37m'; // White
      case LogLevel.info:
        return '\x1B[36m'; // Cyan
      case LogLevel.warning:
        return '\x1B[33m'; // Yellow
      case LogLevel.error:
        return '\x1B[31m'; // Red
      case LogLevel.critical:
        return '\x1B[35m'; // Magenta
    }
  }

  /// الحصول على مستوى developer log
  static int _getDeveloperLogLevel(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
      case LogLevel.critical:
        return 1200;
    }
  }

  /// إرسال للخدمات الخارجية
  static void _reportToExternalServices(
    LogLevel level,
    String message,
    LogCategory category,
    Map<String, dynamic>? data,
    Object? error,
    StackTrace? stackTrace,
  ) {
    // يمكن إضافة تكامل مع:
    // - Firebase Crashlytics
    // - Sentry
    // - LogRocket
    // - Bugsnag

    if (kDebugMode) {
      print('📤 Reporting to external services: $level - $message');
    }
  }

  /// تسجيل بداية العملية
  static void startOperation(String operation, {LogCategory category = LogCategory.general}) {
    info('🚀 Starting: $operation', category: category);
  }

  /// تسجيل انتهاء العملية
  static void endOperation(String operation, {LogCategory category = LogCategory.general}) {
    info('✅ Completed: $operation', category: category);
  }

  /// تسجيل العملية مع الوقت
  static void timedOperation(String operation, Duration duration, {LogCategory category = LogCategory.general}) {
    info('⏱️ $operation completed in ${duration.inMilliseconds}ms', category: category);
  }

  /// تسجيل API call
  static void apiCall(String method, String url, {
    int? statusCode,
    Duration? duration,
    Map<String, dynamic>? requestData,
    Map<String, dynamic>? responseData,
    Object? error,
  }) {
    final data = <String, dynamic>{
      'method': method,
      'url': url,
      if (statusCode != null) 'statusCode': statusCode,
      if (duration != null) 'duration': '${duration.inMilliseconds}ms',
      if (requestData != null) 'request': requestData,
      if (responseData != null) 'response': responseData,
    };

    if (error != null) {
      AppLogger.error('API Call Failed: $method $url',
        category: LogCategory.api,
        data: data,
        error: error
      );
    } else if (statusCode != null && statusCode >= 400) {
      AppLogger.warning('API Call Warning: $method $url',
        category: LogCategory.api,
        data: data
      );
    } else {
      AppLogger.info('API Call: $method $url',
        category: LogCategory.api,
        data: data
      );
    }
  }

  /// تسجيل navigation
  static void navigation(String from, String to, {Map<String, dynamic>? arguments}) {
    info('🧭 Navigation: $from → $to',
      category: LogCategory.navigation,
      data: arguments != null ? {'arguments': arguments} : null,
    );
  }

  /// تسجيل database operation
  static void database(String operation, String table, {
    Map<String, dynamic>? data,
    Duration? duration,
    Object? error,
  }) {
    final logData = <String, dynamic>{
      'operation': operation,
      'table': table,
      if (duration != null) 'duration': '${duration.inMilliseconds}ms',
      if (data != null) 'data': data,
    };

    if (error != null) {
      AppLogger.error('Database Error: $operation on $table',
        category: LogCategory.database,
        data: logData,
        error: error
      );
    } else {
      AppLogger.info('Database: $operation on $table',
        category: LogCategory.database,
        data: logData
      );
    }
  }

  /// طباعة إحصائيات التطبيق
  static void printAppStatistics() {
    info('📊 App Statistics', category: LogCategory.general, data: {
      'timestamp': DateTime.now().toIso8601String(),
      'platform': kIsWeb ? 'Web' : 'Mobile',
      'debugMode': kDebugMode.toString(),
      'releaseMode': kReleaseMode.toString(),
    });
  }
}
