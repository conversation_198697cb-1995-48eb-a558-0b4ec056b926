class TimeSlotModel {
  final String id;
  final int dayOfWeek; // 0 = Sunday, 1 = Monday, etc.
  final String startTime;
  final String endTime;
  final int durationMinutes;
  final bool isActive;
  final int maxPatients;
  final DateTime createdAt;
  final DateTime updatedAt;

  TimeSlotModel({
    required this.id,
    required this.dayOfWeek,
    required this.startTime,
    required this.endTime,
    required this.durationMinutes,
    required this.isActive,
    required this.maxPatients,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TimeSlotModel.fromJson(Map<String, dynamic> json) {
    return TimeSlotModel(
      id: json['id'] ?? '',
      dayOfWeek: json['day_of_week'] ?? 0,
      startTime: json['start_time'] ?? '',
      endTime: json['end_time'] ?? '',
      durationMinutes: json['duration_minutes'] ?? 30,
      isActive: json['is_active'] ?? false,
      maxPatients: json['max_patients'] ?? 1,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'day_of_week': dayOfWeek,
      'start_time': startTime,
      'end_time': endTime,
      'duration_minutes': durationMinutes,
      'is_active': isActive,
      'max_patients': maxPatients,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get dayName {
    const days = [
      'الأحد',
      'الإثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
    ];
    return days[dayOfWeek];
  }

  String get displayTimeRange {
    return '$startTime - $endTime';
  }

  String get displayTimeRange12Hour {
    return '${_formatTime12Hour(startTime)} - ${_formatTime12Hour(endTime)}';
  }

  String get formattedDuration {
    if (durationMinutes >= 60) {
      final hours = durationMinutes ~/ 60;
      final minutes = durationMinutes % 60;
      if (minutes == 0) {
        return 'مدة الجلسة: $hours ${hours == 1 ? 'ساعة' : 'ساعات'}';
      } else {
        return 'مدة الجلسة: $hours ${hours == 1 ? 'ساعة' : 'ساعات'} و $minutes ${minutes == 1 ? 'دقيقة' : 'دقائق'}';
      }
    } else {
      return 'مدة الجلسة: $durationMinutes ${durationMinutes == 1 ? 'دقيقة' : 'دقائق'}';
    }
  }

  String _formatTime12Hour(String time24) {
    final parts = time24.split(':');
    final hour = int.parse(parts[0]);
    final minute = parts[1];

    if (hour == 0) {
      return '12:$minute ص';
    } else if (hour < 12) {
      return '$hour:$minute ص';
    } else if (hour == 12) {
      return '12:$minute م';
    } else {
      return '${hour - 12}:$minute م';
    }
  }

  List<String> generateTimeSlots() {
    final start = _parseTime(startTime);
    final end = _parseTime(endTime);
    final slots = <String>[];

    DateTime current = start;
    while (current.isBefore(end)) {
      slots.add(_formatTime(current));
      current = current.add(Duration(minutes: durationMinutes));
    }

    return slots;
  }

  DateTime _parseTime(String timeString) {
    final parts = timeString.split(':');
    final hour = int.parse(parts[0]);
    final minute = int.parse(parts[1]);
    return DateTime(2000, 1, 1, hour, minute);
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}
