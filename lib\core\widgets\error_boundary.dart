import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../constants/app_colors.dart';
import '../utils/app_logger.dart';
import '../utils/error_filter.dart';

/// Error Boundary لالتقاط الأخطاء غير المتوقعة
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget? fallback;
  final Function(Object error, StackTrace stackTrace)? onError;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.fallback,
    this.onError,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  void initState() {
    super.initState();

    // التقاط الأخطاء في Flutter Framework
    FlutterError.onError = (FlutterErrorDetails details) {
      // فحص ما إذا كان الخطأ يجب تجاهله
      if (ErrorFilter.shouldIgnoreError(details.exception)) {
        // معالجة خاصة لأخطاء الصور
        if (ErrorFilter.isImageLoadingError(details.exception)) {
          AppLogger.warning(
            'Image loading failed (ignored): ${details.exception.toString()}',
            category: LogCategory.general,
          );
        } else {
          ErrorFilter.logIgnoredError(details.exception);
        }
        return;
      }

      _handleError(details.exception, details.stack);

      // إرسال للـ Flutter framework أيضاً
      FlutterError.presentError(details);
    };
  }

  void _handleError(Object error, StackTrace? stackTrace) {
    // تجنب معالجة الأخطاء المتكررة
    if (_error != null) {
      AppLogger.warning(
        'Duplicate error detected - ignoring',
        category: LogCategory.general,
        error: error,
      );
      return;
    }

    // فحص ما إذا كان الخطأ يجب تجاهله
    if (ErrorFilter.shouldIgnoreError(error)) {
      // معالجة خاصة لأخطاء الصور
      if (ErrorFilter.isImageLoadingError(error)) {
        AppLogger.warning(
          'Image loading failed (ignored): ${error.toString()}',
          category: LogCategory.general,
        );
      } else {
        AppLogger.warning(
          'Layout/Overflow error detected - ignoring',
          category: LogCategory.ui,
          error: error,
        );
      }
      return;
    }

    AppLogger.critical(
      '🚨 Critical Error Caught by Error Boundary',
      category: LogCategory.general,
      data: {
        'error': ErrorFilter.cleanErrorMessage(error),
        'errorType': ErrorFilter.getErrorType(error),
        'widget': widget.child.runtimeType.toString(),
      },
      error: error,
      stackTrace: stackTrace,
    );

    // استخدام WidgetsBinding لضمان أن setState يتم في الوقت المناسب
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          _error = error;
          _stackTrace = stackTrace;
        });
      }
    });

    // استدعاء callback إذا كان موجود
    widget.onError?.call(error, stackTrace ?? StackTrace.current);
  }

  @override
  Widget build(BuildContext context) {
    // حماية إضافية من أخطاء البناء
    try {
      if (_error != null) {
        return widget.fallback ?? _buildDefaultErrorWidget();
      }

      return widget.child;
    } catch (e, stackTrace) {
      // في حالة حدوث خطأ في البناء نفسه
      AppLogger.error(
        'Error in ErrorBoundary build method',
        category: LogCategory.general,
        error: e,
        stackTrace: stackTrace,
      );

      return _buildMinimalErrorWidget();
    }
  }

  Widget _buildDefaultErrorWidget() {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // أيقونة الخطأ
              Icon(Icons.error_outline, size: 80.sp, color: AppColors.error),

              SizedBox(height: 24.h),

              // عنوان الخطأ
              Text(
                'حدث خطأ غير متوقع',
                style: TextStyle(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textDark,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 16.h),

              // وصف الخطأ
              Text(
                'نعتذر، حدث خطأ في التطبيق. يرجى المحاولة مرة أخرى.',
                style: TextStyle(fontSize: 16.sp, color: AppColors.textLight),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 32.h),

              // تفاصيل الخطأ (في وضع التطوير فقط)
              if (kDebugMode && _error != null) ...[
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: Colors.red.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تفاصيل الخطأ (وضع التطوير):',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.red.shade700,
                        ),
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        _error.toString(),
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: Colors.red.shade600,
                          fontFamily: 'monospace',
                        ),
                      ),
                      if (_stackTrace != null) ...[
                        SizedBox(height: 8.h),
                        Text(
                          'Stack Trace:',
                          style: TextStyle(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.bold,
                            color: Colors.red.shade700,
                          ),
                        ),
                        SizedBox(height: 4.h),
                        Text(
                          _stackTrace.toString().split('\n').take(5).join('\n'),
                          style: TextStyle(
                            fontSize: 10.sp,
                            color: Colors.red.shade600,
                            fontFamily: 'monospace',
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                SizedBox(height: 16.h),
              ],

              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _retry,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'إعادة المحاولة',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(width: 16.w),

                  Expanded(
                    child: OutlinedButton(
                      onPressed: _reportError,
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.primary,
                        side: BorderSide(color: AppColors.primary),
                        padding: EdgeInsets.symmetric(vertical: 16.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8.r),
                        ),
                      ),
                      child: Text(
                        'إبلاغ عن المشكلة',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),

              SizedBox(height: 16.h),

              // زر العودة للصفحة الرئيسية
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: _goHome,
                  child: Text(
                    'العودة للصفحة الرئيسية',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.textLight,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMinimalErrorWidget() {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red),
            SizedBox(height: 16),
            Text(
              'حدث خطأ في التطبيق',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'يرجى إعادة تشغيل التطبيق',
              style: TextStyle(fontSize: 14, color: Colors.black54),
            ),
          ],
        ),
      ),
    );
  }

  void _retry() {
    AppLogger.info(
      '🔄 User requested retry after error',
      category: LogCategory.ui,
    );

    setState(() {
      _error = null;
      _stackTrace = null;
    });
  }

  void _reportError() {
    AppLogger.info(
      '📧 User requested to report error',
      category: LogCategory.ui,
      data: {
        'error': _error.toString(),
        'errorType': _error.runtimeType.toString(),
      },
    );

    // يمكن إضافة تكامل مع خدمات الإبلاغ عن الأخطاء
    // مثل Firebase Crashlytics أو Sentry

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          'تم إرسال تقرير الخطأ. شكراً لك!',
          style: TextStyle(fontSize: 14.sp),
        ),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _goHome() {
    AppLogger.info(
      '🏠 User requested to go home after error',
      category: LogCategory.navigation,
    );

    Navigator.of(context).pushNamedAndRemoveUntil('/home', (route) => false);
  }
}

/// Widget مساعد لالتقاط الأخطاء في أجزاء محددة من التطبيق
class SafeWidget extends StatelessWidget {
  final Widget child;
  final Widget? fallback;
  final String? name;

  const SafeWidget({super.key, required this.child, this.fallback, this.name});

  @override
  Widget build(BuildContext context) {
    return ErrorBoundary(
      onError: (error, stackTrace) {
        AppLogger.error(
          '⚠️ Error in ${name ?? 'SafeWidget'}',
          category: LogCategory.ui,
          error: error,
          stackTrace: stackTrace,
        );
      },
      fallback: fallback ?? _buildFallback(),
      child: child,
    );
  }

  Widget _buildFallback() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.warning_amber, color: Colors.red.shade600, size: 24.sp),
          SizedBox(height: 8.h),
          Text(
            'حدث خطأ في هذا الجزء',
            style: TextStyle(fontSize: 14.sp, color: Colors.red.shade700),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// Extension لإضافة Error Boundary للـ Routes
extension SafeRoutes on MaterialApp {
  static Widget wrapWithErrorBoundary(Widget child) {
    return ErrorBoundary(
      onError: (error, stackTrace) {
        AppLogger.critical(
          '🚨 Route Error',
          category: LogCategory.navigation,
          error: error,
          stackTrace: stackTrace,
        );
      },
      child: child,
    );
  }
}
