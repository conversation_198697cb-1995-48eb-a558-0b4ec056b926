class MedicalInfoModel {
  final String id;
  final String patientId;
  final String infoType;
  final String name;
  final String? description;
  final String? dosage;
  final String? frequency;
  final DateTime? startDate;
  final DateTime? endDate;
  final String? severity;
  final String? notes;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MedicalInfoModel({
    required this.id,
    required this.patientId,
    required this.infoType,
    required this.name,
    this.description,
    this.dosage,
    this.frequency,
    this.startDate,
    this.endDate,
    this.severity,
    this.notes,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MedicalInfoModel.fromMap(Map<String, dynamic> map) {
    return MedicalInfoModel(
      id: map['id'] as String,
      patientId: map['patient_id'] as String,
      infoType: map['info_type'] as String,
      name: map['name'] as String,
      description: map['description'] as String?,
      dosage: map['dosage'] as String?,
      frequency: map['frequency'] as String?,
      startDate: map['start_date'] != null ? DateTime.parse(map['start_date']) : null,
      endDate: map['end_date'] != null ? DateTime.parse(map['end_date']) : null,
      severity: map['severity'] as String?,
      notes: map['notes'] as String?,
      isActive: map['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'patient_id': patientId,
      'info_type': infoType,
      'name': name,
      'description': description,
      'dosage': dosage,
      'frequency': frequency,
      'start_date': startDate?.toIso8601String().split('T')[0],
      'end_date': endDate?.toIso8601String().split('T')[0],
      'severity': severity,
      'notes': notes,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  MedicalInfoModel copyWith({
    String? id,
    String? patientId,
    String? infoType,
    String? name,
    String? description,
    String? dosage,
    String? frequency,
    DateTime? startDate,
    DateTime? endDate,
    String? severity,
    String? notes,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MedicalInfoModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      infoType: infoType ?? this.infoType,
      name: name ?? this.name,
      description: description ?? this.description,
      dosage: dosage ?? this.dosage,
      frequency: frequency ?? this.frequency,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      severity: severity ?? this.severity,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MedicalInfoModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MedicalInfoModel(id: $id, name: $name, infoType: $infoType, isActive: $isActive)';
  }

  // Helper methods
  String get displayInfoType {
    switch (infoType.toLowerCase()) {
      case 'medication':
        return 'دواء';
      case 'supplement':
        return 'مكمل غذائي';
      case 'allergy':
        return 'حساسية';
      case 'condition':
        return 'حالة طبية';
      case 'surgery':
        return 'عملية جراحية';
      case 'treatment':
        return 'علاج';
      default:
        return infoType;
    }
  }

  String get displaySeverity {
    if (severity == null) return 'غير محدد';
    switch (severity!.toLowerCase()) {
      case 'low':
        return 'خفيف';
      case 'medium':
        return 'متوسط';
      case 'high':
        return 'شديد';
      case 'critical':
        return 'حرج';
      default:
        return severity!;
    }
  }

  String get displayStartDate => startDate != null ? '${startDate!.day}/${startDate!.month}/${startDate!.year}' : 'غير محدد';
  String get displayEndDate => endDate != null ? '${endDate!.day}/${endDate!.month}/${endDate!.year}' : 'مستمر';
  
  String get statusText => isActive ? 'نشط' : 'غير نشط';
  
  bool get isOngoing => endDate == null || endDate!.isAfter(DateTime.now());
  
  Duration? get duration {
    if (startDate == null) return null;
    final endDateToUse = endDate ?? DateTime.now();
    return endDateToUse.difference(startDate!);
  }
  
  String get displayDuration {
    final dur = duration;
    if (dur == null) return 'غير محدد';
    
    final days = dur.inDays;
    if (days < 30) return '$days يوم';
    if (days < 365) return '${(days / 30).round()} شهر';
    return '${(days / 365).round()} سنة';
  }

  // Get color based on info type
  String get colorCode {
    switch (infoType.toLowerCase()) {
      case 'medication':
        return '#4CAF50'; // Green
      case 'supplement':
        return '#2196F3'; // Blue
      case 'allergy':
        return '#F44336'; // Red
      case 'condition':
        return '#FF9800'; // Orange
      case 'surgery':
        return '#9C27B0'; // Purple
      case 'treatment':
        return '#00BCD4'; // Cyan
      default:
        return '#757575'; // Grey
    }
  }
}
