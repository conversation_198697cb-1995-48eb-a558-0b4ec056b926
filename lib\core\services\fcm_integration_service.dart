import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../utils/app_logger.dart';
import 'fcm_token_service.dart';
import 'notification_service.dart';

/// خدمة تكامل FCM مع النظام
class FCMIntegrationService {
  static final FCMIntegrationService _instance =
      FCMIntegrationService._internal();
  factory FCMIntegrationService() => _instance;
  FCMIntegrationService._internal();

  final FCMTokenService _tokenService = FCMTokenService();
  final NotificationService _notificationService = NotificationService();
  final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  SupabaseClient get _supabase => Supabase.instance.client;

  bool _isInitialized = false;

  /// تهيئة خدمة FCM
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      AppLogger.info(
        'Initializing FCM Integration Service',
        category: LogCategory.general,
      );

      // طلب أذونات الإشعارات
      await _requestPermissions();

      // إعداد listeners للإشعارات
      await _setupNotificationListeners();

      // إعداد token refresh listener
      _setupTokenRefreshListener();

      // حفظ Token الحالي إذا كان المستخدم مسجل دخول
      await _saveCurrentTokenIfLoggedIn();

      _isInitialized = true;

      AppLogger.info(
        'FCM Integration Service initialized successfully',
        category: LogCategory.general,
      );
    } catch (e) {
      AppLogger.error(
        'Error initializing FCM Integration Service',
        category: LogCategory.general,
        error: e,
      );

      // تعيين كمهيأ حتى لو فشل، لتجنب توقف التطبيق
      _isInitialized = true;

      // إعادة رمي الخطأ ليتم التعامل معه في main
      rethrow;
    }
  }

  /// طلب أذونات الإشعارات
  Future<void> _requestPermissions() async {
    try {
      final settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      AppLogger.info(
        'FCM Permission status: ${settings.authorizationStatus}',
        category: LogCategory.general,
      );
    } catch (e) {
      AppLogger.error(
        'Error requesting FCM permissions',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// إعداد listeners للإشعارات
  Future<void> _setupNotificationListeners() async {
    try {
      // عند استلام إشعار والتطبيق مفتوح
      FirebaseMessaging.onMessage.listen((RemoteMessage message) {
        AppLogger.info(
          'Received foreground message: ${message.messageId}',
          category: LogCategory.general,
        );
        _notificationService.handleForegroundMessage(message);
      });

      // عند النقر على إشعار والتطبيق في الخلفية
      FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
        AppLogger.info(
          'Message clicked: ${message.messageId}',
          category: LogCategory.general,
        );
        _notificationService.handleNotificationTap(message);
      });

      // التحقق من إشعار تم النقر عليه عند فتح التطبيق
      final initialMessage = await _messaging.getInitialMessage();
      if (initialMessage != null) {
        AppLogger.info(
          'App opened from notification: ${initialMessage.messageId}',
          category: LogCategory.general,
        );
        _notificationService.handleNotificationTap(initialMessage);
      }
    } catch (e) {
      AppLogger.error(
        'Error setting up notification listeners',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// إعداد token refresh listener
  void _setupTokenRefreshListener() {
    try {
      _messaging.onTokenRefresh.listen((String newToken) {
        AppLogger.info('FCM Token refreshed', category: LogCategory.general);
        _tokenService.refreshToken(newToken);
      });
    } catch (e) {
      AppLogger.error(
        'Error setting up token refresh listener',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// حفظ Token الحالي إذا كان المستخدم مسجل دخول
  Future<void> _saveCurrentTokenIfLoggedIn() async {
    try {
      // التحقق من أن Supabase مهيأ
      try {
        _supabase.auth.currentUser;
      } catch (e) {
        AppLogger.warning(
          'Supabase not accessible, skipping token save',
          category: LogCategory.general,
        );
        return;
      }

      final currentUser = _supabase.auth.currentUser;
      if (currentUser != null) {
        final success = await _tokenService.saveToken(userId: currentUser.id);
        if (success) {
          AppLogger.info(
            'Current FCM token saved for logged in user',
            category: LogCategory.general,
          );
        }
      } else {
        AppLogger.info(
          'No current user, skipping token save',
          category: LogCategory.general,
        );
      }
    } catch (e) {
      AppLogger.error(
        'Error saving current token',
        category: LogCategory.general,
        error: e,
      );
      // لا نعيد رمي الخطأ هنا لتجنب توقف التطبيق
    }
  }

  /// حفظ Token عند تسجيل الدخول
  Future<bool> onUserLogin(String userId) async {
    try {
      AppLogger.info(
        'User logged in, saving FCM token',
        category: LogCategory.general,
      );

      final success = await _tokenService.saveToken(userId: userId);

      if (success) {
        AppLogger.info(
          'FCM token saved successfully for user: $userId',
          category: LogCategory.general,
        );
      }

      return success;
    } catch (e) {
      AppLogger.error(
        'Error saving token on user login',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// حذف Token عند تسجيل الخروج
  Future<bool> onUserLogout(String userId) async {
    try {
      AppLogger.info(
        'User logged out, deleting FCM token',
        category: LogCategory.general,
      );

      final success = await _tokenService.deleteToken(userId: userId);

      if (success) {
        AppLogger.info(
          'FCM token deleted successfully for user: $userId',
          category: LogCategory.general,
        );
      }

      return success;
    } catch (e) {
      AppLogger.error(
        'Error deleting token on user logout',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// التحقق من حالة Token
  Future<bool> checkTokenStatus() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) return false;

      final cachedToken = await _tokenService.getCachedToken();
      if (cachedToken == null) return false;

      return await _tokenService.isTokenValid(cachedToken);
    } catch (e) {
      AppLogger.error(
        'Error checking token status',
        category: LogCategory.general,
        error: e,
      );
      return false;
    }
  }

  /// تنظيف دوري للـ tokens
  Future<void> performPeriodicCleanup() async {
    try {
      AppLogger.info(
        'Performing periodic FCM tokens cleanup',
        category: LogCategory.general,
      );

      await _tokenService.cleanupInactiveTokens();

      AppLogger.info(
        'Periodic cleanup completed',
        category: LogCategory.general,
      );
    } catch (e) {
      AppLogger.error(
        'Error during periodic cleanup',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// الحصول على إحصائيات Tokens للمستخدم الحالي
  Future<Map<String, int>> getCurrentUserTokenStats() async {
    try {
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        return {'total': 0, 'active': 0, 'inactive': 0};
      }

      return await _tokenService.getTokenStats(currentUser.id);
    } catch (e) {
      AppLogger.error(
        'Error getting current user token stats',
        category: LogCategory.general,
        error: e,
      );
      return {'total': 0, 'active': 0, 'inactive': 0};
    }
  }

  /// طباعة FCM Token للتطوير
  Future<void> printCurrentToken() async {
    try {
      final token = await _messaging.getToken();
      if (token != null) {
        AppLogger.info(
          '🔑 Current FCM Token: $token',
          category: LogCategory.general,
        );
      } else {
        AppLogger.warning(
          'No FCM Token available',
          category: LogCategory.general,
        );
      }
    } catch (e) {
      AppLogger.error(
        'Error printing current token',
        category: LogCategory.general,
        error: e,
      );
    }
  }

  /// إعادة تهيئة الخدمة
  Future<void> reinitialize() async {
    _isInitialized = false;
    await initialize();
  }

  /// التحقق من حالة التهيئة
  bool get isInitialized => _isInitialized;
}
