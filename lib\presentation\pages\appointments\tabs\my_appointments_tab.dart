import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/constants/app_colors.dart';
import '../../../../core/constants/app_strings.dart';
import '../../../../core/utils/helpers.dart';
import '../../../../data/models/appointment_model.dart';
import '../../../../data/services/appointments_service.dart';
import '../../../widgets/common/empty_state_widget.dart';
import '../../../widgets/common/filter_button.dart';

/// تبويب مواعيدي
class MyAppointmentsTab extends StatefulWidget {
  final String patientId;

  const MyAppointmentsTab({super.key, required this.patientId});

  @override
  State<MyAppointmentsTab> createState() => _MyAppointmentsTabState();
}

enum AppointmentFilter { all, today, upcoming, past }

class _MyAppointmentsTabState extends State<MyAppointmentsTab> {
  final AppointmentsService _appointmentsService = AppointmentsService();
  bool _isLoading = true;
  List<AppointmentModel> _appointments = [];
  AppointmentFilter _selectedFilter = AppointmentFilter.all;

  @override
  void initState() {
    super.initState();
    _loadAppointments();
  }

  Future<void> _loadAppointments() async {
    print(
      '🔄 [MyAppointmentsTab] Loading appointments for patient: ${widget.patientId}',
    );
    setState(() {
      _isLoading = true;
    });

    try {
      final appointments = await _appointmentsService.getPatientAppointments(
        widget.patientId,
      );
      print('✅ [MyAppointmentsTab] Loaded ${appointments.length} appointments');
      setState(() {
        _appointments = appointments;
        _isLoading = false;
      });
    } catch (e) {
      print('❌ [MyAppointmentsTab] Error loading appointments: $e');
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        Helpers.showErrorSnackBar(context, 'فشل في تحميل المواعيد: $e');
      }
    }
  }

  Future<void> _cancelAppointment(AppointmentModel appointment) async {
    final confirmed = await Helpers.showConfirmDialog(
      context,
      title: 'إلغاء الموعد',
      content: 'هل أنت متأكد من إلغاء هذا الموعد؟',
      confirmText: 'إلغاء الموعد',
      cancelText: 'تراجع',
    );

    if (confirmed == true) {
      try {
        await _appointmentsService.cancelAppointment(appointment.id);

        // تحديث قائمة المواعيد من قاعدة البيانات
        await _loadAppointments();

        if (mounted) {
          Helpers.showSuccessSnackBar(context, AppStrings.bookingCancelled);
        }
      } catch (e) {
        if (mounted) {
          Helpers.showErrorSnackBar(context, 'فشل في إلغاء الموعد: $e');
        }
      }
    }
  }

  List<AppointmentModel> get _filteredAppointments {
    switch (_selectedFilter) {
      case AppointmentFilter.all:
        return _appointments;
      case AppointmentFilter.today:
        return _appointments
            .where((appointment) => appointment.isToday)
            .toList();
      case AppointmentFilter.upcoming:
        return _appointments
            .where((appointment) => appointment.isFuture)
            .toList();
      case AppointmentFilter.past:
        return _appointments
            .where((appointment) => appointment.isPast)
            .toList();
    }
  }

  String _getFilterTitle(AppointmentFilter filter) {
    switch (filter) {
      case AppointmentFilter.all:
        return 'الكل';
      case AppointmentFilter.today:
        return 'اليوم';
      case AppointmentFilter.upcoming:
        return 'القادمة';
      case AppointmentFilter.past:
        return 'السابقة';
    }
  }

  String _getArabicDayName(DateTime date) {
    const arabicDays = [
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return arabicDays[date.weekday - 1];
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Center(
        child: SizedBox(
          width: 24.w,
          height: 24.h,
          child: const CircularProgressIndicator(strokeWidth: 2),
        ),
      );
    }

    return Column(
      children: [_buildFilterTabs(), Expanded(child: _buildAppointmentsList())],
    );
  }

  Widget _buildFilterTabs() {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.border),
      ),
      child: Row(
        children:
            AppointmentFilter.values.map((filter) {
              final isSelected = _selectedFilter == filter;
              return TabFilterButton(
                text: _getFilterTitle(filter),
                isSelected: isSelected,
                onTap: () {
                  setState(() {
                    _selectedFilter = filter;
                  });
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildAppointmentsList() {
    final filteredAppointments = _filteredAppointments;

    if (filteredAppointments.isEmpty) {
      String emptyMessage;
      switch (_selectedFilter) {
        case AppointmentFilter.all:
          emptyMessage = 'لا توجد مواعيد';
          break;
        case AppointmentFilter.today:
          emptyMessage = 'لا توجد مواعيد اليوم';
          break;
        case AppointmentFilter.upcoming:
          emptyMessage = 'لا توجد مواعيد قادمة';
          break;
        case AppointmentFilter.past:
          emptyMessage = 'لا توجد مواعيد سابقة';
          break;
      }

      return EmptyStateWidget(
        title: emptyMessage,
        subtitle: 'يمكنك حجز موعد جديد من التبويب الأول',
        icon: Icons.calendar_today,
      );
    }

    return RefreshIndicator(
      onRefresh: _loadAppointments,
      color: AppColors.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(16.w),
        itemCount: filteredAppointments.length,
        itemBuilder: (context, index) {
          final appointment = filteredAppointments[index];
          return _buildAppointmentCard(appointment);
        },
      ),
    );
  }

  Widget _buildAppointmentCard(AppointmentModel appointment) {
    return Card(
      margin: EdgeInsets.only(bottom: 16.h),
      child: Padding(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  color: AppColors.primary,
                  size: 20.sp,
                ),
                SizedBox(width: 8.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getArabicDayName(appointment.appointmentDate),
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: AppColors.primary,
                      ),
                    ),
                    Text(
                      appointment.displayDate,
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimary,
                      ),
                    ),
                  ],
                ),
                const Spacer(),
                _buildStatusChip(appointment.displayStatus),
              ],
            ),

            SizedBox(height: 12.h),

            // الوقت والمدة
            Row(
              children: [
                Icon(
                  Icons.access_time,
                  color: AppColors.textSecondary,
                  size: 16.sp,
                ),
                SizedBox(width: 8.w),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      appointment.displayTime,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                    if (appointment.timeSlot != null) ...[
                      SizedBox(height: 2.h),
                      Text(
                        appointment.timeSlot!.formattedDuration,
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: AppColors.textLight,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),

            // الملاحظات
            if (appointment.notes != null) ...[
              SizedBox(height: 8.h),
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Icon(Icons.note, color: AppColors.textSecondary, size: 16.sp),
                  SizedBox(width: 8.w),
                  Expanded(
                    child: Text(
                      appointment.notes!,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: AppColors.textSecondary,
                      ),
                    ),
                  ),
                ],
              ),
            ],

            // الوقت المتبقي (للمواعيد القادمة فقط)
            if (appointment.isFuture &&
                appointment.timeUntilAppointment.isNotEmpty) ...[
              SizedBox(height: 8.h),
              Row(
                children: [
                  Icon(Icons.schedule, color: AppColors.primary, size: 16.sp),
                  SizedBox(width: 8.w),
                  Text(
                    appointment.timeUntilAppointment,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: AppColors.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],

            // الأزرار
            if (appointment.canCancel) ...[
              SizedBox(height: 16.h),
              Row(
                children: [
                  const Spacer(),
                  TextButton(
                    onPressed: () => _cancelAppointment(appointment),
                    child: Text(
                      'إلغاء الموعد',
                      style: TextStyle(
                        color: AppColors.error,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(String status) {
    Color backgroundColor;
    Color textColor;
    String text = status;

    switch (status.toLowerCase()) {
      case 'استشارة':
      case 'موعد قادم':
      case 'scheduled':
        backgroundColor = AppColors.primary.withValues(alpha: 0.1);
        textColor = AppColors.primary;
        text = 'موعد قادم';
        break;
      case 'كشف':
      case 'فحص واستشارة':
      case 'booked':
        backgroundColor = AppColors.warning.withValues(alpha: 0.1);
        textColor = AppColors.warning;
        text = 'فحص واستشارة';
        break;
      case 'مكتمل':
      case 'completed':
        backgroundColor = AppColors.success.withValues(alpha: 0.1);
        textColor = AppColors.success;
        text = 'مكتمل';
        break;
      case 'ملغي':
      case 'cancelled':
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        text = 'ملغي';
        break;
      case 'لم يحضر':
      case 'no_show':
        backgroundColor = AppColors.error.withValues(alpha: 0.1);
        textColor = AppColors.error;
        text = 'لم يحضر';
        break;
      default:
        backgroundColor = AppColors.border;
        textColor = AppColors.textSecondary;
        text = status;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 12.sp,
          fontWeight: FontWeight.w500,
          color: textColor,
        ),
      ),
    );
  }
}
