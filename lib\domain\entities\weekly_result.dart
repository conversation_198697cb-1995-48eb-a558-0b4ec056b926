import 'package:equatable/equatable.dart';

/// كيان النتائج الأسبوعية
class WeeklyResult extends Equatable {
  final String id;
  final String? patientId;
  final double? weight;
  final double? bodyFat;
  final double? visceralFat;
  final double? waterPercentage;
  final double? muscleMass;
  final DateTime recordedDate;
  final String? notes;
  final DateTime? createdAt;

  const WeeklyResult({
    required this.id,
    this.patientId,
    this.weight,
    this.bodyFat,
    this.visceralFat,
    this.waterPercentage,
    this.muscleMass,
    required this.recordedDate,
    this.notes,
    this.createdAt,
  });

  @override
  List<Object?> get props => [
    id,
    patientId,
    weight,
    bodyFat,
    visceralFat,
    waterPercentage,
    muscleMass,
    recordedDate,
    notes,
    createdAt,
  ];

  /// التحقق من اكتمال البيانات
  bool get isComplete {
    return weight != null &&
        bodyFat != null &&
        visceralFat != null &&
        waterPercentage != null &&
        muscleMass != null;
  }

  /// التحقق من وجود ملاحظات
  bool get hasNotes => notes != null && notes!.isNotEmpty;

  /// حساب مؤشر كتلة الجسم (يحتاج الطول من بيانات المريض)
  double? calculateBMI(double? height) {
    if (weight == null || height == null) return null;
    final heightInMeters = height / 100;
    return weight! / (heightInMeters * heightInMeters);
  }

  /// تقييم نسبة الدهون
  String? get bodyFatCategory {
    if (bodyFat == null) return null;

    // هذه القيم تقريبية ويجب تخصيصها حسب الجنس والعمر
    if (bodyFat! < 10) {
      return 'منخفض جداً';
    } else if (bodyFat! < 15) {
      return 'منخفض';
    } else if (bodyFat! < 25) {
      return 'طبيعي';
    } else if (bodyFat! < 35) {
      return 'مرتفع';
    } else {
      return 'مرتفع جداً';
    }
  }

  /// تقييم نسبة الماء
  String? get waterPercentageCategory {
    if (waterPercentage == null) return null;

    if (waterPercentage! < 45) {
      return 'منخفض';
    } else if (waterPercentage! < 65) {
      return 'طبيعي';
    } else {
      return 'مرتفع';
    }
  }

  /// تقييم الدهون الحشوية
  String? get visceralFatCategory {
    if (visceralFat == null) return null;

    if (visceralFat! < 10) {
      return 'طبيعي';
    } else if (visceralFat! < 15) {
      return 'مرتفع قليلاً';
    } else {
      return 'مرتفع';
    }
  }

  /// حساب التغيير من النتيجة السابقة
  WeeklyResultComparison? compareWith(WeeklyResult? previous) {
    if (previous == null) return null;

    return WeeklyResultComparison(
      weightChange:
          weight != null && previous.weight != null
              ? weight! - previous.weight!
              : null,
      bodyFatChange:
          bodyFat != null && previous.bodyFat != null
              ? bodyFat! - previous.bodyFat!
              : null,
      visceralFatChange:
          visceralFat != null && previous.visceralFat != null
              ? visceralFat! - previous.visceralFat!
              : null,
      waterPercentageChange:
          waterPercentage != null && previous.waterPercentage != null
              ? waterPercentage! - previous.waterPercentage!
              : null,
      muscleMassChange:
          muscleMass != null && previous.muscleMass != null
              ? muscleMass! - previous.muscleMass!
              : null,
    );
  }
}

/// فئة مقارنة النتائج الأسبوعية
class WeeklyResultComparison extends Equatable {
  final double? weightChange;
  final double? bodyFatChange;
  final double? visceralFatChange;
  final double? waterPercentageChange;
  final double? muscleMassChange;

  const WeeklyResultComparison({
    this.weightChange,
    this.bodyFatChange,
    this.visceralFatChange,
    this.waterPercentageChange,
    this.muscleMassChange,
  });

  @override
  List<Object?> get props => [
    weightChange,
    bodyFatChange,
    visceralFatChange,
    waterPercentageChange,
    muscleMassChange,
  ];

  /// التحقق من وجود تحسن في الوزن
  bool get hasWeightImprovement {
    return weightChange != null && weightChange! < 0; // نقص في الوزن
  }

  /// التحقق من وجود تحسن في الدهون
  bool get hasBodyFatImprovement {
    return bodyFatChange != null && bodyFatChange! < 0; // نقص في الدهون
  }

  /// التحقق من وجود تحسن في العضلات
  bool get hasMuscleImprovement {
    return muscleMassChange != null &&
        muscleMassChange! > 0; // زيادة في العضلات
  }

  /// التحقق من وجود تحسن عام
  bool get hasOverallImprovement {
    return hasWeightImprovement ||
        hasBodyFatImprovement ||
        hasMuscleImprovement;
  }
}
