import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../core/services/notification_service.dart';
import '../../../core/utils/app_logger.dart';
import 'notifications_events.dart';
import 'notifications_states.dart';

/// BLoC إدارة الإشعارات
class NotificationsBloc extends Bloc<NotificationsEvent, NotificationsState> {
  final NotificationService _notificationService;

  NotificationsBloc({NotificationService? notificationService})
    : _notificationService = notificationService ?? NotificationService(),
      super(const NotificationsInitial()) {
    on<LoadNotifications>(_onLoadNotifications);
    on<AddNotification>(_onAddNotification);
    on<MarkNotificationAsRead>(_onMarkNotificationAsRead);
    on<MarkAllNotificationsAsRead>(_onMarkAllNotificationsAsRead);
    on<ClearAllNotifications>(_onClearAllNotifications);
    on<UpdateUnreadCount>(_onUpdateUnreadCount);
    on<PrintFCMToken>(_onPrintFCMToken);

    // إعداد callbacks للخدمة
    _setupNotificationCallbacks();
  }

  /// إعداد callbacks للخدمة
  void _setupNotificationCallbacks() {
    _notificationService.onNotificationReceived = (notification) {
      add(AddNotification(notification));
    };
  }

  /// تحميل الإشعارات
  Future<void> _onLoadNotifications(
    LoadNotifications event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      emit(const NotificationsLoading());

      final notifications = await _notificationService.getStoredNotifications();
      final unreadCount = await _notificationService.getUnreadCount();

      emit(
        NotificationsLoaded(
          notifications: notifications,
          unreadCount: unreadCount,
        ),
      );

      AppLogger.info(
        '📱 Notifications loaded: ${notifications.length} total, $unreadCount unread',
      );
    } catch (e) {
      AppLogger.error('Failed to load notifications', error: e);
      emit(NotificationsError('فشل في تحميل الإشعارات: ${e.toString()}'));
    }
  }

  /// إضافة إشعار جديد
  Future<void> _onAddNotification(
    AddNotification event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is NotificationsLoaded) {
        final updatedNotifications = [
          event.notification,
          ...currentState.notifications,
        ];
        final unreadCount = await _notificationService.getUnreadCount();

        emit(
          currentState.copyWith(
            notifications: updatedNotifications,
            unreadCount: unreadCount,
          ),
        );

        AppLogger.info(
          '📱 New notification added: ${event.notification.title}',
        );
      } else {
        // إذا لم تكن الحالة محملة، قم بتحميل الإشعارات
        add(const LoadNotifications());
      }
    } catch (e) {
      AppLogger.error('Failed to add notification', error: e);
      emit(NotificationsError('فشل في إضافة الإشعار: ${e.toString()}'));
    }
  }

  /// تحديد إشعار كمقروء
  Future<void> _onMarkNotificationAsRead(
    MarkNotificationAsRead event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is NotificationsLoaded) {
        // تحديث الإشعار محلياً
        final updatedNotifications =
            currentState.notifications.map((notification) {
              if (notification.id == event.notificationId) {
                return notification.copyWith(isRead: true);
              }
              return notification;
            }).toList();

        // تحديث في الخدمة
        await _notificationService.markAsRead(event.notificationId);
        final unreadCount = await _notificationService.getUnreadCount();

        emit(
          currentState.copyWith(
            notifications: updatedNotifications,
            unreadCount: unreadCount,
          ),
        );

        AppLogger.info(
          '✅ Notification marked as read: ${event.notificationId}',
        );
      }
    } catch (e) {
      AppLogger.error('Failed to mark notification as read', error: e);
      emit(NotificationsError('فشل في تحديد الإشعار كمقروء: ${e.toString()}'));
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<void> _onMarkAllNotificationsAsRead(
    MarkAllNotificationsAsRead event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is NotificationsLoaded) {
        // تحديث جميع الإشعارات محلياً
        final updatedNotifications =
            currentState.notifications
                .map((notification) => notification.copyWith(isRead: true))
                .toList();

        // تحديث في الخدمة
        await _notificationService.markAllAsRead();

        emit(
          NotificationsOperationSuccess(
            message: 'تم تحديد جميع الإشعارات كمقروءة',
            notifications: updatedNotifications,
            unreadCount: 0,
          ),
        );

        AppLogger.info('✅ All notifications marked as read');
      }
    } catch (e) {
      AppLogger.error('Failed to mark all notifications as read', error: e);
      emit(
        NotificationsError(
          'فشل في تحديد جميع الإشعارات كمقروءة: ${e.toString()}',
        ),
      );
    }
  }

  /// مسح جميع الإشعارات
  Future<void> _onClearAllNotifications(
    ClearAllNotifications event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      await _notificationService.clearAllNotifications();

      emit(
        const NotificationsOperationSuccess(
          message: 'تم مسح جميع الإشعارات',
          notifications: [],
          unreadCount: 0,
        ),
      );

      AppLogger.info('🗑️ All notifications cleared');
    } catch (e) {
      AppLogger.error('Failed to clear all notifications', error: e);
      emit(NotificationsError('فشل في مسح الإشعارات: ${e.toString()}'));
    }
  }

  /// تحديث عداد الإشعارات غير المقروءة
  Future<void> _onUpdateUnreadCount(
    UpdateUnreadCount event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      final currentState = state;
      if (currentState is NotificationsLoaded) {
        final unreadCount = await _notificationService.getUnreadCount();
        emit(currentState.copyWith(unreadCount: unreadCount));
      }
    } catch (e) {
      AppLogger.error('Failed to update unread count', error: e);
    }
  }

  /// طباعة FCM Token
  Future<void> _onPrintFCMToken(
    PrintFCMToken event,
    Emitter<NotificationsState> emit,
  ) async {
    try {
      await _notificationService.printFCMToken();

      final currentState = state;
      if (currentState is NotificationsLoaded) {
        emit(
          NotificationsOperationSuccess(
            message: 'تم طباعة رمز الجهاز في وحدة التحكم',
            notifications: currentState.notifications,
            unreadCount: currentState.unreadCount,
          ),
        );
      }
    } catch (e) {
      AppLogger.error('Failed to print FCM token', error: e);
      emit(NotificationsError('فشل في طباعة رمز الجهاز: ${e.toString()}'));
    }
  }
}
