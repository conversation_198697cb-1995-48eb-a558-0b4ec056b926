name: iihc_user
description: "IIHC - مركز مستشفى إربد الإسلامي للسمع والنطق والسلوك"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 3.2.3+3

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Icons
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  lottie: ^3.3.1
  animated_text_kit: ^4.2.2

  # State Management
  flutter_bloc: ^8.1.6
  bloc: ^8.1.4
  equatable: ^2.0.5

  # Network & API
  dio: ^5.7.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0

  # Database
  supabase_flutter: ^2.8.0

  # Responsive Design
  flutter_screenutil: ^5.9.3
  responsive_framework: ^1.5.1

  # Navigation
  go_router: ^14.6.1

  # Local Storage
  shared_preferences: ^2.3.3

  # Image Handling
  cached_network_image: ^3.4.1
  image_picker: ^1.1.2

  # Sharing
  share_plus: ^11.0.0

  # Notifications & Reminders
  flutter_local_notifications: ^17.2.3
  timezone: ^0.9.4

  # Date & Time
  intl: ^0.20.2

  # Validation
  formz: ^0.7.0

  # Loading & Shimmer
  shimmer: ^3.0.0
  flutter_spinkit: ^5.2.1

  # Charts for medical data
  fl_chart: ^1.0.0

  # HTTP for OpenAI
  http: ^1.2.2

  # Error Tracking
  sentry_flutter: ^8.11.0

  # Utils
  logger: ^2.4.0
  uuid: ^4.5.1
  flutter_staggered_grid_view: ^0.7.0
  url_launcher: ^6.3.1
  flutter_pdfview: ^1.3.2
  path_provider: ^2.1.4
  firebase_core: ^3.14.0
  firebase_messaging: ^15.2.7
  device_info_plus: ^10.1.2
  package_info_plus: ^8.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4

  # Code Generation

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/animations/

  # Fonts (تم تعطيلها مؤقتاً - يمكن إضافة ملفات الخطوط لاحقاً)
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
  #       - asset: assets/fonts/Cairo-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Cairo-Medium.ttf
  #         weight: 500

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/images/logo.jpeg"
  min_sdk_android: 21
  web:
    generate: true
    image_path: "assets/images/logo.jpeg"
  windows:
    generate: true
    image_path: "assets/images/logo.jpeg"
    icon_size: 48
  macos:
    generate: true
    image_path: "assets/images/logo.jpeg"
  # إعدادات إضافية للأندرويد
  adaptive_icon_background: "#04938c"
  adaptive_icon_foreground: "assets/images/logo.jpeg"