class HomeworkModel {
  final String id;
  final String patientId;
  final String title;
  final String? description;
  final String? instructions;
  final String? pdfUrl;
  final DateTime assignedDate;
  final String status;
  final String? notes;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  HomeworkModel({
    required this.id,
    required this.patientId,
    required this.title,
    this.description,
    this.instructions,
    this.pdfUrl,
    required this.assignedDate,
    this.status = 'assigned',
    this.notes,
    this.createdAt,
    this.updatedAt,
  });

  factory HomeworkModel.fromJson(Map<String, dynamic> json) {
    return HomeworkModel(
      id: json['id'] ?? '',
      patientId: json['patient_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'],
      instructions: json['instructions'],
      pdfUrl: json['pdf_url'],
      assignedDate: json['assigned_date'] != null 
          ? DateTime.parse(json['assigned_date']) 
          : DateTime.now(),
      status: json['status'] ?? 'assigned',
      notes: json['notes'],
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'patient_id': patientId,
      'title': title,
      'description': description,
      'instructions': instructions,
      'pdf_url': pdfUrl,
      'assigned_date': assignedDate.toIso8601String().split('T')[0],
      'status': status,
      'notes': notes,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  HomeworkModel copyWith({
    String? id,
    String? patientId,
    String? title,
    String? description,
    String? instructions,
    String? pdfUrl,
    DateTime? assignedDate,
    String? status,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return HomeworkModel(
      id: id ?? this.id,
      patientId: patientId ?? this.patientId,
      title: title ?? this.title,
      description: description ?? this.description,
      instructions: instructions ?? this.instructions,
      pdfUrl: pdfUrl ?? this.pdfUrl,
      assignedDate: assignedDate ?? this.assignedDate,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get hasDescription => description != null && description!.isNotEmpty;
  bool get hasInstructions => instructions != null && instructions!.isNotEmpty;
  bool get hasPdf => pdfUrl != null && pdfUrl!.isNotEmpty;
  bool get hasNotes => notes != null && notes!.isNotEmpty;
  
  String get displayTitle => title.isNotEmpty ? title : 'واجب غير محدد';
  
  String get formattedAssignedDate {
    return '${assignedDate.day}/${assignedDate.month}/${assignedDate.year}';
  }
  
  String get formattedCreatedDate {
    if (createdAt == null) return 'غير محدد';
    return '${createdAt!.day}/${createdAt!.month}/${createdAt!.year}';
  }
  
  // Status helpers
  bool get isAssigned => status.toLowerCase() == 'assigned';
  bool get isInProgress => status.toLowerCase() == 'in_progress';
  bool get isCompleted => status.toLowerCase() == 'completed';
  bool get isOverdue => status.toLowerCase() == 'overdue';
  bool get isCancelled => status.toLowerCase() == 'cancelled';
  
  String get statusText {
    switch (status.toLowerCase()) {
      case 'assigned':
        return 'مُكلف';
      case 'in_progress':
        return 'قيد التنفيذ';
      case 'completed':
        return 'مكتمل';
      case 'overdue':
        return 'متأخر';
      case 'cancelled':
        return 'ملغي';
      default:
        return 'غير محدد';
    }
  }
  
  // Date helpers
  bool get isToday {
    final now = DateTime.now();
    return assignedDate.year == now.year &&
           assignedDate.month == now.month &&
           assignedDate.day == now.day;
  }
  
  bool get isPast => assignedDate.isBefore(DateTime.now());
  bool get isFuture => assignedDate.isAfter(DateTime.now());
  
  int get daysFromNow {
    final now = DateTime.now();
    return assignedDate.difference(now).inDays;
  }
  
  String get timeFromNow {
    if (isToday) return 'اليوم';
    if (isPast) {
      final days = DateTime.now().difference(assignedDate).inDays;
      return 'منذ $days ${days == 1 ? 'يوم' : 'أيام'}';
    } else {
      final days = daysFromNow;
      return 'خلال $days ${days == 1 ? 'يوم' : 'أيام'}';
    }
  }
  
  // Priority based on status and date
  int get priority {
    if (isOverdue) return 4; // Highest priority
    if (isAssigned && isPast) return 3;
    if (isInProgress) return 2;
    if (isAssigned) return 1;
    return 0; // Completed or cancelled
  }
  
  @override
  String toString() {
    return 'HomeworkModel(id: $id, title: $title, status: $status, date: $formattedAssignedDate)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is HomeworkModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
