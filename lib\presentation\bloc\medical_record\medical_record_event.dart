import 'package:equatable/equatable.dart';

abstract class MedicalRecordEvent extends Equatable {
  const MedicalRecordEvent();

  @override
  List<Object?> get props => [];
}

class LoadMedicalRecord extends MedicalRecordEvent {
  final String patientId;

  const LoadMedicalRecord(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadMedicalRecordByAuthId extends MedicalRecordEvent {
  final String authId;

  const LoadMedicalRecordByAuthId(this.authId);

  @override
  List<Object?> get props => [authId];
}

class RefreshMedicalRecord extends MedicalRecordEvent {
  final String patientId;

  const RefreshMedicalRecord(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadPatientInfo extends MedicalRecordEvent {
  final String patientId;

  const LoadPatientInfo(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadWeeklyResults extends MedicalRecordEvent {
  final String patientId;

  const LoadWeeklyResults(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadMedicalInfo extends MedicalRecordEvent {
  final String patientId;

  const LoadMedicalInfo(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadMedicalInfoByType extends MedicalRecordEvent {
  final String patientId;
  final String infoType;

  const LoadMedicalInfoByType(this.patientId, this.infoType);

  @override
  List<Object?> get props => [patientId, infoType];
}

class LoadLabTests extends MedicalRecordEvent {
  final String patientId;

  const LoadLabTests(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadReminders extends MedicalRecordEvent {
  final String patientId;

  const LoadReminders(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadActiveReminders extends MedicalRecordEvent {
  final String patientId;

  const LoadActiveReminders(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadRecentLabTests extends MedicalRecordEvent {
  final String patientId;

  const LoadRecentLabTests(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class LoadMedicalSummary extends MedicalRecordEvent {
  final String patientId;

  const LoadMedicalSummary(this.patientId);

  @override
  List<Object?> get props => [patientId];
}

class ChangeTab extends MedicalRecordEvent {
  final int tabIndex;

  const ChangeTab(this.tabIndex);

  @override
  List<Object?> get props => [tabIndex];
}

class ClearMedicalRecord extends MedicalRecordEvent {
  const ClearMedicalRecord();
}
